package com.wisematch.test;

import com.wisematch.AppRun;
import com.wisematch.modules.chat.controller.AiTalentPortraitController;
import com.wisematch.modules.chat.controller.AiTalentReportController;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.concurrent.ThreadPoolExecutor;

@SpringBootTest(
    classes = AppRun.class)
class DemoApplicationTests {

    @Resource
    private AiTalentPortraitController aiTalentPortraitController;

    @Resource
    private AiTalentReportController aiTalentReportController;





	@Test
	void contextLoads() throws InterruptedException {

        String[] chats = {
                "Chat_fe874a75d2f6411ca4f1",
                "Chat_588a376ab7c145568b59",
                "Chat_07ff8cab764b429aa2e8",
                "Chat_6667bd73a41a4d0e8fe8",
                "Chat_3a23c2351770416fa2f6",
                "Chat_3626f6bb66f44233bd28",
                "Chat_c3e51b1fe14647bd9d7b",
                "Chat_9c36370f55e3439cb8fc",
                "Chat_7e3f8e9829c14724bc87",
                "Chat_84735afaf51f4b71a81e",
                "Chat_c58d43e63d294d26bc84",
                "Chat_a3c3b78b44bc4d9fa786",
                "Chat_91155c3c278548f38877",
                "Chat_c7d9c7f6d27e4598a19f",
                "Chat_0dbaaaaf4ebb425fa2d1",
                "Chat_510f84afc0b04575895d",
                "Chat_8c04536b2d8e41519f2c",
                "Chat_7ae3c1ec868544d08202",
                "Chat_8c634436ba7e4810af01",
                "Chat_e723f09b29494f4c9065",
        };



        //生成一个新的线程
        ThreadPoolExecutor executor = new ThreadPoolExecutor(15, 15, 20000, java.util.concurrent.TimeUnit.MILLISECONDS,
                new java.util.concurrent.ArrayBlockingQueue<>(10000), new ThreadPoolExecutor.CallerRunsPolicy());




        for (String chat : chats) {
            executor.execute(() -> {
                long start = System.currentTimeMillis();
                System.out.println("report start: " + chat);
                System.out.println(aiTalentReportController.report(chat));
                System.out.println("report cost: " + (System.currentTimeMillis() - start) + " ms");
                System.out.println("portrait start: " + chat);
                System.out.println(aiTalentPortraitController.generate(chat));
                System.out.println("portrait cost: " + (System.currentTimeMillis() - start) + " ms");
            });

        }

        Thread.sleep(5000000);
	}

}
