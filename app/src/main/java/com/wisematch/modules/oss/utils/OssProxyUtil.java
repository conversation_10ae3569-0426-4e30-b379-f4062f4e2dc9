package com.wisematch.modules.oss.utils;

import jakarta.servlet.http.HttpServletResponse;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;

import static com.google.common.io.Files.getFileExtension;


public class OssProxyUtil {

    public void proxyDocx(@RequestParam("url") String ossUrl, HttpServletResponse response) throws IOException {
        String googleDocsViewerUrl = "https://docs.google.com/viewer?url=" + URLEncoder.encode(ossUrl, "UTF-8");
        response.sendRedirect(googleDocsViewerUrl);
    }

    // 代理HTML请求
    @GetMapping("/html")
    public void proxyHtml(@RequestParam("url") String ossUrl,
                          HttpServletResponse response) throws IOException {
        proxyRequest(ossUrl, response, "text/html");
    }

    // 代理CSS请求
    @GetMapping("/css")
    public void proxyCss(@RequestParam("url") String ossUrl,
                         HttpServletResponse response) throws IOException {
        proxyRequest(ossUrl, response, "text/css");
    }

    // 代理JS请求
    @GetMapping("/js")
    public void proxyJs(@RequestParam("url") String ossUrl,
                        HttpServletResponse response) throws IOException {
        proxyRequest(ossUrl, response, "application/javascript");
    }

    // 代理PDF请求
    @GetMapping("/preview")
    public void proxyPdf(@RequestParam("url") String ossUrl,
                         HttpServletResponse response) throws IOException {
        proxyRequest(ossUrl, response, "application/pdf");
    }
    public static void proxyFile(@RequestParam("url") String ossUrl,
                          HttpServletResponse response) throws IOException {
        // 判断文件类型
        String fileExtension = getFileExtension(ossUrl);

        if (fileExtension.equals("pdf")) {
            proxyRequest(ossUrl, response, "application/pdf");
        } else if (isImage(fileExtension)) {
            proxyRequest(ossUrl, response, "image/*");
        } else if (isWordDocument(fileExtension)) {
            // 这里使用 PDF 预览 Word 文档，可以使用类似 `docx-to-pdf` 工具或者将其返回给浏览器查看
            proxyRequest(ossUrl, response, "application/msword");  // 实际上这里需要调用转换工具转换为 PDF
        } else {
            response.sendError(HttpServletResponse.SC_UNSUPPORTED_MEDIA_TYPE, "不支持的文件类型");
        }
    }



    // 判断是否是图片文件
    private static boolean isImage(String extension) {
        return FileUtils.examPicture(extension);
    }

    // 判断是否是 Word 文档
    private static boolean isWordDocument(String extension) {
        return extension.equals("doc") || extension.equals("docx");
    }

    // 通用代理方法
    public static void proxyRequest(String ossUrl, HttpServletResponse response, String contentType)
            throws IOException {
        Request request = new Request.Builder()
                .url(ossUrl)
                .build();

        Response ossResponse = null;
        InputStream inputStream = null;
        OutputStream outputStream = null;
        OkHttpClient client = new OkHttpClient();
        try {
            ossResponse = client.newCall(request).execute();
            if (!ossResponse.isSuccessful()) {
                response.sendError(ossResponse.code(), "OSS请求失败");
                return;
            }

            // 设置响应头
            response.setContentType(contentType);
            response.setHeader("Content-Disposition", "inline");

            // 获取响应体流
            ResponseBody body = ossResponse.body();
            if (body != null) {
                inputStream = body.byteStream();
                outputStream = response.getOutputStream();

                // 复制流内容
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }
        } finally {
            // 手动关闭资源
            if (inputStream != null) inputStream.close();
            if (outputStream != null) outputStream.close();
            if (ossResponse != null) ossResponse.body().close();
        }
    }
}
