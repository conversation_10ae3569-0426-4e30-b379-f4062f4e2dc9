package com.wisematch.modules.oss.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;



/**
 * 文件上传
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2017-03-25 12:13:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysOss extends Model<SysOss> implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//
	private Long id;
	//URL地址
	private String url;
	//创建时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createDate;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;

	private String fileName;

	private String fileType;

	private String userId;

	private Integer isDel;

}
