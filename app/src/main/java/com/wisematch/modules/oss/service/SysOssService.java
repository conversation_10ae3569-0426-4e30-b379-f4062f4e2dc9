package com.wisematch.modules.oss.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.oss.entity.SysOss;
import com.wisematch.modules.oss.model.SysOssDTO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * 文件上传
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2017-03-25 12:13:26
 */
public interface SysOssService extends IService<SysOss> {

    public SysOssDTO fileUpload(MultipartFile file,SysOssDTO sysOssDTO);

    public SysOss saveOssObj(SysOssDTO sysOssDTO);

    public SysOss ossUpload(MultipartFile file,String userId, String fileType);

    public SysOss uploadAsy(MultipartFile file,String userId, String fileType);

    public void fileUploadAsy(MultipartFile file,SysOssDTO sysOssDTO);

    public InputStream getOssInputStream(String bucketName, String fileName);
    public String downloadFromOssUrl(String ossUrl, String userid) throws IOException;
    Page<SysOss> pageQuery(SysOssDTO sysOss);

    int logicDel(String id);

    String generateUnique(SysOssDTO sysOssDTO);

    void batchLogicDelete(List<Long> list);
}
