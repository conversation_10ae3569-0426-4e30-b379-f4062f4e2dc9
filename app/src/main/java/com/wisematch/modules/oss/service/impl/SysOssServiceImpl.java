package com.wisematch.modules.oss.service.impl;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.utils.ThreadPoolUtil;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.common.utils.UUIDutils;
import com.wisematch.modules.oss.entity.SysOss;
import com.wisematch.modules.oss.mapper.SysOssMapper;
import com.wisematch.modules.oss.model.SysOssDTO;
import com.wisematch.modules.oss.service.SysOssService;
import com.wisematch.modules.oss.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.wisematch.modules.chat.enums.WiserConstant.DELETED;
import static com.wisematch.modules.chat.enums.WiserConstant.NOT_DELETE;
import static com.wisematch.modules.oss.utils.FileUtils.*;


@Service
@Slf4j
public class SysOssServiceImpl extends ServiceImpl<SysOssMapper,SysOss> implements SysOssService {


    @Autowired
    private OSS ossClient;
    @Value("${aliyun.oss.bucket-name}")
    private String bucketName;
    @Override
    public SysOssDTO fileUpload(MultipartFile file,SysOssDTO sysOssDTO) {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }
        // 上传文件到阿里云OSS
        String normalizedFolder = FileUtils.getOssResumeFolder(sysOssDTO.getUserId());
//            String normalizedFolder = folder.endsWith("/") ? folder : folder + "/";
        createFolderIfNotExists(ossClient, normalizedFolder);
        String extension = getExtension(sysOssDTO.getFileName());
        String uuid = UUIDutils.generate();

        String targetSrc = FileUtils.contactFilePath(normalizedFolder, uuid,extension);

        String fileUrl = getOssUrl(bucketName,uuid+extension,sysOssDTO.getUserId());
        sysOssDTO.setUrl(fileUrl);
        try {
            ossClient.putObject(new PutObjectRequest(bucketName, targetSrc, file.getInputStream()));
            log.info("文件上传成功：" + sysOssDTO.getFileName());
        }catch (ClientException ce) {
            log.error("连接服务问题:" + ce);
        }  catch (IOException e) {
            log.error("文件上传失败：" + e);
        }
        return sysOssDTO;
    }

    @Override
    public void fileUploadAsy(MultipartFile file,SysOssDTO sysOssDTO) {
        ThreadPoolUtil.execute(() -> {
            fileUpload(file,sysOssDTO);
            updateById(sysOssDTO);
        });
    }

    private void createFolderIfNotExists(OSS ossClient, String folderPath) {
        // OSS文件夹本质是以/结尾的空文件
        if (!ossClient.doesObjectExist(bucketName, folderPath)) {
            // 创建空内容的对象，key以/结尾表示文件夹
            ossClient.putObject(bucketName, folderPath, new ByteArrayInputStream(new byte[0]));
        }
    }

    @Override
    public SysOss saveOssObj(SysOssDTO sysOssDTO){
        SysOss ossEntity = new SysOss();
        ossEntity.setCreateDate(new Date());
        ossEntity.setFileName(sysOssDTO.getFileName());
        ossEntity.setUserId(sysOssDTO.getUserId());
        ossEntity.setFileType(sysOssDTO.getFileType());
        // 3. 返回完整的OSS访问路径
//        String fileUrl = getOssUrl(bucketName,fileName,userId);
        ossEntity.setUrl(sysOssDTO.getUrl());
        this.save(ossEntity);
        return ossEntity;
    }

    @Override
    public SysOss ossUpload(MultipartFile file,String userId, String fileType) {
        String originalFilename = file.getOriginalFilename();

        SysOssDTO sysOssDTO = new SysOssDTO();
        sysOssDTO.setFileType(fileType);
        sysOssDTO.setUserId(userId);
        sysOssDTO.setIsDel(NOT_DELETE);
        sysOssDTO.setFileName(originalFilename);

        String fileName = this.generateUnique(sysOssDTO);
        sysOssDTO.setFileName(fileName);

        SysOssDTO sysOss = fileUpload(file,sysOssDTO);

        return saveOssObj(sysOss);
    }



    @Override
    public SysOss uploadAsy(MultipartFile file,String userId, String fileType) {
        SysOssDTO sysOssDTO = new SysOssDTO();
        sysOssDTO.setFileType(fileType);
        sysOssDTO.setUserId(userId);
        sysOssDTO.setIsDel(NOT_DELETE);
        String originalFilename = file.getOriginalFilename();
        sysOssDTO.setFileName(originalFilename);

        String fileName = this.generateUnique(sysOssDTO);
        sysOssDTO.setFileName(fileName);

        SysOss sysOss = saveOssObj(sysOssDTO);

        fileUploadAsy(file,sysOssDTO);
        return sysOss;
    }


    @Override
    public InputStream getOssInputStream(String bucketName, String fileName) {
        OSSObject ossObject = ossClient.getObject(bucketName, fileName);
        return ossObject.getObjectContent();
    }

    @Override
    public Page<SysOss> pageQuery(SysOssDTO sysOss) {


        Page<SysOss> page = new Page<>(sysOss.getPageNum(), sysOss.getPageSize());
        QueryWrapper<SysOss> wrapper = new QueryWrapper<SysOss>();
        if (StringUtils.isNotBlank(sysOss.getFileName())) {
            wrapper.lambda().like(SysOss::getFileName, sysOss.getFileName());
        }
        if (StringUtils.isNotBlank(sysOss.getFileType())) {
            wrapper.lambda().eq(SysOss::getFileType, sysOss.getFileType());
        }
        if (!Objects.isNull(sysOss.getIsDel())) {
            wrapper.lambda().eq(SysOss::getIsDel, sysOss.getIsDel());
        }
        wrapper.lambda().eq(SysOss::getUserId, UserInfoUtils.getCurrentUserId());
        return this.page(page, wrapper);
    }

    @Override
    public int logicDel(String id) {
        return this.baseMapper.update(new UpdateWrapper<SysOss>().lambda().eq(SysOss::getId, id).set(SysOss::getIsDel, DELETED).set(SysOss::getUpdateTime,new Date()));
    }

    @Override
    public String generateUnique(SysOssDTO sysOssDTO) {
        List<SysOss> sysOssList = this.baseMapper.selectList(new QueryWrapper<SysOss>().lambda()
                .eq(SysOss::getIsDel, NOT_DELETE).eq(SysOss::getFileType, sysOssDTO.getFileType())
                .eq(SysOss::getUserId, sysOssDTO.getUserId()));
        List<String> filenames = sysOssList.stream().map(SysOss::getFileName).map(FileUtils::removeFileExtension).toList();
        String extension = getExtension(sysOssDTO.getFileName());
        String filename = removeFileExtension(sysOssDTO.getFileName());
        return FileUtils.returnNameDistinct(filenames, filename)+extension;
    }

    @Override
    public void batchLogicDelete(List<Long> list) {
        this.baseMapper.batchLogicDelete(list);
    }



    /**
     * 根据OSS文件URL获取文件流
     * @param ossFileUrl 完整的OSS文件访问URL
     * @return 文件输入流（需自行关闭）
     */
    public InputStream getFileFromOssUrl(String ossFileUrl) {
        try {
            // 1. 从URL中提取Bucket和ObjectKey
            String[] parts = parseOssUrl(ossFileUrl);
            String bucketName = parts[0];
            String objectKey = parts[1];

            // 2. 获取OSS文件对象
            OSSObject ossObject = ossClient.getObject(bucketName, objectKey);
            return ossObject.getObjectContent();

        } catch (Exception e) {
            throw new RuntimeException("从OSS获取文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析OSS URL
     * @param ossFileUrl 示例: https://my-bucket.oss-cn-beijing.aliyuncs.com/path/to/file.txt
     * @return [bucketName, objectKey]
     */
    private String[] parseOssUrl(String ossFileUrl) {
        // 移除协议头和可能的查询参数
        String cleanUrl = ossFileUrl.replace("https://", "").replace("http://", "").split("\\?")[0];

        // 分割得到bucket和剩余部分
        String[] domainParts = cleanUrl.split("\\.", 2);
        String bucketName = domainParts[0];
        String endpointAndPath = domainParts[1];

        // 提取objectKey（移除endpoint部分）
        String objectKey = endpointAndPath.substring(endpointAndPath.indexOf("/") + 1);

        return new String[]{bucketName, objectKey};
    }

    /**
     * 安全关闭OSSObject流的方法
     */
    public void safeCloseOssObject(OSSObject ossObject) {
        try {
            if (ossObject != null) {
                ossObject.close();
            }
        } catch (Exception e) {
            // 静默关闭
        }
    }

    @Override
    public String downloadFromOssUrl(String ossUrl, String userid) throws IOException {
        OSSObject ossObject = null;

        Path tempDir = Files.createTempDirectory(userid);
        Path tempFile = tempDir.resolve(userid);
        try (InputStream is = this.getFileFromOssUrl(ossUrl);
             FileOutputStream fos = new FileOutputStream(tempFile.toString())) {

            IOUtils.copy(is, fos); // 使用commons-io工具类
            log.info("文件下载完成");
        } finally {
            this.safeCloseOssObject(ossObject);
        }
        return tempFile.toString();
    }

}
