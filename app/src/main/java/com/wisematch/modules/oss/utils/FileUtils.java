package com.wisematch.modules.oss.utils;

import com.wisematch.common.utils.UUIDutils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.List;
import java.util.Set;

public class FileUtils {


    // 最大文件大小 50MB（字节）
    private static final long MAX_FILE_SIZE = 50 * 1024 * 1024;

    // 允许的扩展名
    public static final Set<String> DOCUMENT_EXTENSIONS = Set.of("doc", "docx", "pdf");

    public static final Set<String> PICTURE_EXTENSIONS = Set.of("jpg", "jpeg", "png", "gif", "bmp", "tiff");

    /**
     * 校验上传文件是否合法（格式 + 大小）
     * @param file 上传的文件
     * @throws IllegalArgumentException 如果不符合要求
     */
    public static void validateResume(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        String filename = file.getOriginalFilename();
        if (filename == null || !filename.contains(".")) {
            throw new IllegalArgumentException("文件名无效");
        }

        // 获取扩展名
        String ext = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();

        boolean isAllowed = DOCUMENT_EXTENSIONS.contains(ext) || PICTURE_EXTENSIONS.contains(ext);

        if (!isAllowed) {
            throw new IllegalArgumentException("仅支持上传 Word (.doc/.docx) 、 PDF 和 图片");
        }

        // 校验文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new IllegalArgumentException("文件大小不能超过 50MB");
        }
    }

    public static String returnNameDistinct(List<String> list, String newData) {
        // 如果数据不存在，直接添加
        if (!list.contains(newData)) {
            return newData;
        }

        // 如果数据已存在，添加后缀 _1, _2...
        int suffix = 1;
        String newName;
        do {
            newName = newData + "_" + suffix;
            suffix++;
        } while (list.contains(newName));

        return newName;
    }


    public static String generateFilePath(String folder, String fileName){
        String extension = getExtension(fileName);
        String uuid = UUIDutils.generate();
        return folder + uuid + File.separator + extension;
    }
    public static String contactFilePath(String folder, String fileName, String extension){
        return folder + fileName + extension;
    }


    public static Boolean examFileType(String originalFilename){
        String extension = getExtensionNoPoint(originalFilename);
        return DOCUMENT_EXTENSIONS.contains(extension);
    }


    public static Boolean examPictureByName(String originalFilename){
        String extension = getExtensionNoPoint(originalFilename);
        return PICTURE_EXTENSIONS.contains(extension);
    }

    public static Boolean examDocumentByName(String originalFilename){
        String extension = getExtensionNoPoint(originalFilename);
        return DOCUMENT_EXTENSIONS.contains(extension);
    }



    public static Boolean examPicture(String extension){
        return PICTURE_EXTENSIONS.contains(extension);
    }

    public static Boolean examDocument(String extension){
        return DOCUMENT_EXTENSIONS.contains(extension);
    }


    @Value("${aliyun.oss.bucket-name}")
    private String bucketName;
    public static String getOssUrl(String bucketName, String fileName, String userId){
        return "https://" + bucketName + ".oss-cn-hangzhou.aliyuncs.com/" +"user/resume/"+userId+"/"+ fileName;
    }

    public static String getOssResumeFolder(String userId){
        return "user/resume/"+userId+"/";
    }

    public static String getExtension(String originalFilename){
        return originalFilename.substring(originalFilename.lastIndexOf('.')).toLowerCase();
    }

    public static String getExtensionNoPoint(String originalFilename){
        return originalFilename.substring(originalFilename.lastIndexOf('.')+1).toLowerCase();
    }

    public static String removeFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return fileName;
        }

        // 处理路径分隔符（兼容Windows/Unix）
        String separator = File.separator;
        int lastSeparatorIndex = fileName.lastIndexOf(separator);
        String nameWithPath = (lastSeparatorIndex >= 0) ? fileName.substring(0, lastSeparatorIndex + 1) : "";
        String baseName = (lastSeparatorIndex >= 0) ? fileName.substring(lastSeparatorIndex + 1) : fileName;

        // 去除后缀
        int dotIndex = baseName.lastIndexOf('.');
        if (dotIndex > 0) {  // 确保不是隐藏文件（如 ".gitignore"）
            baseName = baseName.substring(0, dotIndex);
        }

        return nameWithPath + baseName;
    }




    public static Path saveInputStreamToTempFile(InputStream inputStream, String fileName, String userId) throws IOException {
        // 1. 获取系统临时目录并创建临时文件
        Path tempDir = Files.createTempDirectory(userId);
        Path tempFile = tempDir.resolve(fileName);

        // 2. 将 InputStream 写入临时文件
        Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING);

        // 3. 返回临时文件路径（可后续操作）
        return tempFile;
    }


    public static String getOssTmpByFileId(String fileId){

        return "fileid://"+fileId;
    }



}
