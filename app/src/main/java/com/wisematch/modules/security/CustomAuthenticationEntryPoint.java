package com.wisematch.modules.security;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class CustomAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private static final Logger log = LoggerFactory.getLogger(CustomAuthenticationEntryPoint.class);

    private boolean isClientAbortException(Throwable ex) {
        if (ex == null) return false;
        String msg = ex.getMessage();
        return msg != null && (msg.contains("Broken pipe") || msg.contains("Connection reset by peer"));
    }

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException {
        if (isClientAbortException(authException.getCause())) {
            // 客户端断开，忽略异常，不写响应
            log.warn("客户端断开连接，忽略Spring Security异常");
            return;
        }

        // 其它异常正常处理
        response.sendError(HttpServletResponse.SC_UNAUTHORIZED, authException.getMessage());
    }
}

