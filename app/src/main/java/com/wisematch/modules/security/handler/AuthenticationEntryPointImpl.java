package com.wisematch.modules.security.handler;

import com.alibaba.fastjson.JSON;
import com.wisematch.common.utils.R;
import com.wisematch.modules.sys.utils.ServletUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * 认证失败处理类 返回未授权
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class AuthenticationEntryPointImpl implements AuthenticationEntryPoint, Serializable
{
    private static final long serialVersionUID = -8970718410437077606L;

    private boolean isClientAbortException(Throwable ex) {
        if (ex == null) return false;
        String msg = ex.getMessage();
        return msg != null && (msg.contains("Broken pipe") || msg.contains("Connection reset by peer"));
    }

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e) {
        if (isClientAbortException(e.getCause())) {
            // 客户端断开，忽略异常，不写响应
            log.warn("客户端断开连接，忽略Spring Security异常");
            return;
        }

        log.error("认证异常",e);
        int code = HttpStatus.UNAUTHORIZED.value();
        String msg = String.format("认证失败，无法访问系统资源", request.getRequestURI());
        ServletUtils.renderString(response, JSON.toJSONString(R.error(code, msg)));
    }
}
