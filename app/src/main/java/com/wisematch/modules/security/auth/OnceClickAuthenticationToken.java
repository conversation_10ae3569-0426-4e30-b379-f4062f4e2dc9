package com.wisematch.modules.security.auth;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

public class OnceClickAuthenticationToken extends AbstractAuthenticationToken {

    private final Object principal;

    public OnceClickAuthenticationToken(String phone) {
        super(null);
        this.principal = phone;
        setAuthenticated(false);
    }

    public OnceClickAuthenticationToken(Object principal, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        super.setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getPrincipal() {
        return this.principal;
    }
}

