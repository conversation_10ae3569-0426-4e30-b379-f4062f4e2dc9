package com.wisematch.modules.security.auth;

import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.model.LoginUser;
import com.wisematch.modules.sys.service.SysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

@Component
public class OnceClickAuthenticationProvider implements AuthenticationProvider {

    private static final Logger log = LoggerFactory.getLogger(OnceClickAuthenticationProvider.class);
    @Autowired
    private SysUserService sysUserService;

    @Autowired
    CommonLoginHandler commonLoginHandler;
    @Autowired
    private CustomUserDetailsService customUserDetailsService;
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String phone = authentication.getPrincipal().toString();

        SysUser user = commonLoginHandler.phoneLogin(phone);
        LoginUser loginUser = customUserDetailsService.createLoginUser(user);

        return new OnceClickAuthenticationToken(loginUser, loginUser.getAuthorities());
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return OnceClickAuthenticationToken.class.isAssignableFrom(authentication);
    }
}

