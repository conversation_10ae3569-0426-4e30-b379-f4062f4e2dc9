package com.wisematch.modules.security.auth;

import com.wisematch.config.PermissionConfig;
import com.wisematch.modules.sys.service.SysPermissionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

/**
 * 用户角色
 */
@Slf4j
@Service
public class PermissionsService {

    @Resource
    private PermissionConfig permissionConfig;

    @Resource
    private SysPermissionService sysPermissionService;

    public Set<String> getUserPermissions(String userId) {
        Set<String> permissions = new HashSet<>();
//        if (userId.equals(Constant.SUPER_ADMIN)) {
//            permissions = permissionConfig.getPermissionEntities().stream().map(PermissionEntity::getEnglishName).collect(Collectors.toSet());
//        } else {
//            permissions = sysPermissionService.getPermissionListByUserId(userId).stream().map(SysPermission::getEnglishName).collect(Collectors.toSet());
//        }
        return permissions;
    }




}