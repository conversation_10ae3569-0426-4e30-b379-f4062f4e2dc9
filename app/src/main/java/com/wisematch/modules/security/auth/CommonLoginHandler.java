package com.wisematch.modules.security.auth;

import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.modules.chat.enums.WiserConstant;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.model.LoginUser;
import com.wisematch.modules.sys.service.SysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Service
public class CommonLoginHandler {
    @Autowired
    private SysUserService sysUserService;

    private static final Logger log = LoggerFactory.getLogger(CommonLoginHandler.class);

    public void statusVerify(LoginUser sysUser) throws UsernameNotFoundException, DisabledException, AccountExpiredException{
        if (WiserConstant.DELETED.equals(sysUser.getIsDel()))
        {
            throw new RRException(RRExceptionEnum.USER_DELETED.getMessage());
        }
        if (WiserConstant.DISABLE.equals(sysUser.getStatus()))
        {
            throw new RRException(RRExceptionEnum.ADMIN_ACCOUNT_DISABLED_ERROR.getMessage());
        }
    }
    public void statusVerify(SysUser sysUser) throws UsernameNotFoundException, DisabledException, AccountExpiredException{
        if (WiserConstant.DELETED.equals(sysUser.getIsDel()))
        {
            throw new DisabledException(RRExceptionEnum.USER_DELETED.getMessage());
        }
        if (WiserConstant.DISABLE.equals(sysUser.getStatus()))
        {
            throw new AccountExpiredException(RRExceptionEnum.ADMIN_ACCOUNT_DISABLED_ERROR.getMessage());
        }
    }

    public SysUser phoneLogin(String phone){
        // 根据手机号查找用户
        SysUser user = sysUserService.createUser(phone);
//        this.statusVerify(user);
        return user;
    }

}
