package com.wisematch.modules.security.auth;

import com.alibaba.excel.util.StringUtils;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.modules.sms.SmsUtils;
import com.wisematch.modules.sms.SmsVerifyCodeManager;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.model.LoginUser;
import com.wisematch.modules.sys.service.SysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

@Component
public class PhoneAuthenticationProvider implements AuthenticationProvider {

    private static final Logger log = LoggerFactory.getLogger(PhoneAuthenticationProvider.class);
    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SmsVerifyCodeManager smsVerifyCodeManager;
    @Autowired
    CommonLoginHandler commonLoginHandler;

    @Autowired
    private CustomUserDetailsService customUserDetailsService;
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String phone = authentication.getPrincipal().toString();

        if(!SmsUtils.isChinesePhone(phone)){
            throw new RRException(RRExceptionEnum.PHONE_NOT_EXIST_ERROR);
        }

        SysUser user = commonLoginHandler.phoneLogin(phone);

        String code = authentication.getCredentials().toString();
        if (StringUtils.isBlank(code)){
            throw new RRException(RRExceptionEnum.APP_LOGIN_CODE_EMPTY);
        }

        boolean flag = smsVerifyCodeManager.validateUserVerifyCode(phone, code);
        if (!flag){
            throw new RRException(RRExceptionEnum.APP_LOGIN_CODE_ERROR);
        }

        LoginUser loginUser = customUserDetailsService.createLoginUser(user);

        return new PhoneAuthenticationToken(loginUser, loginUser.getAuthorities(),"PHONE");
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return PhoneAuthenticationToken.class.isAssignableFrom(authentication);
    }
}

