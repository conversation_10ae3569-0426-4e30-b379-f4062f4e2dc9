package com.wisematch.modules.security.config;

import com.wisematch.modules.sys.annotation.Anonymous;
import com.wisematch.modules.sys.model.LoginUser;
import com.wisematch.modules.sys.service.TokenService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerMapping;

import java.io.IOException;

/**
 * token过滤器 验证token有效性
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {
    @Autowired
    private TokenService tokenService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        boolean isAnonymous = checkAnonymousAnnotation(request);
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (null != loginUser){
            //&& null != UserInfoUtils.getAuthentication()
            tokenService.verifyToken(loginUser);
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
        } else if (!isAnonymous) {
//            logPrint(request);
        }
        chain.doFilter(request, response);
    }

    private void logPrint(HttpServletRequest request){
        // ===== 打印请求信息 =====
        String uri = request.getRequestURI();
        String method = request.getMethod();
        String userAgent = request.getHeader("user-agent");
        String ip = getClientIp(request);
        log.info("请求路径: {} | 请求方式: {} | 请求IP: {}", uri, method, ip);
    }

    /**
     * 获取客户端 IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 如果有多个 IP（通过代理），取第一个
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        return ip;
    }


    /**
     * 检查当前请求对应的方法或类是否加了 @Anonymous 注解
     */
    private boolean checkAnonymousAnnotation(HttpServletRequest request) {
        try {
            HandlerMapping handlerMapping = request.getAttribute(HandlerMapping.BEST_MATCHING_HANDLER_ATTRIBUTE) != null
                    ? (HandlerMapping) request.getAttribute(HandlerMapping.BEST_MATCHING_HANDLER_ATTRIBUTE)
                    : null;
            if (handlerMapping == null) {
                return false;
            }
            HandlerMethod handlerMethod = (HandlerMethod) handlerMapping;
            return handlerMethod.getMethod().isAnnotationPresent(Anonymous.class)
                    || handlerMethod.getBeanType().isAnnotationPresent(Anonymous.class);
        } catch (Exception e) {
            return false;
        }
    }
}
