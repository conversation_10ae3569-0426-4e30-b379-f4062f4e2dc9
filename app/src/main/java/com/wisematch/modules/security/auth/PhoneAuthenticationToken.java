package com.wisematch.modules.security.auth;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

public class PhoneAuthenticationToken extends AbstractAuthenticationToken {

    private final Object principal;
    private final Object credentials;

    public PhoneAuthenticationToken(String phone, String credentials) {
        super(null);
        this.credentials = credentials;
        this.principal = phone;
        setAuthenticated(false);
    }

    public PhoneAuthenticationToken(Object principal, Collection<? extends GrantedAuthority> authorities, Object credentials) {
        super(authorities);
        this.principal = principal;
        this.credentials = credentials;
        super.setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return this.credentials;
    }

    @Override
    public Object getPrincipal() {
        return this.principal;
    }
}

