package com.wisematch.modules.security.auth;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.model.LoginUser;
import com.wisematch.modules.sys.service.SysUserService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Set;

/**
 * @Description 自定义用户认证
 * @Date 21:09
 * <AUTHOR>
 **/
@Component
public class CustomUserDetailsService implements UserDetailsService {

    @Resource
    @Lazy
    private SysUserService sysUserService;

    @Resource
    @Lazy
    private PermissionsService permissionsService;


    @Override
    public LoginUser loadUserByUsername(String username) throws UsernameNotFoundException, DisabledException, AccountExpiredException {
        SysUser sysUser = sysUserService.loadUserByUsername(username);
        if (ObjectUtil.isNull(sysUser)) {
            throw new UsernameNotFoundException("用户不存在");
        }
//        commonLoginHandler.statusVerify(sysUser);
        return createLoginUser(sysUser);
    }

    public UserDetails loadUserByUserId(String userId) throws UsernameNotFoundException {
        SysUser sysUser = sysUserService.getById(userId);
        if (ObjectUtil.isNull(sysUser)) {
            throw new UsernameNotFoundException("用户不存在");
        }
        return createLoginUser(sysUser);
    }

    public LoginUser createLoginUser(SysUser user)
    {
        Set<String> permissions = permissionsService.getUserPermissions(user.getUserId());
        return new LoginUser(user, permissions);
    }
    public UserDetails getDetail(SysUser sysUser) {
        Set<String> permissions = permissionsService.getUserPermissions(sysUser.getUserId());
        String[] roles = new String[0];
        if (CollUtil.isNotEmpty(permissions)) {
            roles = permissions.toArray(String[]::new);
        }
        Collection<? extends GrantedAuthority> authorities = AuthorityUtils.createAuthorityList(roles);

        // 是否允许账号登录
        boolean enabled = sysUser.getStatus() == null || sysUser.getStatus() != 0;
        //帐户未过期
        boolean accountNonExpired = true;
        //帐户未锁定
        boolean accountNonLocked = true;
        //证书未过期
        boolean credentialsNonExpired = true;

        return new CustomUserDetailsUser(sysUser.getTenancyId(),
                sysUser.getUserId(),
                sysUser.getUsername(),
                sysUser.getPassword(),
                enabled,
                accountNonExpired,
                accountNonLocked,
                credentialsNonExpired,
                authorities);
    }
}
