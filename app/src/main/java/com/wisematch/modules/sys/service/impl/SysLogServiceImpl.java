package com.wisematch.modules.sys.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.utils.ThreadPoolUtil;
import com.wisematch.modules.sys.entity.SysLog;
import com.wisematch.modules.sys.mapper.SysLogMapper;
import com.wisematch.modules.sys.service.SysLogService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;


@Service
@AllArgsConstructor
public class SysLogServiceImpl extends ServiceImpl<SysLogMapper, SysLog> implements SysLogService {

    @Override
    public void asyncSave(SysLog sysLog) {
        ThreadPoolUtil.supplyAsync(() -> this
                .save(sysLog)
        );
    }
}
