package com.wisematch.modules.sys.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.modules.sys.entity.SysPermission;
import com.wisematch.modules.sys.mapper.SysPermissionMapper;
import com.wisematch.modules.sys.service.SysPermissionService;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 接口权限管理
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-17 14:48:21
 */
@Service
public class SysPermissionServiceImpl extends ServiceImpl<SysPermissionMapper, SysPermission> implements SysPermissionService {

    @Override
    public List<SysPermission> getPermissionListByUserId(String userId) {
        return baseMapper.getPermissionListByUserId(userId);
    }
}