package com.wisematch.modules.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wisematch.modules.sys.entity.SysPermission;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 接口权限管理
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-17 14:48:21
 */
public interface SysPermissionMapper extends BaseMapper<SysPermission> {

    @Select("SELECT * from sys_permission where permission_id in (SELECT t1.permission_id from sys_role_permission t1,sys_user_role t2 where t1.role_id = t2.role_id and t2.user_id = #{userId})")
    List<SysPermission> getPermissionListByUserId(@Param("userId") String userId);

}
