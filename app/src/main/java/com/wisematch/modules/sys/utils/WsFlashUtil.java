package com.wisematch.modules.sys.utils;

import com.wisematch.modules.chat.utils.AESCBCUtil;
import com.wisematch.modules.chat.utils.HmacSHA256Util;
import com.wisematch.modules.common.utils.Md5Util;

public class WsFlashUtil {

    public static String sign(String appKey, String appid, String token) throws Exception {

        String appkeyMd5 = Md5Util.md5(appKey);
        String key = appkeyMd5.substring(0,16);
        String iv = appkeyMd5.substring(16);
//        System.out.println("appkey_md5: " + key);
//        System.out.println("appkey_md5: " + iv);
        String contact = "appId"+appid+"token"+token;
//        System.out.println(contact);

        return HmacSHA256Util.hmacSha256(contact, appKey);
    }


    public static String decrypt(String phoneName, String appKey) throws Exception {

        String appkeyMd5 = Md5Util.md5(appKey);
        String key = appkeyMd5.substring(0,16);
        String iv = appkeyMd5.substring(16);
//        System.out.println("appkey_md5: " + key);
//        System.out.println("appkey_md5: " + iv);

        return AESCBCUtil.decrypt(phoneName,key,iv);
    }

}