package com.wisematch.modules.sys.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "修改个人信息")
public class ModifyUserInfoDTO implements Serializable {

    @Schema(description = "头像")
    public String photo;

    @Schema(description = "昵称")
    public String nickname;

    @Schema(description = "手机号")
    public String mobile;

    @Schema(description = "邮箱")
    public String email;

    @Schema(description = "真实姓名")
    public String name;

    @Schema(description = "微信号")
    public String vxNo;
}
