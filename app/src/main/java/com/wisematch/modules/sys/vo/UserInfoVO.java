package com.wisematch.modules.sys.vo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description : 用户信息
 * @Date 12:50
 * <AUTHOR>
 **/
@Data
public class UserInfoVO {

    @Schema(description = "微信号")
    public String vxNo;
    @Schema(description = "隐私政策")
    public JSONObject privacyProtection;
    public String userId;
    public String username;
    public String name;
    public String loginIp;
    public String photo;
    public String time;
    public String[] roles;
    public String[] authBtnList;
    public String nickname;
    public Boolean hasPassword;
    @Schema(description = "性别：0女，1男")
    public Integer sex;

    @Schema(description = "邮箱")
    public String email;

    @Schema(description = "组织ID")
    public String orgId;

    @Schema(description = "手机号")
    public String mobile;
    @Schema(description = "找工作状态：1找工作，0不找工作")
    public Integer lookingJob;

    @Schema(description = "是否实名认证，0未认证，1已认证，2认证失败")
    public Integer hasCertification;

}