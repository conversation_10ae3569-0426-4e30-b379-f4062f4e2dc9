package com.wisematch.modules.sys.utils;

public class Test {

    public static void main(String[] args) {
        String markdown_str = "参加展会等。  \n" +
                "\n" +
                "隆晟国际 外贸业务员  \n" +
                "\n" +
                "金属和塑料原材料的采购和谈判以及合同的签订。  \n" +
                "\n" +
                "参加国内外行业展会。  \n" +
                "\n" +
                "宁波坤泰 外贸业务员  \n" +
                "\n" +
                "阿里巴巴国际站的运营，询盘回复，业务洽谈以及合同的签订，参加国内外展会等。  \n" +
                "\n" +
                "教育经历  \n" +
                "\n" +
                "湖北第二师范学院 本科 商务英语  \n" +
                "\n" +
                "1b596a6ef704a0cf1n1y3du8GVZQyoW-V_ycWOWgmfDRNxNr  \n" +
                "\n" +
                "1b5 96a 6ef 704 a0c f1n 1y3 du8 GV ZQ yoW -V_ ycW OW gm fDR Nx Nr  ";
        // ✅ 1. 去除多段乱码（空格分隔）整行，如：1b5 96a ...
        markdown_str = markdown_str.replaceAll("(?m)^([a-zA-Z0-9_-]{2,10}\\s+){6,}[a-zA-Z0-9_-]{2,10}\\s*$", "");

// ✅ 2. 去除一整行超长乱码（没有空格，长度大于30）
        markdown_str = markdown_str.replaceAll("(?m)^[a-zA-Z0-9_-]{20,}\\s*$", "");

// ✅ 3. 删除多余空行
        markdown_str = markdown_str.replaceAll("(?m)^\\s*$", "");

    }


}
