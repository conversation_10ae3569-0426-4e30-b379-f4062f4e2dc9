package com.wisematch.modules.sys.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.common.utils.*;
import com.wisematch.modules.chat.entity.AiOrganization;
import com.wisematch.modules.chat.enums.WiserConstant;
import com.wisematch.modules.chat.model.AiOrganizationVerifyDTO;
import com.wisematch.modules.chat.model.UserInfo;
import com.wisematch.modules.chat.service.GeekRecommendMilvusStoreService;
import com.wisematch.modules.common.handler.id2MetaVerify.Id2MetaVerifyHandler;
import com.wisematch.modules.common.handler.id2MetaVerify.IdCardMd5Encoder;
import com.wisematch.modules.common.handler.id2MetaVerify.NameMd5Encoder;
import com.wisematch.modules.common.utils.JsonUtils;
import com.wisematch.modules.rtc.Utils;
import com.wisematch.modules.sms.SmsVerifyCodeManager;
import com.wisematch.modules.sys.constant.Constants;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.entity.SysUserRole;
import com.wisematch.modules.sys.mapper.SysUserMapper;
import com.wisematch.modules.sys.mapper.SysUserRoleMapper;
import com.wisematch.modules.sys.model.*;
import com.wisematch.modules.sys.service.SysMenuNewService;
import com.wisematch.modules.sys.service.SysUserService;
import com.wisematch.modules.sys.service.TokenService;
import com.wisematch.modules.sys.utils.IdCardValidator;
import com.wisematch.modules.sys.vo.RouterInfo;
import com.wisematch.modules.sys.vo.SysMenuNewVO;
import com.wisematch.modules.sys.vo.UserInfoVO;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutionException;

import static com.wisematch.modules.chat.enums.WiserConstant.DELETED;
import static com.wisematch.modules.chat.enums.WiserConstant.NOT_DELETE;


/**
 * 系统用户
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年9月18日 上午9:46:09
 */
@Service
@AllArgsConstructor
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {
    private final SysUserMapper sysUserMapper;
    private final SysUserRoleMapper sysUserRoleMapper;
    private final PasswordEncoder passwordEncoder;
    private final SysMenuNewService sysMenuNewService;
    private final GeekRecommendMilvusStoreService geekRecommendMilvusStoreService;


    @Autowired
    Id2MetaVerifyHandler id2MetaVerifyHandler;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SmsVerifyCodeManager smsVerifyCodeManager;

    @Override
    public SysUser getById(Serializable id) {
        SysUser sysUser = super.getById(id);
        if (sysUser == null) {
            throw new RRException(RRExceptionEnum.USER_LOGIN_AUTH);
        }
        return sysUser;
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        return this.baseMapper.selectOne(new QueryWrapper<SysUser>().lambda().eq(SysUser::getUsername, userName));
    }


    private boolean isUserExist(String phone) {
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysUser::getMobile, phone);
        return this.baseMapper.exists(queryWrapper);
    }

    @Override
    public SysUser createUser(String phone) {
        SysUser user = loadUserByPhone(phone);
        if (null == user) {
            SysUser sysUser = new SysUser();
//            sysUser.setUserId(IdUtil.fastSimpleUUID());
            sysUser.setName("");
            sysUser.setUsername(phone);
            sysUser.setMobile(phone);
            sysUser.setStatus(1);
            sysUser.setPhoto("https://wise-match.oss-cn-hangzhou.aliyuncs.com/user/resume/803afbe6aef9c2fbd9aa0559449c75b2/ba93d93bbecb4f9dbafbf726ab5c52ef.png");
            sysUser.setCreateTime(new Date());
            this.baseMapper.insert(sysUser);
            return sysUser;
        }
//        else if (DELETED.equals(user.getIsDel())) {
//            retrieveAccount(user.getUserId(), NOT_DELETE);
//            return user;
//        }
        else {
            return user;
        }
    }

    @Override
    public SysUser createUser(String phone, String password) {
        SysUser user = loadUserByPhone(phone);
        if (null == user) {
            SysUser sysUser = new SysUser();
//            sysUser.setUserId(IdUtil.fastSimpleUUID());
            sysUser.setName("");
            sysUser.setUsername(phone);
            sysUser.setPassword(passwordEncoder.encode(password));
            sysUser.setMobile(phone);
            sysUser.setStatus(1);
            sysUser.setCreateTime(new Date());
            this.baseMapper.insert(sysUser);
            return sysUser;
        }
//        else if (DELETED.equals(user.getIsDel())) {
//            retrieveAccount(user.getUserId(), NOT_DELETE);
//            return user;
//        }
        else {
            return user;
        }
    }




    @Override
    public UserInfo getUserByPhone(String phone) {
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysUser::getMobile, phone);
        SysUser sysUser = this.baseMapper.selectOne(queryWrapper);
        if (sysUser != null) {
            UserInfo userInfo = new UserInfo();
            userInfo.setPhone(phone);
            userInfo.setUserId(sysUser.getUserId());
            userInfo.setUserName(sysUser.getUsername());
            userInfo.setUserPhoto(sysUser.getPhoto());
            return userInfo;
        }
        return null;
    }

    @Override
    @Transactional
    public void saveUserRole(SysUser user) {
        user.setCreateTime(new Date());
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        sysUserMapper.insert(user);
        sysUserRoleMapper.delete(Wrappers.<SysUserRole>update().lambda().eq(SysUserRole::getUserId, user.getUserId()));
        //保存用户与角色关系
        saveUserRoleList(user);
    }

    @Override
    @Transactional
    public void updateUserRole(SysUser user) {
        if (StringUtils.isBlank(user.getPassword())) {
            user.setPassword(null);
        } else {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }
        baseMapper.updateById(user);
        sysUserRoleMapper.delete(Wrappers.<SysUserRole>update().lambda().eq(SysUserRole::getUserId, user.getUserId()));
        //保存用户与角色关系
        saveUserRoleList(user);
    }

    @Override
    public SysUser loadUserByPhone(String phone) {

        return this.getOne(new QueryWrapper<SysUser>().lambda()
                .eq(SysUser::getMobile, phone)
                .eq(SysUser::getIsDel, NOT_DELETE));
    }

    @Override
    public void updateUserInfo(ModifyUserInfoDTO modifyUserInfoDTO) {
        SysUser sysUser = new SysUser();
        if(null != modifyUserInfoDTO.getName()){
            if(!UserInfoUtils.getLoginUser().getName().equals(modifyUserInfoDTO.getName())){
                sysUser.setHasCertification(WiserConstant.NOT_CERTIFICATED);
            }
        }

        BeanUtils.copyProperties(modifyUserInfoDTO, sysUser);
        sysUser.setUserId(UserInfoUtils.getCurrentUserId());
        this.updateById(sysUser);
        this.updateRedisUserInfo(sysUser);
    }

    @Override
    public void modifyPhone(ModifyPhoneDTO modifyUserInfoDTO) {
        String phone = modifyUserInfoDTO.getPhone();
        boolean flag = smsVerifyCodeManager.validateUserVerifyCode(phone, modifyUserInfoDTO.getCode());
//        if (flag){
        this.update(new LambdaUpdateWrapper<SysUser>()
                .eq(SysUser::getUserId, UserInfoUtils.getCurrentUserId())
                .set(SysUser::getUsername, phone)
                .set(SysUser::getMobile, phone));

        LoginUser loginUser = UserInfoUtils.getLoginUser();
        loginUser.setUsername(phone);
        loginUser.setPhone(phone);
        tokenService.refreshToken(loginUser);
//        }


    }


    @Override
    public void changeStatus(String userId, Integer status) {
        sysUserMapper.update(new UpdateWrapper<SysUser>().lambda()
                .eq(SysUser::getUserId, userId)
                .set(SysUser::getStatus, status));

        LoginUser loginUser = UserInfoUtils.getLoginUser();
        loginUser.setStatus(status);
        tokenService.refreshToken(loginUser);
    }

    @Override
    public void changeLookingStatus(String userId, Integer status) {
        sysUserMapper.update(new UpdateWrapper<SysUser>().lambda()
                .eq(SysUser::getUserId, userId)
                .set(SysUser::getLookingJob, status));

        LoginUser loginUser = UserInfoUtils.getLoginUser();
        loginUser.setLookingJob(status);
        tokenService.refreshToken(loginUser);
    }

    @Override
    public void retrieveAccount(String userId, Integer status) {
        sysUserMapper.update(new UpdateWrapper<SysUser>().lambda()
                .eq(SysUser::getUserId, userId)
                .set(SysUser::getIsDel, status).set(SysUser::getUsername, Utils.usernameGenerate()));
    }

    @Override
    public long idCardCount(String idCard) {
        return this.baseMapper.selectCount(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getIdCard, idCard)
                .eq(SysUser::getIsDel, NOT_DELETE)
                .eq(SysUser::getHasCertification, WiserConstant.CERTIFICATED_SUCCESS));
    }

    @Override
    public void id2MetaVerify(Id2MetaVerifyDTO user) throws ExecutionException, InterruptedException {

        String userId = UserInfoUtils.getCurrentUserId();
        String name = user.getName();
        String idCard = user.getIdCard();
        if (!IdCardValidator.validate(idCard)) {
            //正则校验
            throw new RRException(RRExceptionEnum.AUTHENTICATION_FAILED);
        }
        String idCardMd5 = IdCardMd5Encoder.encodeIdCardToMd5(idCard);
        if (idCardCount(idCardMd5) >= 3) {
            throw new RRException(RRExceptionEnum.ID_CARD_IS_USED);
        }
        String md5Name = NameMd5Encoder.encodeNameToMd5(name);
        Integer hasCertification = id2MetaVerifyHandler.handle(idCardMd5, md5Name) ? WiserConstant.CERTIFICATED_SUCCESS : WiserConstant.CERTIFICATED_FAIL;

        int index = idCard.charAt(idCard.length() - 2);
        int sex = index % 2 == 0 ? 0 : 1;

        this.baseMapper.update(new UpdateWrapper<SysUser>().lambda().eq(SysUser::getUserId, userId)
                .set(SysUser::getIdCard, idCardMd5)
                .set(SysUser::getName, name)
                .set(SysUser::getSex, sex)
                .set(SysUser::getHasCertification, hasCertification)
                .set(SysUser::getBirthDayStr, IdCardMd5Encoder.extractBirthDay(idCard)));
        LoginUser loginUser = UserInfoUtils.getLoginUser();
        loginUser.setName(name);
        loginUser.setSex(sex);
        loginUser.setHasCertification(hasCertification);
        tokenService.refreshToken(loginUser);
        if(WiserConstant.CERTIFICATED_FAIL.equals(hasCertification)){
            throw new RRException(RRExceptionEnum.AUTHENTICATION_FAILED);
        }
    }

    @Override
    public void updateOrgId(String userId, String orgId) {
        this.update(new LambdaUpdateWrapper<SysUser>()
                .eq(SysUser::getUserId, userId).eq(SysUser::getIsDel, NOT_DELETE).set(SysUser::getOrgId, orgId));
    }

    @Override
    public void updateOrgIdWithRedis(LoginUser loginUser, AiOrganizationVerifyDTO dto) {
        LambdaUpdateWrapper<SysUser> wrapper = new LambdaUpdateWrapper<SysUser>()
                .eq(SysUser::getUserId, loginUser.getUserId()).eq(SysUser::getIsDel, NOT_DELETE)
                .set(SysUser::getOrgId, dto.getId());

        loginUser.setOrgId(dto.getId());
//        if(null != dto.getIsCertified()){
//            wrapper.set(SysUser::getHasCertification, dto.getIsCertified());
//            loginUser.setHasCertification(dto.getIsCertified());
//        }
        this.update(wrapper);
        tokenService.refreshToken(loginUser);
    }

    @Override
    public void updateOrgIdAndRedis(LoginUser loginUser, AiOrganization dto) {
        LambdaUpdateWrapper<SysUser> wrapper = new LambdaUpdateWrapper<SysUser>()
                .eq(SysUser::getUserId, loginUser.getUserId()).eq(SysUser::getIsDel, NOT_DELETE)
                .set(SysUser::getOrgId, dto.getId());

        loginUser.setOrgId(dto.getId());
//        if(null != dto.getIsCertified()){
//            wrapper.set(SysUser::getHasCertification, dto.getIsCertified());
//            loginUser.setHasCertification(dto.getIsCertified());
//        }
        this.update(wrapper);
        tokenService.refreshToken(loginUser);
    }

    @Override
    public void cleanCompanyInfo(String userId) {
        this.baseMapper.update(new LambdaUpdateWrapper<SysUser>()
                .eq(SysUser::getUserId, userId).eq(SysUser::getIsDel, NOT_DELETE)
                .set(SysUser::getHasCertification, WiserConstant.NOT_CERTIFICATED).set(SysUser::getOrgId, null));
        LoginUser loginUser = UserInfoUtils.getLoginUser();
        loginUser.setOrgId(null);
        loginUser.setHasCertification(WiserConstant.NOT_CERTIFICATED);
        tokenService.refreshToken(loginUser);
    }


    @Override
    public RouterInfo sysInfo() {

        // 用户菜单
        final List<SysMenuNewVO> userMenu = sysMenuNewService.getUserMenu();

        RouterInfo routerInfo = new RouterInfo();
        routerInfo.setMenus(userMenu);

        // 用户信息
        final SysUser sysUser = this.getById(UserInfoUtils.getCurrentUserId());

        UserInfoVO userInfo = new UserInfoVO();
        BeanUtils.copyProperties(sysUser, userInfo);
        userInfo.setHasPassword(sysUser.getPassword() != null);
        userInfo.setLookingJob(sysUser.getLookingJob());
        userInfo.setUserId(sysUser.getUserId());
        userInfo.setUsername(sysUser.getUsername());
        userInfo.setNickname(sysUser.getNickname());
        userInfo.setName(sysUser.getName());
        userInfo.setOrgId(sysUser.getOrgId());

        JSONObject privacyProtection = StringUtils.isBlank(sysUser.getPrivacyProtection())
                ?JsonUtils.toJsonObject(new PrivacyProtectionChangeDTO()) : JSON.parseObject(sysUser.getPrivacyProtection());

        userInfo.setPrivacyProtection(privacyProtection);
        userInfo.setMobile(sysUser.getMobile());
        userInfo.setSex(sysUser.getSex());
        userInfo.setHasCertification(sysUser.getHasCertification());
        userInfo.setEmail(sysUser.getEmail());
        userInfo.setLoginIp(IPUtils.getIpAddr(HttpContextUtils.getHttpServletRequest()));
        final String photo = sysUser.getPhoto();
        userInfo.setPhoto(photo == null ? Constants.DEFAULT_AVATAR : photo);
        userInfo.setRoles(new String[]{sysUser.getUserId().equals(Constant.SUPER_ADMIN) ? "admin" : "common"});
        userInfo.setTime(DateUtil.now());
        userInfo.setAuthBtnList(new String[]{"btn.add", "btn.del", "btn.edit", "btn.link"});
        routerInfo.setUserInfo(userInfo);
        return routerInfo;
    }

    @Override
    public R cancelAccount() {

        LoginUser loginUser = UserInfoUtils.getLoginUser();
        if (loginUser == null || loginUser.getUserId() == null) {
            return R.error("参数错误");
        }

        if (loginUser.getUserId().equals(Constant.SUPER_ADMIN)) {
            return R.error("系统管理员不能注销");
        }

        tokenService.delLoginUser(UserInfoUtils.getLoginUser().getToken());
        this.update(new LambdaUpdateWrapper<SysUser>()
                .eq(SysUser::getUserId, loginUser.getUserId())
                .set(SysUser::getIsDel, DELETED));

        ThreadPoolUtil.execute(() -> geekRecommendMilvusStoreService.cancelAccount(loginUser.getUserId()));
        return R.ok();
    }

    @Override
    public R deleteUser(SysUser user) {

        if (user == null || user.getUserId() == null) {
            return R.error("参数错误");
        }
        String userId = user.getUserId();
        if (userId.equals(Constant.SUPER_ADMIN)) {
            return R.error("系统管理员不能删除");
        }

        if (userId.equals(UserInfoUtils.getCurrentUserId())) {
            return R.error("当前用户不能删除");
        }
        this.removeById(userId);

        ThreadPoolUtil.execute(() -> geekRecommendMilvusStoreService.deleteUser(userId));
        return R.ok();
    }

    @Override
    public PrivacyProtectionChangeDTO getPrivacyProtection(String userId) {
        SysUser sysUser = this.getById(userId);
        return PrivacyProtectionChangeDTO.getByString(sysUser.getPrivacyProtection());
    }


    @Override
    public void updateRedisUserInfo(SysUser sysUser) {
        LoginUser loginUser = UserInfoUtils.getLoginUser();
//        if (null != modifyUserInfoDTO.getName()) {
//            loginUser.setName(modifyUserInfoDTO.getName());
//        }
        if (null != sysUser.getPhoto()) {
            loginUser.setUserPhoto(sysUser.getPhoto());
        }
        if (null != sysUser.getNickname()) {
            loginUser.setNickname(sysUser.getNickname());
        }
        if (null != sysUser.getName()) {
            loginUser.setName(sysUser.getName());
        }
        if (null != sysUser.getName()) {
            loginUser.setName(sysUser.getName());
        }
        if (null != sysUser.getVxNo()) {
            loginUser.setVxNo(sysUser.getVxNo());
        }
        if (null != sysUser.getHasCertification()) {
            loginUser.setHasCertification(sysUser.getHasCertification());
        }
        tokenService.refreshToken(loginUser);
    }

    @Override
    public SysUser loadUserByUsername(String username) {
        return this.getOne(Wrappers.<SysUser>query().lambda()
                .eq(SysUser::getUsername, username).eq(SysUser::getIsDel,NOT_DELETE));
    }

    @Override
    public int updatePassword(String userId, String password, String newPassword) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setPassword(newPassword);
        return sysUserMapper.updateById(sysUser);
    }

    @Override
    public R webUpdatePassword(UpdatePasswordDTO updatePasswordDTO) {

        if (StrUtil.isBlankIfStr(updatePasswordDTO.getNewPassword())||StrUtil.isBlankIfStr(updatePasswordDTO.getPassword())) {
            return R.error("新密码不为能空");
        }
        if(!updatePasswordDTO.getNewPassword().equals(updatePasswordDTO.getPassword())){
            return R.error("两次密码不一致");
        }
        String  password = passwordEncoder.encode(updatePasswordDTO.getPassword());
        this.updatePassword(UserInfoUtils.getCurrentUserId(), password, password);
        //更新密码
        return R.ok();
    }

    public void saveUserRoleList(SysUser user) {
        if (CollUtil.isNotEmpty(user.getRoleIdList())) {
            user.getRoleIdList().forEach(roleId -> {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(user.getUserId());
                userRole.setRoleId(roleId);
                sysUserRoleMapper.insert(userRole);
            });
        }
    }
}
