package com.wisematch.modules.sys.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.wisematch.modules.chat.model.PreApplyVO;
import com.wisematch.modules.common.utils.JsonUtils;
import com.wisematch.modules.sys.entity.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.io.Serial;
import java.util.Collection;
import java.util.Set;

/**
 * 登录用户身份权限
 *
 * <AUTHOR>
 */
public class LoginUser implements UserDetails {
    @Serial
    private static final long serialVersionUID = 1L;

    @Getter
    @Setter
    @Schema(description = "找工作状态：1找工作，0不找工作")
    public Integer lookingJob;
    /**
     * 用户ID
     */
    @Getter
    @Setter
    @Schema(description = "tenancyId")
    private Integer tenancyId;

    @Schema(description = "状态  0：禁用   1：正常")
    @Getter
    @Setter
    public Integer status;
    @Getter
    @Setter
    private Integer hasCertification;

    @Getter
    @Setter
    @Schema(description = "隐私政策")
    public JSONObject privacyProtection;

    @Getter
    @Setter
    @Schema(description = "性别：0女，1男")
    public Integer sex;
    /**
     * 用户ID
     */
    @Getter
    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "用户姓名")
    @Setter
    @Getter
    String name;

    @Schema(description = "微信号")
    @Setter
    @Getter
    public String vxNo;

    @Schema(description = "企业id")
    @Setter
    @Getter
    String orgId;

    @Schema(description = "用户昵称")
    @Setter
    String nickname;

    @Schema(description = "用户账号")
    @Setter
    String username;

    @Schema(description = "用户电话")
    @Setter
    @Getter
    String phone;

    @Schema(description = "用户头像")
    @Setter
    @Getter
    String userPhoto;

    private String password;
    /**
     * 用户唯一标识
     */
    @Getter
    private String token;

    @Schema(description = "是否注销")
    @Setter
    @Getter
    public Integer isDel;

    @Schema(description = "邮箱")
    @Setter
    @Getter
    public String email;
    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 登录地点
     */
    private String loginLocation;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 权限列表
     */
    private Set<String> permissions;


    public LoginUser() {
    }

    public LoginUser(SysUser user, Set<String> permissions) {
        BeanUtils.copyProperties(user, this);
        this.username = user.getUsername();
        this.userPhoto = user.getPhoto();
        this.phone = user.getMobile();
        this.hasCertification = user.getHasCertification();
        this.privacyProtection = StringUtils.isBlank(user.getPrivacyProtection())
                ?JsonUtils.toJsonObject(new PrivacyProtectionChangeDTO()):JSON.parseObject(user.getPrivacyProtection());

        this.privacyProtection = JSON.parseObject(user.getPrivacyProtection());
        this.name = user.getName();
        this.nickname = user.getNickname();
        this.tenancyId = user.getTenancyId();
        this.userId = user.getUserId();
        this.orgId = user.getOrgId();
        this.password = user.getPassword();
        this.permissions = permissions;
    }


    public void setUserId(String userId) {
        this.userId = userId;
    }

    public void setToken(String token) {
        this.token = token;
    }

    @JSONField(serialize = false)
    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return username;
    }

    /**
     * 账户是否未过期,过期无法验证
     */
    @JSONField(serialize = false)
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * 指定用户是否解锁,锁定的用户无法进行身份验证
     *
     * @return
     */
    @JSONField(serialize = false)
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    /**
     * 指示是否已过期的用户的凭据(密码),过期的凭据防止认证
     *
     * @return
     */
    @JSONField(serialize = false)
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    /**
     * 是否可用 ,禁用的用户不能身份验证
     *
     * @return
     */
    @JSONField(serialize = false)
    @Override
    public boolean isEnabled() {
        return true;
    }

    public Long getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(Long loginTime) {
        this.loginTime = loginTime;
    }

    public String getIpaddr() {
        return ipaddr;
    }

    public void setIpaddr(String ipaddr) {
        this.ipaddr = ipaddr;
    }

    public String getLoginLocation() {
        return loginLocation;
    }

    public void setLoginLocation(String loginLocation) {
        this.loginLocation = loginLocation;
    }

    public String getBrowser() {
        return browser;
    }

    public void setBrowser(String browser) {
        this.browser = browser;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public Long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Long expireTime) {
        this.expireTime = expireTime;
    }

    public Set<String> getPermissions() {
        return permissions;
    }

    public void setPermissions(Set<String> permissions) {
        this.permissions = permissions;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }



    public PreApplyVO wrapToPreApplyVO() {
        PreApplyVO preApplyVO = new PreApplyVO();
        preApplyVO.setApplyPhone(this.getPhone());
        preApplyVO.setApplyEmail(this.getEmail());
        preApplyVO.setApplyName(this.getName());
        return preApplyVO;
    }
}
