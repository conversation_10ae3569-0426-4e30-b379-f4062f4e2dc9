package com.wisematch.modules.sys.utils;

import java.util.regex.Pattern;

public class IdCardValidator {

    // 严格版18位身份证正则表达式
    private static final Pattern ID_CARD_PATTERN = Pattern.compile(
            "^" +
                    // 1-2位：省级行政区划代码（11-65）
                    "[1-9]\\d{5}" +
                    // 3-4位：地级行政区划代码（00-99）
                    // 5-6位：县级行政区划代码（00-99）
                    // 7-10位：出生年份（1800-2299）
                    "(18|19|20|21|22)\\d{2}" +
                    // 11-12位：出生月份（01-12）
                    "(0[1-9]|1[0-2])" +
                    // 13-14位：出生日期（01-31，会根据月份验证）
                    "(0[1-9]|[12]\\d|3[01])" +
                    // 15-17位：顺序码（000-999）
                    "\\d{3}" +
                    // 18位：校验码（0-9或X）
                    "[0-9Xx]" +
                    "$"
    );

    /**
     * 验证18位身份证号码格式
     * @param idCard 身份证号码
     * @return 是否有效
     */
    public static boolean validate(String idCard) {
        if (idCard == null || idCard.length() != 18) {
            return false;
        }

        // 基础格式校验
        if (!ID_CARD_PATTERN.matcher(idCard).matches()) {
            return false;
        }

        // 校验日期有效性
        if (!validateBirthDate(idCard)) {
            return false;
        }

        // 校验码验证（可选）
        return validateCheckDigit(idCard);
    }

    /**
     * 验证身份证中的出生日期是否有效
     */
    private static boolean validateBirthDate(String idCard) {
        try {
            int year = Integer.parseInt(idCard.substring(6, 10));
            int month = Integer.parseInt(idCard.substring(10, 12));
            int day = Integer.parseInt(idCard.substring(12, 14));

            // 简单月份天数验证（不考虑闰年）
            if (month == 2) {
                return day <= (isLeapYear(year) ? 29 : 28);
            }
            if (month == 4 || month == 6 || month == 9 || month == 11) {
                return day <= 30;
            }
            return day <= 31;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 闰年判断
     */
    private static boolean isLeapYear(int year) {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }

    /**
     * 校验码验证（ISO 7064:1983.MOD 11-2标准）
     */
    private static boolean validateCheckDigit(String idCard) {
        // 前17位数字
        String num17 = idCard.substring(0, 17);
        // 计算校验位
        char calculatedCheckDigit = calculateCheckDigit(num17);
        // 实际校验位
        char actualCheckDigit = Character.toUpperCase(idCard.charAt(17));

        return calculatedCheckDigit == actualCheckDigit;
    }

    /**
     * 计算校验码
     */
    private static char calculateCheckDigit(String num17) {
        // 加权因子
        int[] weightFactors = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        // 校验码对应值
        char[] checkDigits = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};

        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += (num17.charAt(i) - '0') * weightFactors[i];
        }

        int mod = sum % 11;
        return checkDigits[mod];
    }


    /**
     * 获取无效原因分析（辅助方法）
     */
    private static String getInvalidReason(String idCard) {
        if (!ID_CARD_PATTERN.matcher(idCard).matches()) {
            return "基本格式不符合要求";
        }

        if (!validateBirthDate(idCard)) {
            return "出生日期无效";
        }

        if (!validateCheckDigit(idCard)) {
            return "校验码不匹配";
        }

        return "未知原因";
    }
}
