package com.wisematch.modules.sys.model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * 求职申请表单实体类
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class JobApplication {
    /**
     * 工作期望（全职/兼职等）
     */
    private String jobExpectations;

    /**
     * 期望行业
     */
    private String expectedIndustry;

    /**
     * 求职状态
     */
    private String jobStatus;

    /**
     * 表单类型
     */
    private String formType;

    /**
     * 工作城市
     */
    private String workCity;

    /**
     * 薪资要求下限
     */
    private String salaryRequirementsStart;

    /**
     * 期望职位
     */
    private String expectedPosition;

    /**
     * 薪资要求上限
     */
    private String salaryRequirementsEnd;

    /**
     * 是否允许查看视频
     */
    private String allowViewingVideos;

    /**
     * 房间ID
     */
    private String roomId;

    /**
     * 期望城市
     */
    private String expectedCity;
}

