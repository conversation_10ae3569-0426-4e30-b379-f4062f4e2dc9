package com.wisematch.modules.common.handler.id2MetaVerify;

import com.wisematch.modules.common.utils.Md5Util;

public class NameMd5Encoder {


    /**
     * MD5编码方法
     * @param input 输入字符串
     * @return 32位小写MD5
     */
    public static String encodeNameToMd5(String input) {
        String start = Md5Util.md5(input.substring(0, 1));
        return start + input.substring(1, input.length());
    }

}
