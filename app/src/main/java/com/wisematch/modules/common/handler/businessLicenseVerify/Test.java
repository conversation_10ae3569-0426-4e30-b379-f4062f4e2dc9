package com.wisematch.modules.common.handler.businessLicenseVerify;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.cloudauth20221125.models.EntElementVerifyV2Request;
import com.aliyun.sdk.service.cloudauth20221125.models.EntElementVerifyV2Response;
import com.aliyun.sdk.service.cloudauth20221125.models.EntElementVerifyV2ResponseBody;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.common.utils.ConfigConstant;
import darabonba.core.client.ClientOverrideConfiguration;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

public class Test {

    public static void main(String s[]) throws ExecutionException, InterruptedException {

        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId("LTAI5tC3Ze5R54rZ22ohvNMY")
                .accessKeySecret("******************************")
                .build());

        // Configure the Client
        com.aliyun.sdk.service.cloudauth20221125.AsyncClient client = com.aliyun.sdk.service.cloudauth20221125.AsyncClient.builder()
                .region("cn-hangzhou")
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                .setEndpointOverride("cloudauth.aliyuncs.com")
                )
                .build();

        EntElementVerifyV2Request verifyBusinessLicenseRequest = EntElementVerifyV2Request.builder()
                .userAuthorization("1")
                .sceneCode(ConfigConstant.IdUtils.fastSimpleUUID())
                .merchantBizId("orgVerify_"+ConfigConstant.IdUtils.fastSimpleUUID())
                .merchantUserId("mch_"+ConfigConstant.IdUtils.fastSimpleUUID())
                .infoVerifyType("ENT_4META")
                .licenseNo("91330109MAEL973B99")
                .legalPersonName("冯迪洋")
                .entName("杭州青麟栖云科技有限公司")
                .legalPersonCertNo("412724200003072522")
                .build();

        CompletableFuture<EntElementVerifyV2Response> response = client.entElementVerifyV2(verifyBusinessLicenseRequest);

        EntElementVerifyV2Response resp = response.get();

        if(200 != resp.getStatusCode()){
            throw new RRException(RRExceptionEnum.SERVICE_UNAVAILABLE);
        }
        EntElementVerifyV2ResponseBody body = resp.getBody();
        String bizCode = body.getResult().getBizCode();
        String status = body.getResult().getStatus();
        System.out.println(bizCode+"::"+status);

    }


}
