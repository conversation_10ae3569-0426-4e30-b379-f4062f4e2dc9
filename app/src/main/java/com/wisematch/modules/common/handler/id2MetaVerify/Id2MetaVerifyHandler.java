package com.wisematch.modules.common.handler.id2MetaVerify;


import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.cloudauth20190307.AsyncClient;
import com.aliyun.sdk.service.cloudauth20190307.models.Id2MetaVerifyRequest;
import com.aliyun.sdk.service.cloudauth20190307.models.Id2MetaVerifyResponse;
import com.aliyun.sdk.service.cloudauth20190307.models.Id2MetaVerifyResponseBody;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.modules.chat.model.Id2MetaVerifyConfigDTO;
import com.wisematch.modules.common.service.AiSysConfigService;
import com.wisematch.modules.common.utils.JsonUtils;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Component
@Slf4j
public class Id2MetaVerifyHandler {

    @Autowired
    AiSysConfigService aiSysConfigService;

    public boolean handle(String idCard, String name) throws ExecutionException, InterruptedException {
        String id2MetaVerifyDTO = aiSysConfigService.selectConfigByKey("sys.agent.Id2MetaVerify");
        Id2MetaVerifyConfigDTO id2MetaVerifyConfigDTO = JsonUtils.fromJson(id2MetaVerifyDTO, Id2MetaVerifyConfigDTO.class);

        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(id2MetaVerifyConfigDTO.getKeyId())
                .accessKeySecret(id2MetaVerifyConfigDTO.getSecretKey())
                .build());

        // Configure the Client
        AsyncClient client = AsyncClient.builder()
                .region("cn-hangzhou") // Region ID
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                // Endpoint 请参考 https://api.aliyun.com/product/Cloudauth
                                .setEndpointOverride(id2MetaVerifyConfigDTO.getEndpoint())
                )
                .build();

        // Parameter settings for API request
        Id2MetaVerifyRequest id2MetaVerifyRequest = Id2MetaVerifyRequest.builder()
                .paramType("md5")
                .identifyNum(idCard)
                .userName(name)
                .build();

        CompletableFuture<Id2MetaVerifyResponse> response = client.id2MetaVerify(id2MetaVerifyRequest);

        try{
            Id2MetaVerifyResponse resp = response.get();
            Id2MetaVerifyResponseBody body = resp.getBody();
            if("200".equals(body.getCode())){
                log.info("请求成功");
                if("1".equals(body.getResultObject().getBizCode())){
                    log.info("认证成功");
                    client.close();
                    return true;
                }
            }
        }catch (Exception e){
            throw new RRException(RRExceptionEnum.UNKNOWN_ERROR);
        }

        client.close();
        return false;
    }

}
