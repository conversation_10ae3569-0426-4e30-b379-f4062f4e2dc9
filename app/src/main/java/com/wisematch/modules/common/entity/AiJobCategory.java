package com.wisematch.modules.common.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("ai_job_category")
@Schema(description = "带父子级关系的行业职位分类表")
public class AiJobCategory implements Serializable {

    public static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "分类名称（如：互联网/AI、后端开发、Java等）")
    private String name;

    @Schema(description = "父级ID，0表示顶级分类")
    private Long parentId;

    @Schema(description = "层级：1-行业类型，2-职位类型，3-职位大类")
    private Integer level;

    @Schema(description = "排序序号，用于控制显示顺序")
    private Integer sortOrder;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "唯一标识编码")
    private String code;
}