package com.wisematch.modules.common.handler.businessLicenseVerify;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.ocr_api20210707.AsyncClient;
import com.aliyun.sdk.service.ocr_api20210707.models.RecognizeBusinessLicenseRequest;
import com.aliyun.sdk.service.ocr_api20210707.models.RecognizeBusinessLicenseResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.modules.chat.model.AliCommonConfigDTO;
import com.wisematch.modules.chat.model.LicenseeVO;
import com.wisematch.modules.chat.model.RecognizeBusinessLicenseDTO;
import com.wisematch.modules.common.service.AiSysConfigService;
import com.wisematch.modules.common.utils.JsonUtils;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RecognizeBusinessLicenseHandler {

    @Autowired
    AiSysConfigService aiSysConfigService;

    public void recognizeBusinessLicense(BusinessLicenseContext businessLicenseContext) throws ExecutionException, InterruptedException, JsonProcessingException {
        String aliVo = aiSysConfigService.selectConfigByKey("sys.agent.aliCommon");
        AliCommonConfigDTO aliCommonConfigDTO = JsonUtils.fromJson(aliVo, AliCommonConfigDTO.class);

        String licenseeVo = aiSysConfigService.selectConfigByKey("sys.agent.RecognizeBusinessLicense");
        RecognizeBusinessLicenseDTO recognizeBusinessLicenseDTO = JsonUtils.fromJson(licenseeVo, RecognizeBusinessLicenseDTO.class);

        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(aliCommonConfigDTO.getKeyId())
                .accessKeySecret(aliCommonConfigDTO.getSecretKey())
                .build());

        AsyncClient client = AsyncClient.builder()
                .region(recognizeBusinessLicenseDTO.getRegion())
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                .setEndpointOverride(recognizeBusinessLicenseDTO.getEndpointOverride())
                )
                .build();

        RecognizeBusinessLicenseRequest recognizeBusinessLicenseRequest = RecognizeBusinessLicenseRequest.builder()
                .url(businessLicenseContext.getFileSrc())
                .build();

        CompletableFuture<RecognizeBusinessLicenseResponse> response = client.recognizeBusinessLicense(recognizeBusinessLicenseRequest);
        RecognizeBusinessLicenseResponse resp = null;
        try{
            resp = response.get();
        }catch (Exception e){
            throw new RRException(RRExceptionEnum.BUSINESS_LICENSE_ERROR);
        }

        if(200 != resp.getStatusCode()){
            throw new RRException(RRExceptionEnum.SERVICE_UNAVAILABLE);
        }
        String resultData = resp.getBody().getData();
        LicenseeVO licenseeVO = parseToObj(resultData);
        client.close();

        businessLicenseContext.setBusinessAddress(licenseeVO.getBusinessAddress());
        businessLicenseContext.setCreditCode(licenseeVO.getCreditCode());
        businessLicenseContext.setCompanyName(licenseeVO.getCompanyName());
        businessLicenseContext.setLegalPerson(licenseeVO.getLegalPerson());
        businessLicenseContext.setKeyId(aliCommonConfigDTO.getKeyId());
        businessLicenseContext.setSecretKey(aliCommonConfigDTO.getSecretKey());
    }

    private LicenseeVO parseToObj(String resultData) throws JsonProcessingException {
        String unescaped = StringEscapeUtils.unescapeJson(resultData);
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(unescaped);
        JsonNode dataNode = rootNode.get("data");
        return mapper.treeToValue(dataNode, LicenseeVO.class);
    }


}
