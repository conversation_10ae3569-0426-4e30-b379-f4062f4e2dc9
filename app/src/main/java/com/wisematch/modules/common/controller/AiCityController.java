package com.wisematch.modules.common.controller;

import com.wisematch.common.utils.R;
import com.wisematch.modules.chat.model.AiCitySelectAllDTO;
import com.wisematch.modules.chat.model.IdRequest;
import com.wisematch.modules.common.service.AiCityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/aiCity")
@Tag(name = "城市", description = "城市")
public class AiCityController {

    @Autowired
    private AiCityService aiCityService;

    @PostMapping("/detail")
    @Operation(summary = "id获取详情")
    public R detail(@RequestBody IdRequest idRequest){
        return R.ok().setData(aiCityService.getById(idRequest.getId()));
    }

    @PostMapping("/selectAll")
    @Operation(summary = "查询菜单")
    public R selectAll(@RequestBody AiCitySelectAllDTO aiCitySelectAllDTO){
        return R.ok().setData(aiCityService.selectAll(aiCitySelectAllDTO));
    }


}

