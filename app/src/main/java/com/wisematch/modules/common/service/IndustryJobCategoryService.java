//package com.wisematch.modules.common.service;
//
//import com.baomidou.mybatisplus.extension.service.IService;
//import com.wisematch.modules.chat.model.IndustryJobCategoryTreeDTO;
//import com.wisematch.modules.common.entity.IndustryJobCategory;
//
//import java.util.List;
//
///**
// * 行业职位分类表Service接口
// */
//public interface IndustryJobCategoryService extends IService<IndustryJobCategory> {
//    // 继承IService后，已包含复杂CRUD方法（如批量操作、分页等）
//    // 可在此扩展业务相关方法
//    List<IndustryJobCategoryTreeDTO> listTree();
//
//    IndustryJobCategory getUniqueByLevelAndName(String name, Integer level);
//}