package com.wisematch.modules.common.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class GetNLevelCategoryModel {

    @Schema(description = "级别", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer level;

    @Schema(description = "模糊查询", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String name;

    @Schema(description = "parentId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String parentId;
}
