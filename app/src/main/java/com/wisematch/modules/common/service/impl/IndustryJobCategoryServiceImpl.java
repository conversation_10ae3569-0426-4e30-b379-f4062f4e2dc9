//package com.wisematch.modules.common.service.impl;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.wisematch.common.exception.RRException;
//import com.wisematch.common.exception.RRExceptionEnum;
//import com.wisematch.modules.chat.model.IndustryJobCategoryTreeDTO;
//import com.wisematch.modules.common.entity.IndustryJobCategory;
//import com.wisematch.modules.common.mapper.IndustryJobCategoryMapper;
//import com.wisematch.modules.common.service.IndustryJobCategoryService;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
///**
// * 行业职位分类表Service实现类
// */
//@Service
//public class IndustryJobCategoryServiceImpl
//        extends ServiceImpl<IndustryJobCategoryMapper, IndustryJobCategory>
//        implements IndustryJobCategoryService {
//
//    // 如需自定义业务逻辑，可在此实现
//
//
//    /**
//     * 根据名称和层级查询唯一节点
//     *
//     * @param name  分类名称
//     * @param level 层级（1-行业类型，2-职位类型，3-职位大类）
//     * @return 唯一匹配的分类节点
//     * @throws com.wisematch.common.exception.RRException 当查询结果不唯一或不存在时抛出
//     */
//    @Override
//    public IndustryJobCategory getUniqueByLevelAndName(String name, Integer level) {
//        // 构建查询条件
//        LambdaQueryWrapper<IndustryJobCategory> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(IndustryJobCategory::getName, name)
//                .eq(IndustryJobCategory::getLevel, level);
//
//        // 执行查询
//        List<IndustryJobCategory> resultList = baseMapper.selectList(queryWrapper);
//
//        // 处理查询结果
//        if (resultList.isEmpty()) {
//            throw new RRException(RRExceptionEnum.DATA_NOT_EXISTS);
//        }
//        if (resultList.size() > 1) {
//            throw new RRException("数据添加重复");
//        }
//
//        return resultList.get(0);
//    }
//
//    /**
//     * 查询所有分类并组装成树形结构
//     * @return 树形结构VO列表
//     */
//    public List<IndustryJobCategoryTreeDTO> listTree() {
//        // 查询所有分类数据，按排序号升序排列
//        List<IndustryJobCategory> allCategories = baseMapper.selectList(
//                new LambdaQueryWrapper<IndustryJobCategory>()
//                        .orderByAsc(IndustryJobCategory::getSortOrder)
//        );
//
//        // 从顶级节点（parentId=0）开始构建树形结构
//        return buildTree(allCategories, 0L);
//    }
//
//    /**
//     * 递归构建树形结构
//     * @param allCategories 所有分类数据
//     * @param parentId 父级ID，用于筛选子节点
//     * @return 树形结构VO列表
//     */
//    private List<IndustryJobCategoryTreeDTO> buildTree(List<IndustryJobCategory> allCategories, Long parentId) {
//        // 筛选出父ID为当前parentId的所有节点，并转换为VO
//        return allCategories.stream()
//                .filter(category -> category.getParentId().equals(parentId))
//                .map(category -> {
//                    IndustryJobCategoryTreeDTO vo = new IndustryJobCategoryTreeDTO();
//                    vo.setName(category.getName());
//                    // 递归查询子节点
//                    vo.setChildren(buildTree(allCategories, category.getId()));
//                    return vo;
//                })
//                .toList();
//    }
//}