package com.wisematch.modules.common.controller;


import com.wisematch.common.utils.R;
import com.wisematch.modules.chat.enums.DictTypeEnum;
import com.wisematch.modules.common.service.AiSysConfigService;
import com.wisematch.modules.sys.annotation.Anonymous;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/sys/config")
@RequiredArgsConstructor
@Tag(name = "字典配置", description = "字典配置")
public class AiSysConfigController {

    private final AiSysConfigService aiSysConfigService;

    @GetMapping("/get")
    @Operation(summary = "字典获取")
    @Anonymous
    public R getByKey(@RequestParam DictTypeEnum dictTypeEnum) {
        return R.ok().setData(aiSysConfigService.selectConfigByKey(dictTypeEnum.name()));
    }


    @GetMapping("/getMulti")
    @Operation(summary = "字典获取（多个）")
    @Anonymous
    public R getMulti(@RequestParam DictTypeEnum[] dictTypeEnum) {
        return R.ok().setData(aiSysConfigService.selectConfigByKeyMulti(dictTypeEnum));
    }

}

