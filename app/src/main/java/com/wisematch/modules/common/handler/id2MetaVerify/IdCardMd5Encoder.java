package com.wisematch.modules.common.handler.id2MetaVerify;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.regex.Pattern;

public class IdCardMd5Encoder {

    /**
     * 提取身份证中间8位年月日并进行MD5编码
     * @param idCard 18位身份证号码
     * @return MD5编码结果(32位小写)
     * @throws IllegalArgumentException 如果身份证格式不正确
     */
    public static String encodeBirthdayToMd5(String idCard) {
        // 基础校验
        validateIdCard(idCard);

        // 提取出生年月日部分(第7-14位)
        String birthday = extractBirthDay(idCard);

        return md5(birthday);
    }

    public static String extractBirthDay(String idCard) {
        return idCard.substring(6, 14);
    }

    /**
     * 身份证基础校验
     */
    private static void validateIdCard(String idCard) {
        if (idCard == null) {
            throw new IllegalArgumentException("身份证号码不能为null");
        }

        // 长度校验
        if (idCard.length() != 18) {
            throw new IllegalArgumentException("身份证号码必须为18位");
        }

        // 前17位必须是数字
        if (!Pattern.matches("\\d{17}", idCard.substring(0, 17))) {
            throw new IllegalArgumentException("身份证前17位必须为数字");
        }

        // 最后一位可能是数字或X
        char lastChar = idCard.charAt(17);
        if (!(Character.isDigit(lastChar) || lastChar == 'X' || lastChar == 'x')) {
            throw new IllegalArgumentException("身份证最后一位必须是数字或X");
        }
    }

    /**
     * MD5编码方法
     * @param input 输入字符串
     * @return 32位小写MD5
     */
    public static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes());

            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5算法不可用", e);
        }
    }

    public static String encodeIdCardToMd5(String idCard) {

        if(null != idCard){
            String md5 = encodeBirthdayToMd5(idCard);
            return idCard.substring(0, 6)+md5+idCard.substring(14, 18);
        }
        throw new IllegalArgumentException("身份证号码格式错误");
    }


}
