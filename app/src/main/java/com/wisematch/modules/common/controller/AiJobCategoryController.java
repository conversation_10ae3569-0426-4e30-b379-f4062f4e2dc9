package com.wisematch.modules.common.controller;

import com.wisematch.common.utils.R;
import com.wisematch.modules.common.model.GetNLevelCategoryModel;
import com.wisematch.modules.common.service.AiJobCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version AiChatController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/jobCategory")
@Tag(name = "工作类别", description = "工作类别")
public class AiJobCategoryController {

    @Autowired
    private AiJobCategoryService aiJobCategoryService;

    @Operation(summary = "工作某级类别列表")
    @PostMapping("/getNLevelCategory")
    public R getNLevelCategory(@RequestBody GetNLevelCategoryModel param) {
        return R.ok().setData(aiJobCategoryService.getNLevelCategory(param));
    }

}
