package com.wisematch.modules.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wisematch.modules.chat.model.ChatMessage;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;

public class JsonUtils {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    // Java对象 → JSON字符串
    public static String toJson(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("对象转JSON失败", e);
        }
    }

    // JSON字符串 → Java对象
    public static <T> T fromJson(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON解析失败", e);
        }
    }

    private static final ObjectMapper MAPPER = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    /**
     * 把 JSON 转成指定元素类型的 List<T>
     * 支持传入 JSON 数组（"[...]"）或单个对象 ("{...}")。
     *
     * @param json         JSON 字符串
     * @param elementClass 元素类型，例如 Option.class
     * @param <T>          元素泛型
     * @return List<T>
     * @throws Exception 解析异常
     */
    public static <T> List<T> convertToList(String json, Class<T> elementClass) throws Exception {
        if (json == null || json.trim().isEmpty() || elementClass == null) {
            return Collections.emptyList();
        }
        String trimmed = json.trim();
        // 如果是数组
        if (trimmed.startsWith("[")) {
            JavaType listType = MAPPER.getTypeFactory().constructCollectionType(List.class, elementClass);
            return MAPPER.readValue(trimmed, listType);
        } else { // 单个对象，包成 List 返回
            T obj = MAPPER.readValue(trimmed, elementClass);
            return Collections.singletonList(obj);
        }
    }

    /**
     * 把 JSON 转成指定元素类型的数组 T[]
     * 支持传入 JSON 数组（"[...]"）或单个对象 ("{...}")（将返回长度为1的数组）。
     *
     * @param json         JSON 字符串
     * @param elementClass 元素类型，例如 Option.class
     * @param <T>          元素泛型
     * @return T[]
     * @throws Exception 解析异常
     */
    @SuppressWarnings("unchecked")
    public static <T> T[] convertToArray(String json, Class<T> elementClass) throws Exception {
        if (elementClass == null) {
            return null;
        }
        if (json == null || json.trim().isEmpty()) {
            // 返回一个长度为0的数组
            return (T[]) java.lang.reflect.Array.newInstance(elementClass, 0);
        }
        String trimmed = json.trim();
        JavaType arrayType = MAPPER.getTypeFactory().constructArrayType(elementClass);
        Object read = MAPPER.readValue(trimmed, arrayType);
        if (read == null) {
            return (T[]) java.lang.reflect.Array.newInstance(elementClass, 0);
        }
        // readValue 对数组类型会直接返回 T[]，直接强转即可
        return (T[]) read;
    }

    /**
     * 安全解析 JSON 字符串为指定对象
     *
     * @param jsonStr   JSON 字符串
     * @param clazz     目标对象类型
     * @param <T>       返回类型
     * @return          转换后的对象，解析失败返回 null
     */
    public static <T> T parseObjectSafe(String jsonStr, Class<T> clazz) {
        if (StringUtils.isBlank(jsonStr)) {
            return null;
        }
        String trimStr = jsonStr.trim();
        if ("null".equalsIgnoreCase(trimStr)) {
            return null;
        }
        try {
            return JSON.parseObject(trimStr, clazz);
        } catch (Exception e) {
            // 这里可以写日志，比如 log.warn("JSON 解析失败: {}", jsonStr, e);
            return null;
        }
    }
    /**
     * 任意对象转 JSONObject（基于 FastJSON2）
     */
    public static <T> JSONObject toJsonObject(T obj) {
        if (obj == null) {
            return new JSONObject(); // 返回空对象
        }
        return (JSONObject) JSON.toJSON(obj);
    }


    public static String extractJSON(String text) {
        int start = text.indexOf('{');
        if (start == -1) {
            return "";
        }

        int count = 1;
        int end = start + 1;
        while (end < text.length() && count > 0) {
            char c = text.charAt(end);
            if (c == '{') {
                count++;
            } else if (c == '}') {
                count--;
            }
            end++;
        }
        return count == 0 ? text.substring(start, end) : "";
    }

    public static String extractJSONArray(String text) {
        int start = text.indexOf('[');
        if (start == -1) {
            return "";
        }

        int count = 1;
        int end = start + 1;
        while (end < text.length() && count > 0) {
            char c = text.charAt(end);
            if (c == '[') {
                count++;
            } else if (c == ']') {
                count--;
            }
            end++;
        }
        return count == 0 ? text.substring(start, end) : "";
    }

    public static void main(String []s){
        fromJson("",ChatMessage.class);
    }

}
