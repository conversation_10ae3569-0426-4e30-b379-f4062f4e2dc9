package com.wisematch.modules.common.handler.businessLicenseVerify;

import com.wisematch.modules.chat.model.AiOrganizationInsertDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PreVerifyBusinessHandler {


    public AiOrganizationInsertDTO handle(BusinessLicenseContext businessLicenseContext){

        AiOrganizationInsertDTO aiOrganizationInsertDTO = new AiOrganizationInsertDTO();
        aiOrganizationInsertDTO.setBusinessLicense(businessLicenseContext.getFileSrc());
        aiOrganizationInsertDTO.setOrganizationName(businessLicenseContext.getCompanyName());
        aiOrganizationInsertDTO.setUniqueSocialCreditCode(businessLicenseContext.getCreditCode());
        return aiOrganizationInsertDTO;

    }

}
