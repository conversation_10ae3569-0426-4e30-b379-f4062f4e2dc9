package com.wisematch.modules.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wisematch.modules.common.entity.AiCity;
import com.wisematch.modules.common.mapper.AiCityMapper;
import com.wisematch.modules.chat.model.AiCitySelectAllDTO;
import com.wisematch.modules.chat.model.Area;
import com.wisematch.modules.chat.model.CityItem;
import com.wisematch.modules.chat.model.ProvinceItem;
import com.wisematch.modules.common.service.AiCityService;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Service
public class AiCityServiceImpl  extends ServiceImpl<AiCityMapper, AiCity> implements AiCityService {


    @Override
    public void importFromJson(String filePath) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        File file = new File(filePath);
        List<ProvinceItem> wrapper = mapper.readValue(file, new TypeReference<List<ProvinceItem>>() {});
        List<AiCity> aiCities = new ArrayList<>(2000);
        for (ProvinceItem province : wrapper) {
            for (CityItem city : province.getCitys()) {
                for (Area area : city.getAreas()) {
                    AiCity aiCity = new AiCity();
                    aiCity.setProvince(province.getProvince());
                    aiCity.setCity(city.getCity());
                    aiCity.setArea(area.getArea());
                    aiCities.add(aiCity);
                }
            }
        }
        this.saveBatch(aiCities);
    }

    @Override
    public List<AiCity> selectAll(AiCitySelectAllDTO aiCitySelectAllDTO) {
        QueryWrapper<AiCity> queryWrapper = new QueryWrapper<>();
        if (aiCitySelectAllDTO.getProvince() != null){
            queryWrapper.lambda().like(AiCity::getProvince, aiCitySelectAllDTO.getProvince());
        }
        if (aiCitySelectAllDTO.getCity() != null){
            queryWrapper.lambda().like(AiCity::getCity, aiCitySelectAllDTO.getCity());
        }
        if (aiCitySelectAllDTO.getArea() != null){
            queryWrapper.lambda().eq(AiCity::getArea, aiCitySelectAllDTO.getArea());
        }
        return this.list(queryWrapper);
    }

}
