package com.wisematch.modules.common.utils;

import java.security.MessageDigest;

public class Md5Util {

    /**
     * 对字符串进行 MD5 加密，返回 32 位十六进制字符串
     * @param input 要加密的字符串
     * @return md5值
     */
    public static String md5(String input) {
        try {
            // 创建 MD5 加密对象
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 执行加密
            byte[] digest = md.digest(input.getBytes("UTF-8"));

            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                // 每个字节转换为两位的16进制，不足补0
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5加密失败", e);
        }
    }

}

