package com.wisematch.modules.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("ai_college")
@Schema(description = "城市")
public class AiCollege implements Serializable {

    public static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id; // 如果用不到可去掉

    @Schema(description = "学校名称")
    private String collegeName;

    @Schema(description = "学校code")
    private String collegeCode;

    @Schema(description = "所属部门")
    private String collegeDepartment;

    @Schema(description = "所在地区")
    private String collegeLocation;

    @Schema(description = "学校层次")
    private String collegeLevel;

    @Schema(description = "学校类型")
    private String collegeTag;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date createTime;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date updateTime;
}
