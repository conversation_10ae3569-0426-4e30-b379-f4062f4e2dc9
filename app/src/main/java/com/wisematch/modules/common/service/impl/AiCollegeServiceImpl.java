package com.wisematch.modules.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.modules.common.entity.AiCollege;
import com.wisematch.modules.common.mapper.AiCollegeMapper;
import com.wisematch.modules.chat.model.AiCollegeCodeDTO;
import com.wisematch.modules.chat.model.AiCollegeSelectAllDTO;
import com.wisematch.modules.common.service.AiCollegeService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AiCollegeServiceImpl extends ServiceImpl<AiCollegeMapper, AiCollege> implements AiCollegeService {

    @Override
    public List<AiCollege> selectAll(AiCollegeSelectAllDTO aiCitySelectAllDTO) {
        QueryWrapper<AiCollege> queryWrapper = new QueryWrapper<>();
        if (aiCitySelectAllDTO.getCollegeLevel() != null){
            queryWrapper.lambda().eq(AiCollege::getCollegeLevel, aiCitySelectAllDTO.getCollegeLevel());
        }
        if (aiCitySelectAllDTO.getCollegeLocation() != null){
            queryWrapper.lambda().like(AiCollege::getCollegeLocation, aiCitySelectAllDTO.getCollegeLocation());
        }
        if (aiCitySelectAllDTO.getCollegeName() != null){
            queryWrapper.lambda().like(AiCollege::getCollegeName, aiCitySelectAllDTO.getCollegeName());
        }
        if (aiCitySelectAllDTO.getCollegeDepartment() != null){
            queryWrapper.lambda().eq(AiCollege::getCollegeDepartment, aiCitySelectAllDTO.getCollegeDepartment());
        }
        if (aiCitySelectAllDTO.getCollegeTag() != null){
            queryWrapper.lambda().eq(AiCollege::getCollegeTag, aiCitySelectAllDTO.getCollegeTag());
        }
        return this.list(queryWrapper);
    }

    @Override
    public AiCollege getByCode(AiCollegeCodeDTO aiCollegeCodeDTO) {
        return this.getOne(new QueryWrapper<AiCollege>().lambda().eq(AiCollege::getCollegeCode, aiCollegeCodeDTO.getCollegeCode()));
    }

}
