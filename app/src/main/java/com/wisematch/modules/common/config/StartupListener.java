package com.wisematch.modules.common.config;

import com.wisematch.modules.common.service.AiSysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class StartupListener implements ApplicationListener<ApplicationReadyEvent> {

    @Autowired
    AiSysConfigService aiSysConfigService;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        Boolean sysLogOpen = aiSysConfigService.isLogOpen();
        log.info("应用启动之后是否记录日志："+sysLogOpen);
    }
}

