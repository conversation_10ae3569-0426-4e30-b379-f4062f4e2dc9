package com.wisematch.modules.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.modules.common.entity.AiJobCategory;
import com.wisematch.modules.common.model.GetNLevelCategoryModel;
import com.wisematch.modules.common.mapper.AiJobCategoryMapper;
import com.wisematch.modules.common.service.AiJobCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 智能体池
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiJobCategoryServiceImpl extends ServiceImpl<AiJobCategoryMapper, AiJobCategory> implements AiJobCategoryService {

    @Override
    public List<AiJobCategory> getNLevelCategory(GetNLevelCategoryModel param) {
        LambdaQueryWrapper<AiJobCategory> queryWrapper = new LambdaQueryWrapper<AiJobCategory>()
                .eq(AiJobCategory::getLevel, param.getLevel());

        queryWrapper.like(StringUtils.isNotBlank(param.getParentId()), AiJobCategory::getParentId, param.getParentId());

        queryWrapper.like(null != param.getName(), AiJobCategory::getName, param.getName());

        return this.baseMapper.selectList(queryWrapper);
    }
}
