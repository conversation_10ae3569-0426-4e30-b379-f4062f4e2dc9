package com.wisematch.modules.common.controller;

import com.wisematch.common.utils.R;
import com.wisematch.modules.chat.model.AiCollegeCodeDTO;
import com.wisematch.modules.chat.model.AiCollegeSelectAllDTO;
import com.wisematch.modules.chat.model.IdRequest;
import com.wisematch.modules.common.service.AiCollegeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/aiCollege")
@Tag(name = "学校", description = "学校")
public class AiCollegeController {

    @Autowired
    private AiCollegeService aiCollegeService;

    @PostMapping("/detailById")
    @Operation(summary = "id获取详情")
    public R detail(@RequestBody IdRequest idRequest){
        return R.ok().setData(aiCollegeService.getById(idRequest.getId()));
    }

    @PostMapping("/selectAll")
    @Operation(summary = "查询菜单")
    public R selectAll(@RequestBody AiCollegeSelectAllDTO aiCollegeSelectAllDTO){
        return R.ok().setData(aiCollegeService.selectAll(aiCollegeSelectAllDTO));
    }

    @PostMapping("/detailByCode")
    @Operation(summary = "code获取详情")
    public R detail(@RequestBody AiCollegeCodeDTO aiCollegeCodeDTO){
        return R.ok().setData(aiCollegeService.getByCode(aiCollegeCodeDTO));
    }

}

