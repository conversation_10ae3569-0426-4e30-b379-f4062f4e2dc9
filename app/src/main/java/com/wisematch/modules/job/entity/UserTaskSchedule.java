package com.wisematch.modules.job.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 用户任务调度表实体类
 */
@Data
@TableName("user_task_schedule")
public class UserTaskSchedule {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 任务标识ID
     */
    private String roomId;

    /**
     * 调度类型
     */
    private String taskType;

    /**
     * 最大调度次数限制
     */
    private Integer maxScheduleCount;

    /**
     * 当前已调度次数
     */
    private Integer currentScheduleCount;

    /**
     * 任务状态: ENABLE-可调度, DISABLE-不可调度, PENDING-调度中
     */
    private String status;
    /**
     * 最后一次调度时间
     */
    private LocalDateTime lastScheduleTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
