package com.wisematch.modules.job.enums;

import lombok.Getter;

/**
 * 任务状态枚举
 */
@Getter
public enum TaskTypeEnum {

    /**
     * 可调度状态
     */
    REPORT_PORTRAIT("REPORT_PORTRAIT", "报告、人才画像和人才知识库");

    /**
     * 数据库存储值
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;

    TaskTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举实例
     */
    public static TaskTypeEnum getByCode(String code) {
        for (TaskTypeEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }
}
