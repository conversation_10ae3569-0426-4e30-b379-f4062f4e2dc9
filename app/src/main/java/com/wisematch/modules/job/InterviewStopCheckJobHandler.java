package com.wisematch.modules.job;

import com.wisematch.modules.chat.agent.RoomAgentFacade;
import com.wisematch.modules.chat.entity.AiViewRecord;
import com.wisematch.modules.chat.service.AiViewRecordService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version AgentNoticeTask.java, v0.1 2025-07-23 14:18
 */
@Component
@Slf4j
public class InterviewStopCheckJobHandler {
    @Autowired
    private AiViewRecordService aiViewRecordService;
    @Autowired
    private RoomAgentFacade roomAgentFacade;

    @XxlJob("roomStatusCheck")
    private void roomCheckTask(){
        //停止面试时长
        int stopSeconds = 60 * 3;
        Date current = new Date();
        List<AiViewRecord> list = aiViewRecordService.getChattingRoom();
        if (!list.isEmpty()) {
            log.info("room check task run, chatting room size:{}", list.size());
        }
        list.forEach(x -> {
            if ((current.getTime() - x.getUpdateTime().getTime()) / 1000 > stopSeconds) {
                // 面试静默超时，停止面试
                XxlJobHelper.log("面试静默超时, 停止面试, roomId={}, current={}, updateTime={}", x.getRoomId(), current.getTime(), x.getUpdateTime());
                roomAgentFacade.stopRoomAgent(x.getRoomId());
                log.info("chatting room stop, roomId = {}", x.getRoomId());
            }
        });
    }

}
