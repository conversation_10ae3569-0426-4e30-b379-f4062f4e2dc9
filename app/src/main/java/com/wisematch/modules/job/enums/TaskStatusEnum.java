package com.wisematch.modules.job.enums;

import lombok.Getter;

/**
 * 任务状态枚举
 */
@Getter
public enum TaskStatusEnum {

    /**
     * 启用状态 - 待调度
     */
    ENABLE("ENABLE", "启用"),

    /**
     * 执行中状态 - 防止重复调度
     */
    PENDING("PENDING", "执行中"),

    /**
     * 禁用状态 - 任务已完成
     */
    DISABLE("DISABLE", "已完成"),

    /**
     * 失败状态 - 任务执行失败且已达最大重试次数
     */
    FAILED("FAILED", "执行失败");

    private final String code;
    private final String desc;

    TaskStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     */
    public static TaskStatusEnum getByCode(String code) {
        for (TaskStatusEnum statusEnum : TaskStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * 判断是否为可调度状态
     */
    public static boolean isSchedulable(String code) {
        return ENABLE.getCode().equals(code);
    }

    /**
     * 判断是否为终态（不再调度）
     */
    public static boolean isFinalState(String code) {
        return DISABLE.getCode().equals(code) || FAILED.getCode().equals(code);
    }
}