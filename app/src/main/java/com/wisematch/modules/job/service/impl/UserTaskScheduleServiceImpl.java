package com.wisematch.modules.job.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.modules.job.entity.UserTaskSchedule;
import com.wisematch.modules.job.enums.TaskStatusEnum;
import com.wisematch.modules.job.mapper.UserTaskScheduleMapper;
import com.wisematch.modules.job.service.UserTaskScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
public class UserTaskScheduleServiceImpl extends ServiceImpl<UserTaskScheduleMapper, UserTaskSchedule>
        implements UserTaskScheduleService {

    @Override
    public void incrementScheduleCount(String id, String targetStatus) {
        // 使用乐观锁更新，防止并发问题
        LambdaUpdateWrapper<UserTaskSchedule> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserTaskSchedule::getId, id)
                // 条件：当前状态必须为PENDING（确保是当前调度器处理的任务）
                .eq(UserTaskSchedule::getStatus, TaskStatusEnum.PENDING.getCode())
                // 自增调度次数
                .setSql("current_schedule_count = current_schedule_count + 1")
                // 更新最后调度时间
                .set(UserTaskSchedule::getLastScheduleTime, LocalDateTime.now())
                // 更新为目标状态
                .set(UserTaskSchedule::getStatus, targetStatus)
                // 更新修改时间
                .set(UserTaskSchedule::getUpdateTime, LocalDateTime.now());

        // 执行更新
        int rows = baseMapper.update(null, updateWrapper);
        if (rows == 0) {
            log.warn("更新任务调度次数失败，任务ID:{}，目标状态:{}，可能已被其他进程修改", id, targetStatus);
        } else {
            log.debug("成功更新任务调度次数，任务ID:{}，目标状态:{}", id, targetStatus);
        }
    }

    @Override
    public void updateStatus(String id, String oldCode, String newCode) {
        LambdaUpdateWrapper<UserTaskSchedule> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserTaskSchedule::getId, id)
                .eq(UserTaskSchedule::getStatus, oldCode)  // 确保状态是预期的旧状态
                .set(UserTaskSchedule::getStatus, newCode)
                .set(UserTaskSchedule::getUpdateTime, LocalDateTime.now());

        int rows = baseMapper.update(null, updateWrapper);
        if (rows == 0) {
            log.warn("更新任务状态失败，任务ID:{}，旧状态:{}，新状态:{}", id, oldCode, newCode);
        } else {
            log.debug("成功更新任务状态，任务ID:{}，旧状态:{}，新状态:{}", id, oldCode, newCode);
        }
    }

    @Override
    public boolean updateStatusBatch(List<String> taskIds, String oldCode, String newCode) {
        if (taskIds == null || taskIds.isEmpty()) {
            return true;
        }

        LambdaUpdateWrapper<UserTaskSchedule> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(UserTaskSchedule::getId, taskIds)
                .eq(UserTaskSchedule::getStatus, oldCode)  // 仅更新指定旧状态的任务
                .set(UserTaskSchedule::getStatus, newCode)
                .set(UserTaskSchedule::getUpdateTime, LocalDateTime.now());

        int rows = baseMapper.update(null, updateWrapper);
        boolean success = rows == taskIds.size();

        if (!success) {
            log.warn("批量更新任务状态部分失败，期望更新数量:{}，实际更新数量:{}，旧状态:{}，新状态:{}",
                    taskIds.size(), rows, oldCode, newCode);
        } else {
            log.debug("成功批量更新任务状态，更新数量:{}，旧状态:{}，新状态:{}", rows, oldCode, newCode);
        }

        // 返回实际更新的行数是否与传入的任务数一致
        return success;
    }

    @Override
    public List<UserTaskSchedule> listPendingTasks(String taskTypeCode, String statusCode, int batchSize) {
        LambdaQueryWrapper<UserTaskSchedule> queryWrapper = new LambdaQueryWrapper<>();

        // 计算6小时前的时间
        LocalDateTime sixHoursAgo = LocalDateTime.now().minusHours(6);

        queryWrapper.eq(UserTaskSchedule::getTaskType, taskTypeCode)
                .eq(UserTaskSchedule::getStatus, statusCode)
                .ge(UserTaskSchedule::getCreateTime, sixHoursAgo)
                .apply("current_schedule_count < max_schedule_count")
                // 添加时间范围条件：只查询6小时内创建的任务
                .orderByAsc(UserTaskSchedule::getCreateTime)
                .last("LIMIT " + batchSize);

        List<UserTaskSchedule> tasks = baseMapper.selectList(queryWrapper);
        log.debug("查询到待处理任务数量:{}，任务类型:{}，状态:{}", tasks.size(), taskTypeCode, statusCode);

        return tasks;
    }
}