package com.wisematch.modules.job.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.job.entity.UserTaskSchedule;

import java.util.List;

public interface UserTaskScheduleService extends IService<UserTaskSchedule> {

    void incrementScheduleCount(String id, String code);

    void updateStatus(String id, String oldCode, String newCode);

    boolean updateStatusBatch(List<String> taskIds, String oldCode, String newCode);

    List<UserTaskSchedule> listPendingTasks(String taskTypeCode, String statusCode, int batchSize);
}
