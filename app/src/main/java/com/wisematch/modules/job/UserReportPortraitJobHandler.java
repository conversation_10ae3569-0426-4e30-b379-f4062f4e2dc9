package com.wisematch.modules.job;

import com.wisematch.common.utils.ReportThreadPoolUtil;
import com.wisematch.modules.chat.entity.AiNotifyMessage;
import com.wisematch.modules.chat.entity.AiViewPortrait;
import com.wisematch.modules.chat.entity.AiViewRecord;
import com.wisematch.modules.chat.enums.ApplyType;
import com.wisematch.modules.chat.factories.notifyMessage.NotifyMessageFactories;
import com.wisematch.modules.chat.service.*;
import com.wisematch.modules.job.entity.UserTaskSchedule;
import com.wisematch.modules.job.enums.TaskStatusEnum;
import com.wisematch.modules.job.enums.TaskTypeEnum;
import com.wisematch.modules.job.service.UserTaskScheduleService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UserReportPortraitJobHandler implements IJobHandler {

    @Autowired
    private AiViewReportService chatReportService;

    @Autowired
    private AiViewPortraitService aiViewPortraitService;

    @Autowired
    private AiViewRecordService aiViewRecordService;

    @Resource
    private GeekRecommendMilvusStoreService geekRecommendMilvusStoreService;

    @Resource
    private UserTaskScheduleService userTaskScheduleService;

    @Resource
    private AiNotifyMessageService aiNotifyMessageService;

    /**
     * 单个任务执行，一般用于手动
     */
    @XxlJob("userReportPortraitJobSingle")
    @Override
    public void singleJob() {
        String roomId = XxlJobHelper.getJobParam();
        XxlJobHelper.log("自定义入参:" + roomId);
        processSingleTask(roomId, 0, 1);
    }

    /**
     * 批量调度任务执行
     */
    @Override
    @XxlJob("userReportPortraitJobMulti")
    public void scheduleBatchJob() {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("自定义入参:" + param);

        // 解析参数：批量大小
        int batchSize = Integer.parseInt(param);

        // 1. 查询符合条件的任务：可调度状态且未达最大次数
        List<UserTaskSchedule> pendingTasks = userTaskScheduleService.listPendingTasks(
                TaskTypeEnum.REPORT_PORTRAIT.getCode(),
                TaskStatusEnum.ENABLE.getCode(),
                batchSize
        );

        XxlJobHelper.log("本次调度任务数量: {}", pendingTasks.size());
        if (pendingTasks.isEmpty()) {
            return;
        }

        // 2. 将任务状态更新为PENDING，防止重复调度
        List<String> taskIds = pendingTasks.stream()
                .map(UserTaskSchedule::getId)
                .collect(Collectors.toList());

        boolean lockSuccess = userTaskScheduleService.updateStatusBatch(
                taskIds,
                TaskStatusEnum.ENABLE.getCode(),
                TaskStatusEnum.PENDING.getCode()
        );

        if (!lockSuccess) {
            XxlJobHelper.log("任务状态更新失败，可能已被其他调度器处理");
            return;
        }

        // 3. 并发处理任务
        pendingTasks.forEach(task -> ReportThreadPoolUtil.execute(() -> {
            boolean success = false;
            try {
                success = processSingleTask(task.getRoomId(), task.getCurrentScheduleCount(), task.getMaxScheduleCount());
                log.info("任务处理完成, roomId: {}, success: {}", task.getRoomId(), success);
            } catch (Exception e) {
                log.error("处理任务异常, roomId: {}", task.getRoomId(), e);
                success = false;
            } finally {
                // 关键：无论成功失败都要增加调度次数
                handleTaskCompletion(task, success);
            }
        }));

        // 4. 任务已成功锁定并提交执行，调度器可以立即返回
        XxlJobHelper.handleSuccess("批量任务调度完成，已提交" + pendingTasks.size() + "个任务到后台执行");

    }

    /**
     * 处理任务完成后的状态更新逻辑
     * @param task 任务对象
     * @param success 是否执行成功
     */
    private void handleTaskCompletion(UserTaskSchedule task, boolean success) {
        try {
            if (success) {
                // 任务成功：增加调度次数并设置为DISABLE（完成状态）
                userTaskScheduleService.incrementScheduleCount(
                        task.getId(),
                        TaskStatusEnum.DISABLE.getCode()
                );
                log.info("任务执行成功，已标记为完成, taskId: {}, roomId: {}", task.getId(), task.getRoomId());
            } else {
                // 任务失败：增加调度次数，判断是否还能重试
                int currentCount = task.getCurrentScheduleCount() + 1; // 当前次数+1

                if (currentCount >= task.getMaxScheduleCount()) {
                    // 已达最大重试次数，标记为FAILED
                    userTaskScheduleService.incrementScheduleCount(
                            task.getId(),
                            TaskStatusEnum.FAILED.getCode()
                    );
                    log.warn("任务执行失败且已达最大重试次数，标记为失败, taskId: {}, roomId: {}, 重试次数: {}/{}",
                            task.getId(), task.getRoomId(), currentCount, task.getMaxScheduleCount());
                } else {
                    // 还可以重试，增加次数并恢复为ENABLE状态
                    userTaskScheduleService.incrementScheduleCount(
                            task.getId(),
                            TaskStatusEnum.ENABLE.getCode()
                    );
                    log.info("任务执行失败，增加重试次数并恢复为可调度状态, taskId: {}, roomId: {}, 重试次数: {}/{}",
                            task.getId(), task.getRoomId(), currentCount, task.getMaxScheduleCount());
                }
            }
        } catch (Exception e) {
            log.error("处理任务完成状态更新异常, taskId: {}, roomId: {}", task.getId(), task.getRoomId(), e);
            // 如果状态更新失败，尝试恢复为ENABLE状态（保守策略）
            try {
                userTaskScheduleService.updateStatus(
                        task.getId(),
                        TaskStatusEnum.PENDING.getCode(),
                        TaskStatusEnum.ENABLE.getCode()
                );
            } catch (Exception ex) {
                log.error("恢复任务状态失败, taskId: {}", task.getId(), ex);
            }
        }
    }

    /**
     * 处理单个任务的核心逻辑
     */
    private boolean processSingleTask(String roomId, Integer currentRunCount, Integer maxRunCount) {
        try {
            AiViewRecord aiViewRecord = aiViewRecordService.getByRoomId(roomId);
            if (aiViewRecord == null) {
                log.warn("未找到对应的记录, roomId: {}", roomId);
                return false;
            }

            String userId = aiViewRecord.getUserId();

            // 生成报告
            boolean reportGenerated = chatReportService.reportSchedule(aiViewRecord, currentRunCount, maxRunCount);
            if (!reportGenerated) {
                log.info("报告生成未完成, roomId: {}", roomId);
                return false;
            }

            // 生成人才画像
            boolean portraitGenerated = aiViewPortraitService.generatePortraitSchedule(roomId);
            if (!portraitGenerated) {
                log.info("人才画像生成未完成, roomId: {}", roomId);
                return false;
            }

            // 如果是职位申请类型，执行后续操作
            if (ApplyType.POSITION.name().equals(aiViewRecord.getChatType())) {
                geekRecommendMilvusStoreService.store(roomId);

                // 加入人才池并发送通知
                AiViewPortrait aiViewPortrait = aiViewPortraitService.getByRoomId(roomId);
                if (aiViewPortrait != null) {
                    AiNotifyMessage aiNotifyMessage = NotifyMessageFactories.joinTalentPool(
                            userId,
                            aiViewPortrait.getId()
                    );
                    aiNotifyMessage.setPosition(aiViewPortrait.getPosition());
                    aiNotifyMessageService.save(aiNotifyMessage);
                }
            }

            return true;

        } catch (Exception e) {
            log.error("处理单个任务异常, roomId: {}", roomId, e);
            return false;
        }
    }
}