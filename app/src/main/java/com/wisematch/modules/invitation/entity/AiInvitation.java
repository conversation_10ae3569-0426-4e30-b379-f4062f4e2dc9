package com.wisematch.modules.invitation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

@Data
@TableName("ai_invitation")
@Schema(description = "邀请信息表")
public class AiInvitation {

    @Schema(description = "题目")
    private String title;

    @Schema(description = "内容")
    private String content;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description = "主键")
    private String id;

    @Schema(description = "所属活动ID")
    private Long sourceId;

    @Schema(description = "发布邀请的用户ID")
    private String inviterUserId;

    @Schema(description = "场景")
    // 表是 ENUM('SYSTEM')，Java中用String即可
    private String bizSence;

    @Schema(description = "邀请类型(邀请码/短链/二维码标识)")
    private String inviteType;

    @Schema(description = "邀请唯一标识")
    private String inviteCode;

    @Schema(description = "最大使用次数，NULL表示无限制")
    private Integer maxUses;

    @Schema(description = "活动状态")
    // ENUM('ACTIVE','EXPIRED')，Java中用String
    private String status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间")
    private Date createdTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "过期时间，可为空")
    private Date expiredTime;

    @Schema(description = "额外信息")
    // longtext 对应 String
    private String extraInfo;

    @Schema(description = "已经使用次数")
    private Integer usedCount;
}

