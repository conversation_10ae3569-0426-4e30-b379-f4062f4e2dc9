package com.wisematch.modules.invitation.controller;

import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.modules.invitation.service.AiInvitationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version AiChatController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/invitation")
@Tag(name = "邀请", description = "邀请")
public class AiInvitationController {

    @Autowired
    private AiInvitationService aiInvitationService;

    @Operation(summary = "邀请列表")
    @GetMapping("/list")
    @NotDoubleSubmit
    public R list() {
        return R.ok().setData(aiInvitationService.list());
    }


//    @Operation(summary = "生成邀请码")
//    @GetMapping("/generatePilotEnterpriseUser")
//    @Anonymous
//    @NotDoubleSubmit
//    public R generatePilotEnterpriseUser() {
//        //PILOT_ENTERPRISE_USER
//        aiInvitationService
//                .generatePilotEnterpriseUser(
//                        new GeneratePilotEnterpriseUserDTO(30, DataUtils.getDefaultExpireTime(30), 10));
//        return R.ok();
//    }

}
