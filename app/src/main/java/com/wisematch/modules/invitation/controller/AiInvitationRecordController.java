package com.wisematch.modules.invitation.controller;

import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.modules.invitation.service.AiInvitationRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version AiChatController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/invitationRecord")
@Tag(name = "邀请记录", description = "邀请记录")
public class AiInvitationRecordController {

    @Autowired
    private AiInvitationRecordService aiInvitationRecordService;

    @Operation(summary = "邀请记录列表")
    @GetMapping("/list")
    @NotDoubleSubmit
    public R list() {
        return R.ok().setData(aiInvitationRecordService.list());
    }


}
