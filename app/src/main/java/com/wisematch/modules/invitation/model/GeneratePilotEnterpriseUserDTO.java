package com.wisematch.modules.invitation.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GeneratePilotEnterpriseUserDTO {


    @Schema(description = "最大使用次数，NULL表示无限制")
    private Integer maxUses = 1;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "过期时间，可为空")
    private Date expiredTime;


    @Schema(description = "生成的数量")
    private Integer generateNumber;

}
