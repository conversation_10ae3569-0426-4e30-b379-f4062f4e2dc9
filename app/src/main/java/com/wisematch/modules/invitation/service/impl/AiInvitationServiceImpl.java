package com.wisematch.modules.invitation.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.common.service.AiSysConfigService;
import com.wisematch.modules.invitation.entity.AiInvitation;
import com.wisematch.modules.invitation.entity.AiInvitationRecord;
import com.wisematch.modules.invitation.enums.AiInvitationSence;
import com.wisematch.modules.invitation.enums.AiInvitationStatus;
import com.wisematch.modules.invitation.enums.InvitationRecordStatus;
import com.wisematch.modules.invitation.enums.InvitationType;
import com.wisematch.modules.invitation.mapper.AiInvitationMapper;
import com.wisematch.modules.invitation.model.GeneratePilotEnterpriseUserDTO;
import com.wisematch.modules.invitation.service.AiInvitationRecordService;
import com.wisematch.modules.invitation.service.AiInvitationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 智能体池
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiInvitationServiceImpl extends ServiceImpl<AiInvitationMapper, AiInvitation> implements AiInvitationService {

    private static final String CHAR_POOL = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final SecureRandom RANDOM = new SecureRandom();

    @Autowired
    AiInvitationRecordService aiInvitationRecordService;

    @Autowired
    AiSysConfigService aiSysConfigService;

    @Transactional
    @Override
    public boolean consumeInvitation(String inviteCode, String userId) {
        AiInvitation one = this.getInviteCode(inviteCode, userId);

        if(null != one && one.getUsedCount() < one.getMaxUses()){
            one.setUsedCount(one.getUsedCount()+1);
            this.updateById(one);
            AiInvitationRecord record = new AiInvitationRecord();
            record.setContent(one.getContent());
            record.setTitle(one.getTitle());
            record.setInvitationId(one.getId());
            record.setInviteeUserId(userId);
            record.setBizStatus(InvitationRecordStatus.USED.name());
            record.setUsedTime(new Date());
            aiInvitationRecordService.save(record);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean consumeInvitation(AiInvitation one) {
        if(null != one && one.getUsedCount() < one.getMaxUses()){
            one.setUsedCount(one.getUsedCount()+1);
            this.updateById(one);
            AiInvitationRecord record = new AiInvitationRecord();
            record.setContent(one.getContent());
            record.setTitle(one.getTitle());
            record.setInvitationId(one.getId());
            record.setInviteeUserId(UserInfoUtils.getCurrentUserId());
            record.setBizStatus(InvitationRecordStatus.USED.name());
            record.setUsedTime(new Date());
            aiInvitationRecordService.save(record);
            return true;
        }
        return false;
    }


    @Override
    public void consumeInvitationEx(AiInvitation one) {
        if(!consumeInvitation(one)){
            throw new RRException(RRExceptionEnum.INVITATION_VALID);
        }
    }

    @Override
    public AiInvitation judgeInviteCode(String invitationCode, String userId) {

        AiInvitation inviteCode = this.getInviteCode(invitationCode, UserInfoUtils.getCurrentUserId());
        if(null == inviteCode){
            throw new RRException(RRExceptionEnum.INVITATION_VALID);
        }else {
            return inviteCode;
        }
    }

    @Override
    public Boolean ifInvite() {
        JSONObject sysConfig = aiSysConfigService.getSysConfig();
        Boolean invitationCodeStatus = sysConfig.getBoolean("invitation_code_status");
        return invitationCodeStatus != null ? invitationCodeStatus : true;
    }

    @Override
    public void use(String inviteCode) {

        if(this.ifInvite()){
            AiInvitation aiInvitation = this.judgeInviteCode(inviteCode, UserInfoUtils.getCurrentUserId());
            long consumeCount = aiInvitationRecordService.consumeCount(UserInfoUtils.getCurrentUserId(), aiInvitation.getId());
            if(consumeCount == 0){
                this.consumeInvitationEx(aiInvitation);
            }
        }
    }

    @Override
    public AiInvitation getInviteCode(String inviteCode, String userId) {

        LambdaQueryWrapper<AiInvitation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiInvitation::getInviteCode, inviteCode);
        return this.getOne(queryWrapper);
    }




    @Override
    public void generatePilotEnterpriseUser(GeneratePilotEnterpriseUserDTO generatePilotEnterpriseUserDTO) {
        List<AiInvitation> aiInvitations = new ArrayList<>();
        for (int i = 0; i < generatePilotEnterpriseUserDTO.getGenerateNumber(); i++) {
            AiInvitation aiInvitation = new AiInvitation();
            aiInvitation.setTitle("先行体验用户邀请");
            aiInvitation.setContent("先行体验用户邀请");

            aiInvitation.setInviteType(InvitationType.INVITATION_CODE.name());
            aiInvitation.setBizSence(AiInvitationSence.PILOT_ENTERPRISE_USER.name());
            aiInvitation.setCreatedTime(new Date());
            aiInvitation.setMaxUses(generatePilotEnterpriseUserDTO.getMaxUses());
            aiInvitation.setStatus(AiInvitationStatus.ACTIVE.name());
            aiInvitation.setCreatedTime(new Date());
            aiInvitation.setExpiredTime(generatePilotEnterpriseUserDTO.getExpiredTime());
            aiInvitation.setInviteCode(generateVerifyCode());
            aiInvitations.add(aiInvitation);
        }
        this.saveBatch(aiInvitations);
    }



    /**
     * 生成 6 位数的随机验证码（字母 + 数字组合）
     * @return 随机验证码
     */
    public static String generateVerifyCode() {
        StringBuilder sb = new StringBuilder(6);
        for (int i = 0; i < 6; i++) {
            int index = RANDOM.nextInt(CHAR_POOL.length());
            sb.append(CHAR_POOL.charAt(index));
        }
        return sb.toString();
    }

}
