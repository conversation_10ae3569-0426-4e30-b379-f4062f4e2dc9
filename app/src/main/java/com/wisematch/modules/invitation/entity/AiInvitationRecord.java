package com.wisematch.modules.invitation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ai_invitation_records")
@Schema(description = "邀请记录表")
public class AiInvitationRecord {

    @Schema(description = "题目")
    private String title;

    @Schema(description = "内容")
    private String content;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description = "主键")
    private String id;

    @Schema(description = "对应 invitations.id")
    private String invitationId;

    @Schema(description = "被邀请人用户ID")
    private String inviteeUserId;

    @Schema(description = "业务状态")
    private String bizStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "使用时间")
    private Date usedTime;

    @Schema(description = "渠道、设备、活动阶段等额外信息")
    // JSON 类型在实体类中通常用 String 或 Map，根据需要可改
    private String extraInfo;

    @Schema(description = "邀请类型(邀请码/短链/二维码标识)")
    private String inviteType;
}

