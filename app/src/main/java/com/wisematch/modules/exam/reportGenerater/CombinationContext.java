package com.wisematch.modules.exam.reportGenerater;

import com.wisematch.modules.exam.model.CombinationResult;

import java.util.List;

public class CombinationContext {
    private CombinationStrategy strategy;

    public CombinationContext(CombinationStrategy strategy) {
        this.strategy = strategy;
    }

    public void setStrategy(CombinationStrategy strategy) {
        this.strategy = strategy;
    }

    public CombinationResult executeStrategy(List<String[]> combinations, List<String> sequence) {
        return strategy.calculate(combinations, sequence);
    }
}

