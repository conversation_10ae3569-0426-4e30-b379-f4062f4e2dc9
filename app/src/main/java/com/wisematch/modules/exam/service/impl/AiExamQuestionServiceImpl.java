package com.wisematch.modules.exam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.modules.exam.entity.AiExamQuestion;
import com.wisematch.modules.exam.entity.AiExamUser;
import com.wisematch.modules.exam.mapper.AiExamQuestionMapper;
import com.wisematch.modules.exam.service.AiExamQuestionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AiExamQuestionServiceImpl extends ServiceImpl<AiExamQuestionMapper, AiExamQuestion> implements AiExamQuestionService {

    @Override
    public List<AiExamQuestion> paperQuestion(Long paperId) {
        return this.list(new LambdaQueryWrapper<AiExamQuestion>().eq(AiExamQuestion::getPaperId, paperId));
    }

    @Override
    public Long paperQuestionNum(Long paperId) {
        return this.count(new LambdaQueryWrapper<AiExamQuestion>().eq(AiExamQuestion::getPaperId, paperId));
    }

    @Override
    public Long paperQuestionNum(AiExamUser ownLatest, Long paperId) {

        if(null != ownLatest){
            return this.paperQuestionNum(ownLatest.getBankId());
        }else {
            return this.paperQuestionNum(paperId);
        }
    }
}

