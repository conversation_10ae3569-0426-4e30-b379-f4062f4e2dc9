package com.wisematch.modules.exam.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wisematch.common.model.PageDTO;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.model.MbtiInfo;
import com.wisematch.modules.exam.entity.AiExamUser;
import com.wisematch.modules.exam.model.AiExamBankFrontDTO;
import com.wisematch.modules.exam.model.AiExamUserAddDTO;
import com.wisematch.modules.exam.model.AnswerQuestionDTO;

import java.util.List;


/**
 * AI面试准备服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */

public interface AiAnswerQuestionService{

    Page<CardInfo> userExamList(PageDTO pageDTO);

    void answerQuestion(AnswerQuestionDTO dto);

    AiExamUser examUserAdd(AiExamUserAddDTO dto);

    Page<CardInfo> frontBankPage(AiExamBankFrontDTO queryDTO);

    ChatMessage wrapWiserChatMessage(ChatMessage chatMessage);

    MbtiInfo generateReport(AiExamUser aiExamUser);

    MbtiInfo getReport(Long id);

    CardInfo getReportCard(Long id);

    MbtiInfo getLatestReport(String userId);

    List<CardInfo> cardWrapper(List<CardInfo> aiExamBanks, String userId);

}
