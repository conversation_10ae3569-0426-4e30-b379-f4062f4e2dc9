package com.wisematch.modules.exam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.exam.entity.AiExamQuestion;
import com.wisematch.modules.exam.entity.AiExamUser;

import java.util.List;


/**
 * AI面试准备服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */

public interface AiExamQuestionService extends IService<AiExamQuestion> {

    List<AiExamQuestion> paperQuestion(Long paperId);

    Long paperQuestionNum(Long paperId);


    Long paperQuestionNum(AiExamUser ownLatest, Long paperId);

}
