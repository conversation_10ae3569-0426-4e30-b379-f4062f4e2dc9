package com.wisematch.modules.exam.model;

import com.wisematch.common.model.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AiExamBankFrontDTO implements PageRequest {

    @Schema(description = "起始页", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer pageNum = 1;

    @Schema(description = "页面大小", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer pageSize = 10;


}
