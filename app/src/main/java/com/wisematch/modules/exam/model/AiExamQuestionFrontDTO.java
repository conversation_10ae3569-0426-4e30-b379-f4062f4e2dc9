package com.wisematch.modules.exam.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AiExamQuestionFrontDTO {

    @Schema(description = "paperId", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer paperId;

    @Schema(description = "起始页", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer pageNum = 1;

    @Schema(description = "页面大小", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer pageSize = 10;

}
