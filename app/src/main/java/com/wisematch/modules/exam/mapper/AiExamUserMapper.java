package com.wisematch.modules.exam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wisematch.modules.exam.entity.AiExamUser;
import org.springframework.data.repository.query.Param;

/**
 * AI面试
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年9月18日 上午9:34:11
 */
public interface AiExamUserMapper extends BaseMapper<AiExamUser> {

    Page<AiExamUser> selectLatestPerUserBank(Page<AiExamUser> page, @Param("userId") String userId);

}
