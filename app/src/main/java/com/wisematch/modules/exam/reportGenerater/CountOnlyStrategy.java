package com.wisematch.modules.exam.reportGenerater;

import com.wisematch.modules.exam.model.CombinationResult;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CountOnlyStrategy implements CombinationStrategy {

    @Override
    public CombinationResult calculate(List<String[]> combinations, List<String> sequence) {
        Map<String, Integer> countMap = new HashMap<>();
        for (String ch : sequence) {
            String key = String.valueOf(ch);
            countMap.put(key, countMap.getOrDefault(key, 0) + 1);
        }

        List<CombinationResult.ComboResult> comboResults = new ArrayList<>();
        for (String[] combo : combinations) {
            String selected = combo[0];
            int maxCount = countMap.getOrDefault(selected, 0);
            for (String element : combo) {
                int count = countMap.getOrDefault(element, 0);
                if (count > maxCount) {
                    maxCount = count;
                    selected = element;
                }
            }
            // 仅返回 selected，ratios为空
            comboResults.add(new CombinationResult.ComboResult(selected, new ArrayList<>()));
        }

        return new CombinationResult(comboResults);
    }
}

