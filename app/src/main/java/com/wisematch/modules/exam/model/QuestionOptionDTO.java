package com.wisematch.modules.exam.model;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wisematch.modules.exam.entity.AiExamQuestionRecord;
import lombok.Data;
import lombok.ToString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@ToString
public class QuestionOptionDTO implements Serializable {

    private static final Logger log = LoggerFactory.getLogger(QuestionOptionDTO.class);
    private List<Option> options;

    @Data
    public static class Option{

        private String text;

        private String code;

        private String tendency;
    }

    public static Option findByCode(List<Option> options, String code) {
        return options.stream()
                .filter(o -> code.equals(o.getCode()))
                .findFirst()
                .orElse(null);
    }

    public static List<Option> convert(String json) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(json, new TypeReference<List<Option>>() {});
    }


    public static void addUpdateWrapper(
            LambdaUpdateWrapper<AiExamQuestionRecord> updateWrapper, AiExamQuestionRecord oneRecord, AnswerQuestionDTO dto) {

        List<Option> options = null;
        try {
            options = QuestionOptionDTO.convert(oneRecord.getOptions());
        } catch (Exception e) {
            log.info("获取答案出错");
            options = new ArrayList<>();
        }
        QuestionOptionDTO.Option option = QuestionOptionDTO.findByCode(options, dto.getUserAnswer());
        updateWrapper.set(AiExamQuestionRecord::getCorrectAnswer,option.getTendency());
    }




}
