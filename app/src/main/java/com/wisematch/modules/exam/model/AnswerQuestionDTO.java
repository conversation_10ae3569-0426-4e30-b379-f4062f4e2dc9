package com.wisematch.modules.exam.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AnswerQuestionDTO {

    @Schema(description = "所属在线测试id（外键）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long paperId;

    @Schema(description = "答题的试题id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long questionId;

    @Schema(description = "用户答案", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userAnswer;

}
