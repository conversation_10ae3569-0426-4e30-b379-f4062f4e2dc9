package com.wisematch.modules.exam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.utils.PageConvertUtils;
import com.wisematch.common.utils.PageUtils;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.exam.entity.AiExamQuestionRecord;
import com.wisematch.modules.exam.entity.AiExamUser;
import com.wisematch.modules.exam.enums.AnserStatus;
import com.wisematch.modules.exam.enums.ExamUserStatus;
import com.wisematch.modules.exam.mapper.AiExamQuestionRecordMapper;
import com.wisematch.modules.exam.model.ExamQuestionFrontDTO;
import com.wisematch.modules.exam.model.GetOneRecordDTO;
import com.wisematch.modules.exam.service.AiExamQuestionRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class AiExamQuestionRecordServiceImpl extends ServiceImpl<AiExamQuestionRecordMapper, AiExamQuestionRecord> implements AiExamQuestionRecordService {

    @Override
    public long answerNum(Long paperId, String userId) {
        return this.count(new LambdaQueryWrapper<AiExamQuestionRecord>()
                .eq(AiExamQuestionRecord::getPaperId,paperId)
                .eq(AiExamQuestionRecord::getStatus, AnserStatus.ANSWERED)
                .eq(AiExamQuestionRecord::getUserId,userId));
    }

    @Override
    public long answerNum(AiExamUser ownLatest, String userId) {

        long answeredNum = 0;
        if(null != ownLatest){
            if(!ExamUserStatus.SUBMITTED.name().equals(ownLatest.getAnswerStatus())){
                answeredNum = this
                        .answerNum(ownLatest.getId(), userId);
            }
        }
        return answeredNum;
    }


    private LambdaQueryWrapper<AiExamQuestionRecord> userQuestionRecordWrapper(ExamQuestionFrontDTO dto){
        LambdaQueryWrapper<AiExamQuestionRecord> wrapper = new LambdaQueryWrapper<>();
        if(Objects.nonNull(dto.getPaperId())){
            wrapper.eq(AiExamQuestionRecord::getPaperId, dto.getPaperId());
        }if(Objects.nonNull(dto.getUserPaperId())){
            wrapper.eq(AiExamQuestionRecord::getPaperId, dto.getUserPaperId());
        }
        wrapper.eq(AiExamQuestionRecord::getUserId, UserInfoUtils.getCurrentUserId());
        wrapper.orderByDesc(AiExamQuestionRecord::getSequence);
        return wrapper;
    }

    @Override
    public Page<CardInfo> examQuestionFrontPage(ExamQuestionFrontDTO dto) {
        LambdaQueryWrapper<AiExamQuestionRecord> wrapper = userQuestionRecordWrapper(dto);
//        wrapper.eq(AiExamQuestionRecord::get, dto.getUserPaperId());
        Page<AiExamQuestionRecord> aiChatMatcherPage = PageUtils.doPage(this, dto, wrapper);

        return PageConvertUtils.convert(
                aiChatMatcherPage,
                CardInfo::questionRecordToCard
        );
    }

    @Override
    public List<AiExamQuestionRecord> examRecord(ExamQuestionFrontDTO dto) {
        LambdaQueryWrapper<AiExamQuestionRecord> wrapper = userQuestionRecordWrapper(dto);
        return this.list(wrapper);
    }

    @Override
    public AiExamQuestionRecord getOneRecord(GetOneRecordDTO dto) {
        LambdaQueryWrapper<AiExamQuestionRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiExamQuestionRecord::getPaperId, dto.getPaperId());
        wrapper.eq(AiExamQuestionRecord::getUserId, dto.getUserId());
        wrapper.eq(AiExamQuestionRecord::getQuestionId, dto.getQuestionId());
        return this.getOne(wrapper);
    }
}

