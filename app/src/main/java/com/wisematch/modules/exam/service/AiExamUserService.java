package com.wisematch.modules.exam.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.exam.entity.AiExamUser;


/**
 * AI面试准备服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */

public interface AiExamUserService extends IService<AiExamUser> {

    AiExamUser getOwnLatest(Long paperId);

    AiExamUser getOwnLatestByUserId(String userId);


    AiExamUser getOwnLatestByUserId(Long paperId, String userId);


    String isAnswered(Long paperId, String userId);

    AiExamUser unSubmittedOne(String userId, Long bankId);

    Page<AiExamUser> getLatestRecord(Page<AiExamUser> page, String userId);
}
