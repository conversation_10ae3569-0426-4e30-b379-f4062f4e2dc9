package com.wisematch.modules.exam.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("ai_exam_question")
@Schema(description = "试题表")
public class AiExamQuestion implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "所属在线测试id（外键）")
    private Long paperId;

    @Schema(description = "在线测试名称")
    private String paperName;

    @Schema(description = "试题名称")
    private String questionName;

    @Schema(description = "选项，json字符串")
    private String options;

    @Schema(description = "试题类型，0：单选题 1：多选题 2：判断题 3：填空题 4:主观题")
    private Long type;

    @Schema(description = "试题排序，值越大排越前面")
    private Long sequence;

    @Schema(description = "分值")
    private Long score;

    @Schema(description = "正确答案")
    private String correctAnswer;

    @Schema(description = "答案解析")
    private String analysis;
}
