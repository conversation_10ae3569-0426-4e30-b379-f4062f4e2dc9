package com.wisematch.modules.exam.model;

import com.wisematch.modules.chat.model.MbtiInfo;
import com.wisematch.modules.common.utils.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class CombinationResult {

    public static List<String[]> combinations = List.of(
            new String[]{"E", "I"},
            new String[]{"S", "N"},
            new String[]{"T", "F"},
            new String[]{"J", "P"}
    );

    private List<ComboResult> comboResults;

    /**
     * 每个元素及其比例
     */
    @Data
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ElementRatio {
        private String element; // 元素名称
        private double ratio;   // 比例百分比
    }

    /**
     * 每个组合的结果对象
     */
    @Data
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ComboResult {
        private String selected; // 出现次数最多的元素
        private List<ElementRatio> ratios; // 每个元素的比例
    }

    /**
     * 主计算方法，返回实体类 CombinationResult
     */
    public static CombinationResult selectWithRatios(List<String[]> combinations, List<String> sequence) {
        // 统计序列中每个字符出现次数
        Map<String, Integer> countMap = new HashMap<>();
        for (String key : sequence) {
            countMap.put(key, countMap.getOrDefault(key, 0) + 1);
        }

        List<ComboResult> comboResults = new ArrayList<>();
        for (String[] combo : combinations) {
            List<ElementRatio> ratiosList = new ArrayList<>();
            int total = 0;

            // 先统计总次数
            for (String element : combo) {
                total += countMap.getOrDefault(element, 0);
            }

            // 计算比例
            for (String element : combo) {
                int count = countMap.getOrDefault(element, 0);
                double ratio = total == 0 ? 0 : count * 100.0 / total;
                ratiosList.add(new ElementRatio(element, Math.round(ratio)));
            }

            // 出现次数最多的元素作为 selected
            String selected = combo[0];
            int maxCount = countMap.getOrDefault(selected, 0);
            for (String element : combo) {
                int count = countMap.getOrDefault(element, 0);
                if (count > maxCount) {
                    maxCount = count;
                    selected = element;
                }
            }

            comboResults.add(new ComboResult(selected, ratiosList));
        }

        return new CombinationResult(comboResults);
    }

    public static String mbtiType(List<ComboResult> comboResults){
        return comboResults.stream().map(ComboResult::getSelected).collect(Collectors.joining());
    }

    public static List<Map<String, Double>> mbtiRatio(List<ComboResult> comboResults){
        return comboResults.stream().map(e -> {
            List<ElementRatio> ratios = e.getRatios();
            Map<String, Double> map = new HashMap<>();
            for (ElementRatio ratio : ratios) {
                map.put(ratio.getElement(), ratio.getRatio());
            }
            return map;
        }).toList();

    }

    public static MbtiInfo getMbtiInfo(CombinationResult combinationResult, String mbtiSimple){
        MbtiInfo mbtiInfo = new MbtiInfo();
        String mbti = mbtiType(combinationResult.getComboResults());
        mbtiInfo.setMbti(mbti);
        List<MBTIinfo> mbtIinfos ;
        try {
            mbtIinfos = JsonUtils.convertToList(mbtiSimple, MBTIinfo.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        MBTIinfo byMbti = MBTIinfo.findByMbti(mbtIinfos, mbti);
        mbtiInfo.setName(byMbti.getName());
        mbtiInfo.setComments(byMbti.getComments());
        mbtiInfo.setList(mbtiRatio(combinationResult.getComboResults()));
        return mbtiInfo;
    }

}
