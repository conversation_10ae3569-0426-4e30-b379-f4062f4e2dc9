package com.wisematch.modules.exam.controller;

import com.wisematch.common.annotation.SysLog;
import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.modules.exam.model.AiExamBankFrontDTO;
import com.wisematch.modules.exam.model.AiExamUserAddDTO;
import com.wisematch.modules.exam.service.AiAnswerQuestionService;
import com.wisematch.modules.exam.service.AiExamBankService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version AiMatcherController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/exam/bank")
@Tag(name = "试卷库", description = "试卷库")
public class AiExamBankController {

    @Autowired
    AiExamBankService aiExamBankService;
    @Autowired
    AiAnswerQuestionService aiAnswerQuestionService;

    @Operation(summary = "选中试题准备作答")
    @PostMapping("/examUserAdd")
    @SysLog("选中试题准备作答")
    @NotDoubleSubmit
    public R frontBankPage(@RequestBody AiExamUserAddDTO dto) {
        return R.ok().setData(aiAnswerQuestionService.examUserAdd(dto));
    }

    @Operation(summary = "wiser端试题")
    @PostMapping("/frontBankPage")
    @Deprecated
    @SysLog("wiser端试题")
    public R frontBankPage(@RequestBody AiExamBankFrontDTO dto) {
        return R.ok().setData(aiAnswerQuestionService.frontBankPage(dto));
    }
    
}
