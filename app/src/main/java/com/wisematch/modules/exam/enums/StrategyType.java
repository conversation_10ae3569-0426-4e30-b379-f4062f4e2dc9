package com.wisematch.modules.exam.enums;

import com.wisematch.modules.exam.reportGenerater.CombinationStrategy;
import com.wisematch.modules.exam.reportGenerater.CountOnlyStrategy;
import com.wisematch.modules.exam.reportGenerater.DefaultCombinationStrategy;

public enum StrategyType {
    MBTI_SIMPLE(new DefaultCombinationStrategy()),
    MBTI_COUNT(new CountOnlyStrategy());

    private final CombinationStrategy strategy;

    StrategyType(CombinationStrategy strategy) {
        this.strategy = strategy;
    }

    public CombinationStrategy getStrategy() {
        return strategy;
    }


}
