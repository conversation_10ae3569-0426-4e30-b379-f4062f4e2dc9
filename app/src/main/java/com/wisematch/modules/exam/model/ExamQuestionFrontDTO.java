package com.wisematch.modules.exam.model;

import com.wisematch.common.model.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ExamQuestionFrontDTO implements PageRequest {

    @Schema(description = "起始页", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer pageNum = 1;

    @Schema(description = "页面大小", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer pageSize = 1000;

    @Schema(description = "paperId，可以不传", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long paperId;

    @Schema(description = "add接口返回的id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userPaperId;

    public ExamQuestionFrontDTO(Integer pageNum, Integer pageSize, Long paperId) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.paperId = paperId;
    }

    public ExamQuestionFrontDTO(Long paperId) {
        this.paperId = paperId;
    }

    public ExamQuestionFrontDTO() {
    }
}
