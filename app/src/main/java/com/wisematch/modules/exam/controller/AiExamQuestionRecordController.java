package com.wisematch.modules.exam.controller;

import com.wisematch.common.annotation.SysLog;
import com.wisematch.common.utils.R;
import com.wisematch.modules.exam.model.AnswerQuestionDTO;
import com.wisematch.modules.exam.model.ExamQuestionFrontDTO;
import com.wisematch.modules.exam.service.AiAnswerQuestionService;
import com.wisematch.modules.exam.service.AiExamQuestionRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version AiMatcherController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/exam/questionRecord")
@Tag(name = "测试记录", description = "测试记录")
public class AiExamQuestionRecordController {
    @Autowired
    private AiExamQuestionRecordService aiExamQuestionRecordService;
    @Autowired
    private AiAnswerQuestionService aiAnswerQuestionService;

    @Operation(summary = "答题（一道一道）")
    @PostMapping("/answerQuestion")
    @SysLog("答题（一道一道）")
    public R report(@RequestBody AnswerQuestionDTO dto) {
        aiAnswerQuestionService.answerQuestion(dto);
        return R.ok();
    }

    @Operation(summary = "获取用户当前试题列表")
    @PostMapping("/examQuestionFrontPage")
    @SysLog("获取用户当前试题列表")
    public R report(@RequestBody ExamQuestionFrontDTO dto) {
        return R.ok().setData(aiExamQuestionRecordService.examQuestionFrontPage(dto));
    }

}
