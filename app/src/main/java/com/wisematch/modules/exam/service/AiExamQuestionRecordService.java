package com.wisematch.modules.exam.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.exam.entity.AiExamQuestionRecord;
import com.wisematch.modules.exam.entity.AiExamUser;
import com.wisematch.modules.exam.model.ExamQuestionFrontDTO;
import com.wisematch.modules.exam.model.GetOneRecordDTO;

import java.util.List;


/**
 * AI面试准备服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */

public interface AiExamQuestionRecordService extends IService<AiExamQuestionRecord> {

    long answerNum(Long paperId, String userId);

    Page<CardInfo> examQuestionFrontPage(ExamQuestionFrontDTO dto);

    List<AiExamQuestionRecord> examRecord(ExamQuestionFrontDTO dto);

    AiExamQuestionRecord getOneRecord(GetOneRecordDTO getOneRecordDTO);

    public long answerNum(AiExamUser ownLatest, String userId);
}
