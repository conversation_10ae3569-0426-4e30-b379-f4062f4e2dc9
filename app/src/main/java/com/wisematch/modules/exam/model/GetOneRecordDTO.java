package com.wisematch.modules.exam.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetOneRecordDTO implements Serializable {

    @Schema(description = "paperId", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long paperId;

    /** 试题id（外键） */
    @Schema(description = "questionId", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long questionId;

    @Schema(description = "userId", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userId;
}
