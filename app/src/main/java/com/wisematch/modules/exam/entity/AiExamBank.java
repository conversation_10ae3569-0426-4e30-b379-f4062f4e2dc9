package com.wisematch.modules.exam.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("ai_exam_bank")
@Schema(description = "题库表")
public class AiExamBank implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "题库名称")
    private String name;

    @Schema(description = "测试时长(分钟)")
    private Integer duration;

    @Schema(description = "在线测试状态，0：不可用，1：可用")
    private Integer status;

    @Schema(description = "题库类型")
    private String examType;

    @Schema(description = "选题规则")
    private String rule;

    @Schema(description = "标签")
    private String labels;

    @Schema(description = "logo")
    private String logo;

    /** 在线测试名称 */
    private String summary;
}

