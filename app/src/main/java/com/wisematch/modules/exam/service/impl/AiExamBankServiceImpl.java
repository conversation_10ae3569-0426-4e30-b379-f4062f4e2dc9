package com.wisematch.modules.exam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.utils.BeanCopyUtils;
import com.wisematch.common.utils.PageConvertUtils;
import com.wisematch.common.utils.PageUtils;
import com.wisematch.modules.chat.enums.WiserConstant;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.exam.entity.AiExamBank;
import com.wisematch.modules.exam.mapper.AiExamBankMapper;
import com.wisematch.modules.exam.model.AiExamBankFrontDTO;
import com.wisematch.modules.exam.service.AiExamBankService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class AiExamBankServiceImpl extends ServiceImpl<AiExamBankMapper, AiExamBank> implements AiExamBankService {

    @Override
    public Page<CardInfo> frontBankPage(AiExamBankFrontDTO queryDTO) {
        QueryWrapper<AiExamBank> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AiExamBank::getStatus, WiserConstant.IN_USE);
        Page<AiExamBank> aiExamBankPage = PageUtils.doPage(this, queryDTO, wrapper);
        return PageConvertUtils.convert(
                aiExamBankPage,
                CardInfo::examBankToCardInfo
        );
    }

    @Override
    public List<CardInfo> allBank() {
        List<AiExamBank> list = this.list(new QueryWrapper<AiExamBank>());
        return BeanCopyUtils.copyList(list,
                CardInfo::examBankToCardInfo);
    }
}

