package com.wisematch.modules.exam.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wisematch.common.model.IUserTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 在线测试表实体类
 */
@Data
@TableName("ai_exam_user")
@Schema(description = "用户试卷表")
public class AiExamUser implements Serializable, IUserTime<String> {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 在线测试名称 */
    private String name;

    /** 在线测试名称 */
    private String summary;

    /** 答题时长(分钟) */
    private Integer time;

    /**
     * //未答题
     NOT_ANSWER,
     //初始化
     INIT,
     //答题中
     ANSWERING,
     //已提交
     SUBMITTED,
     //生成报告中
     REPORTING,
     //报告已生成
     REPORTED,
     */
    private String answerStatus;

    @Schema(description = "标签")
    private String labels;

    /** 用户ID */
    private String userId;

    /** 报告内容 */
    private String reportContent;

    /** 报告内容 */
    private String logo;

    /** bankId题库id */
    private Long bankId;

    /** 题目数量 */
    private Integer questionNum;

    @Schema(description = "题库类型")
    private String examType;

}
