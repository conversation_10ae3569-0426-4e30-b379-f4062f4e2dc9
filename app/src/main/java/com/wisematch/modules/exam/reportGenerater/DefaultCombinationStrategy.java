package com.wisematch.modules.exam.reportGenerater;

import com.wisematch.modules.exam.model.CombinationResult;

import java.util.List;

public class DefaultCombinationStrategy implements CombinationStrategy {

    @Override
    public CombinationResult calculate(List<String[]> combinations, List<String> sequence) {
        // 调用原来的 selectWithRatios 方法逻辑
        return CombinationResult.selectWithRatios(combinations, sequence);
    }
}

