package com.wisematch.modules.exam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.exam.entity.AiExamUser;
import com.wisematch.modules.exam.enums.ExamUserStatus;
import com.wisematch.modules.exam.mapper.AiExamUserMapper;
import com.wisematch.modules.exam.service.AiExamUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AiExamUserServiceImpl extends ServiceImpl<AiExamUserMapper, AiExamUser> implements AiExamUserService {


    @Override
    public AiExamUser getOwnLatest(Long paperId) {

        return this.getOne(new LambdaQueryWrapper<AiExamUser>()
                .eq(AiExamUser::getBankId, paperId)
                .eq(AiExamUser::getUserId, UserInfoUtils.getCurrentUserId())
                .orderByDesc(AiExamUser::getCreateTime).last("limit 1"));
    }

    @Override
    public AiExamUser getOwnLatestByUserId(String userId) {
//        MybatisUtils.getLatestOneByUserId(this, userId);
        return this.getOne(new LambdaQueryWrapper<AiExamUser>()
                .eq(AiExamUser::getUserId, userId)
                .orderByDesc(AiExamUser::getCreateTime).last("limit 1"));
    }

    @Override
    public AiExamUser getOwnLatestByUserId(Long paperId, String userId) {

        return this.getOne(new LambdaQueryWrapper<AiExamUser>()
                .eq(AiExamUser::getBankId, paperId)
                .eq(AiExamUser::getUserId, userId)
                .orderByDesc(AiExamUser::getCreateTime).last("limit 1"));
    }

    @Override
    public String isAnswered(Long paperId, String userId) {
        AiExamUser one = this.getOwnLatestByUserId(paperId, userId);
        if(null == one){
            return ExamUserStatus.NOT_ANSWER.name();
        }
        return one.getAnswerStatus();
    }

    @Override
    public AiExamUser unSubmittedOne(String userId, Long bankId) {
        return this.getOne(new LambdaQueryWrapper<AiExamUser>()
                .eq(AiExamUser::getBankId, bankId)
                .eq(AiExamUser::getUserId, userId)
                .eq(AiExamUser::getAnswerStatus, ExamUserStatus.INIT.name()));
    }

    @Override
    public Page<AiExamUser> getLatestRecord(Page<AiExamUser> page, String userId) {
        return this.baseMapper.selectLatestPerUserBank(page, userId);
    }

}

