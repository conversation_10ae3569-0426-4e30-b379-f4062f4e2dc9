package com.wisematch.modules.exam.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.exam.entity.AiExamBank;
import com.wisematch.modules.exam.model.AiExamBankFrontDTO;

import java.util.List;


/**
 * AI面试准备服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */

public interface AiExamBankService extends IService<AiExamBank> {

    public Page<CardInfo> frontBankPage(AiExamBankFrontDTO queryDTO);

    public List<CardInfo> allBank();


}
