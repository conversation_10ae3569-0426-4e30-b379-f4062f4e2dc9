package com.wisematch.modules.exam.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 测试记录表
 */
@Data
@TableName("ai_exam_question_record")
@Schema(description = "考试记录表")
public class AiExamQuestionRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 用户id */
    private String userId;

    /** 用户名 */
    private String username;

    /** 在线测试id（外键） */
    private Long paperId;

    /** 在线测试名称 */
    private String paperName;

    /** 试题id（外键） */
    private Long questionId;

    /** 试题名称 */
    private String questionName;

    /** 选项，json字符串 */
    private String options;

    /** 得分 */
    private Long score;

    /** 试题类型：0 单选题，1 多选题，2 判断题，3 填空题，4 主观题 */
    private Long type;

    /** 用户答案 */
    private String userAnswer;

    /** 正确答案 */
    private String correctAnswer;

    /** 答案解析 */
    private String analysis;

    /** 答题状态,0未答，1已答 */
    private Integer status;

    /** 顺序 */
    private Long sequence;
}

