package com.wisematch.modules.exam.model;

import lombok.Data;

import java.util.List;

@Data
public class MBTIinfo {

    private String mbti;

    private String name;

    private String comments;

    /**
     * 根据 mbti 查询对应的 MBTIinfo 对象
     * @param list MBTIinfo 列表
     * @param mbti 要查询的 mbti 值
     * @return 匹配的 MBTIinfo 对象，若未找到返回 null
     */
    public static MBTIinfo findByMbti(List<MBTIinfo> list, String mbti) {
        if (list == null || mbti == null) {
            return new MBTIinfo();
        }
        return list.stream()
                .filter(info -> mbti.equalsIgnoreCase(info.getMbti()))
                .findFirst()
                .orElse(new MBTIinfo());
    }

}
