/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package com.wisematch.modules.sms;

import lombok.experimental.UtilityClass;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version SmsUtils.java, v 0.1 2023-09-24 22:23 syoka
 */
@UtilityClass
public class SmsUtils {
    private static final String regex = "^1[3456789]\\d{9}$";
    private static final Pattern chinesePhonePattern = Pattern.compile(regex);

    public boolean isChinesePhone(String phone) {
        return chinesePhonePattern.matcher(phone).matches();
    }
}
