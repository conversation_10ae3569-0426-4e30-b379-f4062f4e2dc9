/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package com.wisematch.modules.sms;

import com.wisematch.common.cache.RedisCache;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.RandomUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class SmsVerifyCodeManager {

    @Resource
    private RedisCache redisCache;

    // 短、中、长时间窗口限制次数
    // 60秒内最多1次
    private final int SHORT_LIMIT = 1;
    // 1小时内最多5次
    private final int MEDIUM_LIMIT = 5;
    // 1天内最多20次
    private final int LONG_LIMIT = 20;

    // 窗口时间（毫秒）// 60秒
    private final long SHORT_WINDOW_MS = 60 * 1000L;
    // 1小时
    private final long MEDIUM_WINDOW_MS = 60 * 60 * 1000L;
    // 1天
    private final long LONG_WINDOW_MS = 24 * 60 * 60 * 1000L;

    public String generateVerifyCode(String phone, String ip) {
        if (!SmsUtils.isChinesePhone(phone)) {
            log.warn("非中国手机号: {}", phone);
            throw new RRException(RRExceptionEnum.PHONE_NOT_EXIST_ERROR);
        }
        String userSmsCodeGen = "USER_LOGIN_PHONE_VERIFY_CODE_GEN_" + phone;
        // 验证码未过期
        if (redisCache.get(userSmsCodeGen) != null) {
            log.warn("发送验证码过于频繁, phone: {}, ip: {}", phone, ip);
            throw new RRException(RRExceptionEnum.APP_SEND_SMS_TOO_OFTEN);
        }
        String code = getRandomCode();
        String userSmsKey = "USER_LOGIN_PHONE_VERIFY_CODE_" + phone;
        redisCache.put(userSmsCodeGen, code, 60L);
        redisCache.put(userSmsKey, code, 300L);
        log.info("Send code, userSmsKey: {}, code: {}", userSmsKey, code);
        return code;
    }

    public boolean validateUserVerifyCode(String phone, String smsCode) {
        if (StringUtils.isNotBlank(phone) && StringUtils.isNotBlank(smsCode)) {
            String key = "USER_LOGIN_PHONE_VERIFY_CODE_" + phone;
            log.info("Verify code, userSmsKey: {}, code: {}", key, smsCode);
            String code = redisCache.getString(key);
            if (StringUtils.isBlank(code)) {
                throw new RRException(RRExceptionEnum.APP_LOGIN_CODE_EXPIRED);
            }
            if (StringUtils.equals(smsCode, code)) {
                return true;
            }
            log.error("Verify code error,phone:{},smsCode:{},verifyCode:{}", phone, code, smsCode);
        }
        return false;
    }

    /**
     * 滑动窗口检查
     */
    private boolean canSendSlidingWindow(String phone, String ip) {
        return checkSlidingWindow("phone", phone, SHORT_WINDOW_MS, SHORT_LIMIT) &&
                checkSlidingWindow("phone", phone, MEDIUM_WINDOW_MS, MEDIUM_LIMIT) &&
                checkSlidingWindow("phone", phone, LONG_WINDOW_MS, LONG_LIMIT) &&
                checkSlidingWindow("ip", ip, SHORT_WINDOW_MS, SHORT_LIMIT) &&
                checkSlidingWindow("ip", ip, MEDIUM_WINDOW_MS, MEDIUM_LIMIT) &&
                checkSlidingWindow("ip", ip, LONG_WINDOW_MS, LONG_LIMIT);
    }

    /**
     * 单个 key 滑动窗口限制
     */
    private boolean checkSlidingWindow(String type, String key, long windowMs, int limit) {
        String redisKey = String.format("sms:sliding:%s:%s", type, key);
        long now = System.currentTimeMillis();

        // 使用 ZSet 存储发送时间戳
        // 移除过期
        redisCache.removeRangeByScore(redisKey, 0, now - windowMs);
        Long count = redisCache.zCard(redisKey);
        if (count != null && count >= limit) {
            // 超过限制
            return false;
        }
        // 添加当前时间戳，TTL 设置为窗口时间+1分钟，防止永久存储
        redisCache.zAdd(redisKey, String.valueOf(now), now);
        redisCache.expire(redisKey, windowMs / 1000 + 60);
        return true;
    }

    private String getRandomCode() {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            int value = Math.abs(RandomUtils.nextInt( 10));
            code.append(value);
        }
        return code.toString();
    }
}