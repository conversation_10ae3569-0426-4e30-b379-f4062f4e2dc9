/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package com.wisematch.modules.sms;

import com.alibaba.fastjson.JSON;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version AliyunSmsMessageService.java, v 0.1 2023-09-24 22:52 syoka
 */
@Component
@Slf4j
public class AliyunSmsMessageService implements MessageService {

    @Value("${message.phone.aliyun.endpoint}")
    private String endpoint;

    @Value("${message.phone.aliyun.region-id}")
    private String regionId;

    @Value("${message.phone.aliyun.access-key-id}")
    private String accessKeyId;

    @Value("${message.phone.aliyun.access-key-secret}")
    private String accessKeySecret;

    @Value("${message.phone.aliyun.sign-name}")
    private String signName;

    @Value("${message.phone.aliyun.verify-template-code}")
    private String templateCode;

    @Override
    public void sendMessage(MessagePayload payload) {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);

        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain(endpoint);
        request.setSysVersion("2017-05-25");
        request.setSysAction("SendSms");
        request.putQueryParameter("RegionId", regionId);
        request.putQueryParameter("PhoneNumbers", payload.getTarget());
        request.putQueryParameter("SignName", signName);
        request.putQueryParameter("TemplateCode", templateCode);
        request.putQueryParameter("TemplateParam", payload.getContent());
        try {
            CommonResponse response = client.getCommonResponse(request);
            log.info("aliyun返回的短信响应体：{}", JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("aliyun发送消息失败", e);
         }
    }

}
