/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package com.wisematch.modules.sms;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 消息体
 *
 * <AUTHOR>
 * @version MessagePayload.java, v 0.1 2023-09-24 22:43 syoka
 */
@Data
@Accessors(chain = true)
public class MessagePayload {

    private String target;

    private String content;

}
