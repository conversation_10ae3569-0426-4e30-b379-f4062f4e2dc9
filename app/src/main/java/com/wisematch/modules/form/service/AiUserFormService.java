package com.wisematch.modules.form.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.form.entity.AiUserForm;
import com.wisematch.modules.form.model.AiUserFormSearchDTO;
import com.wisematch.modules.form.model.LatestIntentionDTO;

import java.util.List;

/**
 * 用户表单服务
 *
 * 表名：ai_user_form，用于存储用户提交的表单信息（如求职意愿、问题反馈等）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */
public interface AiUserFormService extends IService<AiUserForm> {

    AiUserForm insertByJson(JSONObject jsonObject, String formType);

    void batchLogicDelete(List<String> list);

    void logicDelete(String id);

    List<AiUserForm> queryOwnList(AiUserFormSearchDTO aiUserFormDTO, String formType);

    AiUserForm insertByEntity(Object obj, String formType);

    AiUserForm updateByEntity(Object obj);


    AiUserForm updateByJson(JSONObject jsonObject);

    List<AiUserForm> jobIntentionList(String userId);

    AiUserForm latestIntention(LatestIntentionDTO dto);

    void insertFromInterview(String chatName, JSONObject applyInfos) ;
}