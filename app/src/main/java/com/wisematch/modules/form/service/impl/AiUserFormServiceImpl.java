package com.wisematch.modules.form.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.enums.AiUserFormType;
import com.wisematch.modules.chat.model.InsertByJsonDTO;
import com.wisematch.modules.common.utils.JsonUtils;
import com.wisematch.modules.form.entity.AiUserForm;
import com.wisematch.modules.form.mapper.AiUserFormMapper;
import com.wisematch.modules.form.model.AiUserFormSearchDTO;
import com.wisematch.modules.form.model.LatestIntentionDTO;
import com.wisematch.modules.form.service.AiUserFormService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.wisematch.modules.chat.enums.WiserConstant.DELETED;
import static com.wisematch.modules.chat.enums.WiserConstant.NOT_DELETE;

/**
 * 用户表单服务实现类
 *
 * 表名：ai_user_form，用于存储用户提交的表单信息（如求职意愿、问题反馈等）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:45:21
 */
@Service
@Slf4j
public class AiUserFormServiceImpl extends ServiceImpl<AiUserFormMapper, AiUserForm> implements AiUserFormService {


    @Override
    public AiUserForm insertByJson(JSONObject jsonObject, String formType) {
        AiUserForm aiUserForm = getAiUserForm(jsonObject);
        if(formType.equals(AiUserFormType.FULL_TIME_JOB_INTENTION.name()) || formType.equals(AiUserFormType.PART_TIME_JOB_INTENTION.name()) ){
            aiUserForm.setPosition(jsonObject.getString("expectedPosition"));
        }

        aiUserForm.setCreateTime(new Date());
        aiUserForm.setFormType(formType);
        aiUserForm.setUserId(UserInfoUtils.getCurrentUserId());
        this.baseMapper.insert(aiUserForm);
        return aiUserForm;
    }

    @Override
    public void batchLogicDelete(List<String> list) {
        this.baseMapper.batchLogicDelete(list);
    }

    @Override
    public void logicDelete(String id) {

        this.baseMapper.update(
                new UpdateWrapper<AiUserForm>()
                        .lambda()
                        .eq(AiUserForm::getId, id)
                        .set(AiUserForm::getIsDel, DELETED)
        );
    }

    @Override
    public List<AiUserForm> queryOwnList(AiUserFormSearchDTO aiUserFormDTO, String formType) {
        QueryWrapper<AiUserForm> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(formType)) {
            queryWrapper.lambda().eq(AiUserForm::getFormType, formType);
        }
        queryWrapper.lambda().eq(AiUserForm::getIsDel, NOT_DELETE);
        queryWrapper.lambda().eq(AiUserForm::getUserId, UserInfoUtils.getCurrentUserId());
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public AiUserForm insertByEntity(Object obj, String formType) {
        JSONObject jsonObject = JsonUtils.toJsonObject(obj);
        return insertByJson(jsonObject,formType);
    }

    @Override
    public AiUserForm updateByJson(JSONObject jsonObject) {
        AiUserForm aiUserForm = getAiUserForm(jsonObject);
        aiUserForm.setId(jsonObject.getString("id"));
        this.baseMapper.updateById(aiUserForm);
        return aiUserForm;
    }

    @Override
    public List<AiUserForm> jobIntentionList(String userId) {
        QueryWrapper<AiUserForm> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(AiUserForm::getFormType, AiUserFormType.FULL_TIME_JOB_INTENTION.name(),AiUserFormType.PART_TIME_JOB_INTENTION.name());
        queryWrapper.lambda().eq(AiUserForm::getUserId, UserInfoUtils.getCurrentUserId());
        queryWrapper.lambda().eq(AiUserForm::getIsDel, NOT_DELETE);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public AiUserForm latestIntention(LatestIntentionDTO dto) {
        QueryWrapper<AiUserForm> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiUserForm::getUserId, UserInfoUtils.getCurrentUserId());
        queryWrapper.lambda().eq(AiUserForm::getFormType, dto.getFormType());
        if(StringUtils.isNotBlank(dto.getExpectedPosition())){
            queryWrapper.lambda().eq(AiUserForm::getPosition, dto.getExpectedPosition());
        }
        queryWrapper.lambda().eq(AiUserForm::getIsDel,NOT_DELETE);
        queryWrapper.lambda().last("limit 1");
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public void insertFromInterview(String chatName, JSONObject applyInfos) {

        String formType = applyInfos.getString("formType");
        applyInfos.put("expectedPosition", chatName);
        AiUserForm aiUserForm = this.latestIntention(new LatestIntentionDTO(chatName, formType));
        if(null == aiUserForm){
            aiUserForm = new AiUserForm();
        }

        aiUserForm.setPosition(chatName);
        if(formType.equals(AiUserFormType.PART_TIME_JOB_APPLY.name())){
            aiUserForm.setFormType(AiUserFormType.PART_TIME_JOB_INTENTION.name());
        }else if(formType.equals(AiUserFormType.FULL_TIME_JOB_APPLY.name())){
            aiUserForm.setFormType(AiUserFormType.FULL_TIME_JOB_INTENTION.name());
        }

        aiUserForm.setUserId(UserInfoUtils.getCurrentUserId());
        aiUserForm.setFormJson(applyInfos.toJSONString());
        aiUserForm.setIsDel(NOT_DELETE);
        this.saveOrUpdate(aiUserForm);
    }

    @NotNull
    private AiUserForm getAiUserForm(JSONObject jsonObject) {
        InsertByJsonDTO jsonDTO = jsonObject.toJavaObject(InsertByJsonDTO.class);
        AiUserForm aiUserForm = new AiUserForm();
        BeanUtils.copyProperties(jsonDTO,aiUserForm);
        aiUserForm.setFormJson(jsonObject.toJSONString());
        aiUserForm.setUpdateTime(new Date());
        aiUserForm.setIsDel(NOT_DELETE);
        return aiUserForm;
    }

    @Override
    public AiUserForm updateByEntity(Object obj) {
        JSONObject jsonObject = JsonUtils.toJsonObject(obj);
        return updateByJson(jsonObject);
    }

}

