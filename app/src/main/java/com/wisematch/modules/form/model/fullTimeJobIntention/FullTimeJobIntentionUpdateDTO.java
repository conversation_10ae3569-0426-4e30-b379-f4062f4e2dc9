package com.wisematch.modules.form.model.fullTimeJobIntention;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import static com.wisematch.modules.chat.enums.aiPosition.TypeEnum.FULL_TIME;

@Data
@Schema(description = "全职工作意向修改（所有字段必传）")
public class FullTimeJobIntentionUpdateDTO {

    @Schema(description = "主键id")
    private String id;

    @Schema(description = "期望岗位")
    private String expectedPosition;

    @Schema(description = "求职期望")
    private String jobExpectations;

    @Schema(description = "允许企业查看视频")
    private String allowViewingVideos;

    @Schema(description = "工作城市")
    private String workCity;

    @NotBlank
    @Schema(description = "最低薪资要求")
    private String salaryRequirementsStart;

    @NotBlank
    @Schema(description = "最高薪资要求")
    private String salaryRequirementsEnd;

    @Schema(description = "薪资要求")
    private String salaryRequirements;

    @Schema(description = "求职状态")
    private String jobStatus;


    @Schema(description = "期望城市")
    private String expectedCity;

    @Schema(description = "期望行业")
    private String expectedIndustry;

    @Schema(description = "全职、兼职")
    String workWay = FULL_TIME.name();

}
