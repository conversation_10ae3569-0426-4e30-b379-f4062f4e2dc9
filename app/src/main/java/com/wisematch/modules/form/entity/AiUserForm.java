package com.wisematch.modules.form.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户表单表实体类
 */
@Data
@TableName("ai_user_form")
@Schema(description = "用户表单信息")
public class AiUserForm implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "表单内容(JSON格式)")
    private String formJson;

    @Schema(description = "表单类型：求职意愿，问题反馈等")
    private String formType;

    @Schema(description = "职位")
    private String position;

    @Schema(description = "表单状态")
    private Integer status;

    @Schema(description = "删除标志 0:删除,1:不删除")
    private Integer isDel;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
