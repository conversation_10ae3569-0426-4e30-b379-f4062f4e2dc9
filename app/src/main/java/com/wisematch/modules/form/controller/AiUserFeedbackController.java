package com.wisematch.modules.form.controller;

import com.wisematch.common.utils.R;
import com.wisematch.modules.chat.enums.AiUserFormType;
import com.wisematch.modules.form.model.wiserFeedback.WiserFeedbackInsertDTO;
import com.wisematch.modules.form.service.AiUserFormService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户表单，分类管理
 * <AUTHOR>
 * @version AiChatController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/user/wiserFeedback")
@Tag(name = "面试问题反馈表单", description = "面试问题反馈表单")
@Slf4j
public class AiUserFeedbackController {

    @Autowired
    AiUserFormService aiUserFormService;

    /**
     * 按表单类型提交，类型枚举UserFormType
     * @return
     */
    @Operation(summary = "面试问题反馈表单")
    @PostMapping("/submit")
    public R startAgent(@RequestBody WiserFeedbackInsertDTO wiserFeedbackInsertDTO) {
        return R.ok().setData(aiUserFormService.insertByEntity(wiserFeedbackInsertDTO, AiUserFormType.WISER_FEED_BACK.name()));
    }

}
