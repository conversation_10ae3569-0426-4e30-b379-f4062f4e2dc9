package com.wisematch.modules.form.controller;

import com.wisematch.common.utils.R;
import com.wisematch.modules.chat.enums.AiUserFormType;
import com.wisematch.modules.form.model.ComplaintReport.ComplaintReportDTO;
import com.wisematch.modules.form.service.AiUserFormService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户表单，分类管理
 * <AUTHOR>
 * @version AiChatController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/user/complaintReport")
@Tag(name = "投诉举报", description = "投诉举报")
@Slf4j
public class AiComplaintReportController {

    @Autowired
    AiUserFormService aiUserFormService;

    /**
     * 按表单类型提交，类型枚举UserFormType
     * @return
     */
    @Operation(summary = "投诉举报提交表单")
    @PostMapping("/submit")
    public R startAgent(@RequestBody ComplaintReportDTO complaintReportDTO) {
        return R.ok().setData(aiUserFormService.insertByEntity(complaintReportDTO, AiUserFormType.COMPLAINT_REPORT.name()));
    }

}
