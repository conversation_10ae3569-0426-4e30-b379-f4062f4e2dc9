package com.wisematch.modules.form.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "修改DTO")
public class AiUserFormEditDTO {

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "表单内容(JSON格式)")
    private String formJson;

    @Schema(description = "表单类型：求职意愿，问题反馈等")
    private String formType;

}
