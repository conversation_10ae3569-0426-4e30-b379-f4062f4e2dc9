package com.wisematch.modules.form.model.partTimeJobApply;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import static com.wisematch.modules.chat.enums.aiPosition.TypeEnum.PART_TIME;

@Data
@Schema(description = "兼职工作面试申请新增（所有字段必传）")
public class PartTimeJobApplyInsertDTO {

    @Schema(description = "允许企业查看视频")
    private String allowViewingVideos;

    @Schema(description = "求职期望")
    private String jobExpectations;

    @Schema(description = "工作城市")
    private String workCity;

    @Schema(description = "每周工作时间")
    private String weekWorkTime;

    @Schema(description = "roomId")
    private String roomId;

    @Schema(description = "工作时间（多选）")
    private String workTime;

    @Schema(description = "工作时段（多选）")
    private String periodTime;

    @Schema(description = "期望城市")
    private String expectedCity;

    @Schema(description = "期望行业")
    private String expectedIndustry;

    @Schema(description = "全职、兼职")
    String workWay = PART_TIME.name();
}
