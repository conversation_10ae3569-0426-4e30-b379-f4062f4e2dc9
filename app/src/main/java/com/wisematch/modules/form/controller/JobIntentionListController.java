package com.wisematch.modules.form.controller;

import com.wisematch.common.utils.R;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.form.model.LatestIntentionDTO;
import com.wisematch.modules.form.service.AiUserFormService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/user/jobIntention")
@Tag(name = "求职意愿", description = "工作申请列表")
@Slf4j
public class JobIntentionListController {

    @Autowired
    AiUserFormService aiUserFormService;

    @PostMapping("/list")
    @Operation(summary = "工作意向列表")
    public R list() {
        return R.ok().setData(aiUserFormService.jobIntentionList(UserInfoUtils.getCurrentUserId()));
    }

    @PostMapping("/latestIntention")
    @Operation(summary = "最新求职意向")
    public R latestIntention(@RequestBody LatestIntentionDTO dto) {
        return R.ok().setData(aiUserFormService.latestIntention(dto));
    }

}
