package com.wisematch.modules.form.controller;

import com.wisematch.common.utils.R;
import com.wisematch.modules.chat.enums.AiUserFormType;
import com.wisematch.modules.form.entity.AiUserForm;
import com.wisematch.modules.form.model.partTimeJobIntention.PartTimeJobIntentionInsertDTO;
import com.wisematch.modules.form.model.partTimeJobIntention.PartTimeJobIntentionUpdateDTO;
import com.wisematch.modules.form.service.AiUserFormService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户表单，分类管理
 * <AUTHOR>
 * @version AiChatController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/user/partTimeJobIntention")
@Tag(name = "兼职工作意向表单", description = "兼职工作意向表单")
@Slf4j
public class PartTimeJobIntentionController {


    @Autowired
    AiUserFormService aiUserFormService;

    /**
     * 按表单类型提交，类型枚举UserFormType
     * @return
     */
    @Operation(summary = "兼职工作意向提交表单")
    @PostMapping("/submit")
    public R startAgent(@RequestBody PartTimeJobIntentionInsertDTO dto) {
        return R.ok().setData(aiUserFormService.insertByEntity(dto, AiUserFormType.PART_TIME_JOB_INTENTION.name()));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "逻辑删除（单条）")
    public R delete(@PathVariable String id) {
        aiUserFormService.logicDelete(id);
        return R.ok();
    }

    @PostMapping("/update")
    @Operation(summary = "兼职工作意向修改表单")
    public R update(@RequestBody PartTimeJobIntentionUpdateDTO dto) {
        AiUserForm aiUserForm = aiUserFormService.updateByEntity(dto);
        return R.ok().setData(aiUserForm);
    }

    @GetMapping("/{id}")
    @Operation(summary = "兼职工作意向根据 ID 获取表单")
    public R getById(@PathVariable String id) {
        return R.ok().setData(aiUserFormService.getById(id));
    }

}
