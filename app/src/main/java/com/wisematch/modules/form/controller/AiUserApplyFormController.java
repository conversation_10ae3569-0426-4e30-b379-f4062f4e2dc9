package com.wisematch.modules.form.controller;

import com.alibaba.fastjson.JSONObject;
import com.wisematch.common.utils.R;
import com.wisematch.modules.form.entity.AiUserForm;
import com.wisematch.modules.form.model.AiUserFormSearchDTO;
import com.wisematch.modules.form.service.AiUserFormService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户表单，分类管理
 * <AUTHOR>
 * @version AiChatController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/user/form")
@Tag(name = "表单提交", description = "表单提交")
@Slf4j
@Deprecated(since = "后续调用本包下的其他分接口")
public class AiUserApplyFormController {

    @Autowired
    AiUserFormService aiUserFormService;

    /**
     * 按表单类型提交，类型枚举UserFormType
     * @return
     */
    @Operation(summary = "提交表单")
    @PostMapping("/submit")
    public R startAgent(@RequestBody JSONObject formJSON) {
        return R.ok().setData(aiUserFormService.insertByJson(formJSON,""));
    }

//
//    @DeleteMapping("/{id}")
//    @Operation(summary = "逻辑删除（单条）")
//    public R delete(@PathVariable String id) {
//        aiUserFormService.logicDelete(id);
//        return R.ok();
//    }

    @PutMapping("/{id}")
    @Operation(summary = "修改表单")
    public R update(@PathVariable String id, @RequestBody AiUserForm dto) {
        dto.setId(id);
        aiUserFormService.updateById(dto);
        return R.ok().setData(dto);
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据 ID 获取表单")
    public R getById(@PathVariable String id) {
        return R.ok().setData(aiUserFormService.getById(id));
    }
//
//    @PostMapping("/delete")
//    @Operation(summary = "逻辑删除（批量）")
//    public R delete(@RequestBody String[] ids) {
//        aiUserFormService.batchLogicDelete(Arrays.asList(ids));
//        return R.ok();
//    }

    @PostMapping("/queryList")
    @Operation(summary = "搜索")
    public R list(
            @RequestBody AiUserFormSearchDTO aiUserFormDTO) {
        return R.ok().setData(aiUserFormService.queryOwnList(aiUserFormDTO,""));
    }

}
