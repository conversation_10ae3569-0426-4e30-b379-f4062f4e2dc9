package com.wisematch.modules.chat;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 面试对象
 * <AUTHOR>
 * @version Chat.java, v0.1 2025-06-17 17:17
 */
public class RoomChat {

    /**
     * token
     */
    @Schema(description = "token")
    String token;

    /**
     * 面试房间ID
     */
    @Schema(description = "面试房间ID")
    String roomId;

    /**
     * 面试ID
     */
    @Schema(description = "面试ID")
    String interviewId;

}
