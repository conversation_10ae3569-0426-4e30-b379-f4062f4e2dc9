package com.wisematch.modules.chat.model;

import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 人才画像摘要
 * <AUTHOR>
 * @version PortraitBrief.java, v0.1 2025-06-28 12:06
 */
@Data
public class PortraitBrief {

    String id;

    String userId;

    String roomId;

    @Schema(description = "用户姓名")
    String userName;

    @Schema(description = "性别")
    String sex;

    @Schema(description = "用户头像")
    String userPhoto;

    @Schema(description = "意向岗位")
    String position;
    /**
     * 工作年限
     */
    @Schema(description = "工作年限")
    String workYears;
    /**
     * 薪资
     */
    @Schema(description = "薪资期望")
    String salary;
    /**
     * 全职、兼职
     */
    @Schema(description = "全职、兼职")
    String workWay = "工作方式";
    /**
     * 简介
     */
    @Schema(description = "一句话自评")
    String userBrief;
    /**
     * 最高学历
     */
    @Schema(description = "最高学历")
    String educationalTitle;
    /**
     * 技能标签
     */
    @Schema(description = "消息标签")
    String skills;

    /**
     * 推荐理由
     */
    @Schema(description = "")
    String comments;
    /**
     * 额外字段
     */
    @Schema(description = "")
    JSONObject extra = new JSONObject();
}
