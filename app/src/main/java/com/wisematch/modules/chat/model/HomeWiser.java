package com.wisematch.modules.chat.model;


import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version HomeIndex.java, v0.1 2025-07-01 23:25
 */
@Data
public class HomeWiser {

    /**
     * 头像
     */
    @Schema(description = "头像")
    String photo;

    /**
     * wiser
     */
    @Schema(description = "wiser")
    String name = "Hi~ 我是Wiser";

    /**
     * slogan
     */
    @Schema(description = "slogan")
    String slogan = "面试投递一次 匹配千企岗位";


    @Schema(description = "输入提示")
    String inputTips = "试试提问职业规划，岗位匹配，技能提升...";

    /**
     * 热门岗位
     */
    @Schema(description = "热门岗位")
    List<CardInfo> hotPosition;

    /**
     * 热门面试
     */
    @Schema(description = "热门面试")
    List<CardInfo> hotInterview;


    @Schema(description = "热门标签")
    List<CardInfo> tips;
}
