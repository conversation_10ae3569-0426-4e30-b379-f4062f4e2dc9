package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.modules.chat.entity.AiChatLog;
import com.wisematch.modules.chat.mapper.AiChatLogMapper;
import com.wisematch.modules.chat.model.AiChatLogQueryDTO;
import com.wisematch.modules.chat.service.AiChatLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 智能体池
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiChatLogServiceImpl extends ServiceImpl<AiChatLogMapper, AiChatLog> implements AiChatLogService {

    @Override
    public List<AiChatLog> queryMemory(AiChatLogQueryDTO aiChatMemoryQueryDTO) {

        // 构造查询条件
        QueryWrapper<AiChatLog> queryWrapper = new QueryWrapper<>();
        if (aiChatMemoryQueryDTO.getConversationId() != null && !aiChatMemoryQueryDTO.getConversationId().isEmpty()) {
            queryWrapper.lambda().eq(AiChatLog::getConversationId, aiChatMemoryQueryDTO.getConversationId());
        }
        return this.list(queryWrapper);
    }
}
