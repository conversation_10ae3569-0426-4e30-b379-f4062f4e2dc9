package com.wisematch.modules.chat.model;

import com.wisematch.modules.chat.entity.AiUserResume;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AiUserResumeDTO extends AiUserResume {

    @Schema(description = "起始页")
    private Integer pageNum = 1;

    @Schema(description = "页面大小")
    private Integer pageSize = 100;

    @Schema(description = "在线简历")
    private String resumeJson;

    @Schema(description = "附件简历")
    private String resumeUrl;

    @Schema(description = "简历文本")
    private String resumeText;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "是否公开，0:公开,1:不公开")
    private Integer isOpen;

    @Schema(description = "是否删除，0:删除,1:不删除")
    private Integer isDel;

    @Schema(description = "简历解析状态")
    private Integer isAnalysis;
}
