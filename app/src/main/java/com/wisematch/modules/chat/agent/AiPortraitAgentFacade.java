package com.wisematch.modules.chat.agent;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wisematch.modules.chat.entity.AiJobExamine;
import com.wisematch.modules.chat.entity.AiViewPrepare;
import com.wisematch.modules.chat.entity.AiViewReport;
import com.wisematch.modules.chat.enums.ViewerAgentConstant;
import com.wisematch.modules.chat.model.PortraitLabelInput;
import com.wisematch.modules.chat.service.AiJobExamineService;
import com.wisematch.modules.chat.service.AiViewPrepareService;
import com.wisematch.modules.chat.service.AiViewReportService;
import com.wisematch.modules.chat.utils.JsonExtractor;
import com.wisematch.modules.common.utils.JsonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Wiser Agent 聊天门面
 * <AUTHOR>
 * @version IntentionAgentFacade.java, v0.1 2025-07-11 11:02
 */
@Component
@Slf4j
public class AiPortraitAgentFacade {

    @Resource
    private ObjectMapper objectMapper;

    @Autowired
    AgentFacade agentFacade;

    @Autowired
    AiViewPrepareService aiViewPrepareService;
    @Autowired
    AiJobExamineService aiJobAssessmentService;
    @Autowired
    AiViewReportService aiViewReportService;

    public String getPortrait(String roomId){

        AiViewPrepare aiViewPrepare = aiViewPrepareService.getByRoomId(roomId);
        String prompt = agentFacade.getPrompt(ViewerAgentConstant.PORTRAIT);
        if (aiViewPrepare.getResume() != null) {
            prompt = prompt.replace("$resume", aiViewPrepare.getResume());
        }
        AiJobExamine aiJobExamine = aiJobAssessmentService.getById(aiViewPrepare.getExamineId());

        if (aiJobExamine == null) {
            log.error("未找到对应的测评题目，测评ID：{}", aiViewPrepare.getExamineId());
            return "";
        }

        prompt = prompt.replace("$examine", aiJobExamine.getContent());

        AiViewReport aiViewReport = aiViewReportService.getByRoomId(roomId);
        if (aiViewReport.getReport() != null) {
            prompt = prompt.replace("$dimension", aiViewReport.getReport());
        }

        AgentContext context = new AgentContext();
        context.setPrompt(prompt);
        context.setAgentCode(ViewerAgentConstant.PORTRAIT);
        context.setUserMsg("帮我生成人才画像");

        String reply = JsonUtils.extractJSON(agentFacade.supply(context));
        String content = aiJobExamine.getContent();
        JSONArray allDimensions = JSONObject.parseArray(content);

        JSONObject portrait = JSONObject.parseObject(reply);
        JSONArray list = portrait.getJSONArray("dimensions");
        JSONArray reportDimensions = JSONObject.parseObject(aiViewReport.getReport()).getJSONArray("dimensions");

        portrait.put("dimensions", list.stream().peek(x -> {
            JSONObject jsonObject = (JSONObject) x;
            String portraitDimensionLabel = jsonObject.getString("dimensionName");
            jsonObject.put("score", getStandardizedScore(portraitDimensionLabel, reportDimensions, allDimensions));
        }).toList());

        return portrait.toString();
    }


    /**
     * 计算维度的标准化得分
     *
     * @param dimensionLabel  目标维度（labels）
     * @param dimensions 所有维度数据
     * @return 标准化得分（0-1之间）
     */
    private double getStandardizedScore(String dimensionLabel, JSONArray dimensions, JSONArray allDimensions) {
        // 1. 筛选出符合维度且数据完整的记录
        List<JSONObject> validItems = dimensions.stream()
                .map(item -> (JSONObject) item)
                .filter(item -> {
                    // 过滤条件：labels匹配、checkPoint不为空、score不为空
                    String itemLabel = item.getString("labels");
                    String checkPoint = item.getString("checkPoint");
                    Integer score = item.getInteger("score");
                    return itemLabel != null && itemLabel.equals(dimensionLabel)
                            && checkPoint != null && score != null;
                })
                .toList();


        // 2. 统计考核点个数
        long checkPointCount = allDimensions.stream()
                .map(dim -> (JSONObject) dim) // 显式转换为JSONObject，解决getJSONArray调用问题
                .filter(item -> item.getString("dimension").equals(dimensionLabel))
                // 从每个维度对象中获取checkPoint数组
                .map(dimension -> dimension.getJSONArray("checkPoint"))
                // 将每个JSONArray转换为流并合并
                .mapToLong(Collection::size)
                // 统计元素数量
                .sum();


        // 提取目标维度下所有checkPoint的name集合（纯Stream实现，无手动集合创建）
        List<String> checkPointNames = allDimensions.stream()
                .map(dim -> (JSONObject) dim) // 转换为JSONObject
                .filter(dimension -> dimensionLabel.equals(dimension.getString("dimension"))) // 筛选目标维度
                .map(dimension -> dimension.getJSONArray("checkPoint")) // 获取checkPoint数组
                // 通过索引流遍历JSONArray，避免手动创建List
                .flatMap(checkPointArr -> IntStream.range(0, checkPointArr.size())
                        .mapToObj(checkPointArr::getJSONObject)) // 按索引提取每个checkPoint对象
                .map(checkPoint -> checkPoint.getString("name")) // 提取name字段
                .toList(); // 收集为不可变列表（Java 16+）或collect(Collectors.toList())


        // 2. 按checkPoint分组，每组取最高score，并过滤出仅存在于checkPointNames中的key
        Map<String, Integer> maxScoresByCheckPoint = validItems.stream()
                // 先过滤出checkPoint在目标列表中的项（减少后续分组计算量）
                .filter(item -> checkPointNames.contains(item.getString("checkPoint")))
                .collect(Collectors.groupingBy(
                        item -> item.getString("checkPoint"),  // 按checkPoint分组
                        Collectors.mapping(
                                item -> item.getInteger("score"),
                                Collectors.reducing(0, Integer::max)  // 取每组最大score
                        )
                ));

        // 3. 计算总和与考核点数量
        int sumOfMaxScores = maxScoresByCheckPoint.values().stream()
                .mapToInt(Integer::intValue)
                .sum();

        // 4. 计算平均分
        double averageScore = (double) sumOfMaxScores / checkPointCount;

        // 5. 标准化（除以5）

        return (int) Math.round((averageScore / 5) * 20);
    }

    public String getReportLabel(PortraitLabelInput input) {
        long start = System.currentTimeMillis();

        String prompt = agentFacade.getPrompt(ViewerAgentConstant.INTERVIEW_TAG_EXTRACT);

        prompt = prompt.replace("$history", input.getHistory());
        prompt = prompt.replace("$resume", input.getResume());
        prompt = prompt.replace("$jd", input.getJd());

        AgentContext agentContext = new AgentContext();
        agentContext.setAgentCode(ViewerAgentConstant.INTERVIEW_TAG_EXTRACT);
        agentContext.setPrompt(prompt);
        agentContext.setUserMsg("AI面试标签提取");
        String reply = agentFacade.supply(agentContext);
        reply = JsonExtractor.extractJSONArray(reply);

        // 使用Jackson解析JSON数组并拼接字符串
        try {
            // 将JSON数组解析为List<String>
            List<String> labels = objectMapper.readValue(reply, new TypeReference<>() {});

            // 用|连接所有元素
            reply = String.join("|", labels);
        } catch (Exception e) {
            log.error("解析标签JSON失败", e);
            reply = "";
        }

        log.info("report label success, cost: {} ms", (System.currentTimeMillis() - start));
        return reply;
    }


}
