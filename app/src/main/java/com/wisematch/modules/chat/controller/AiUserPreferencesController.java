package com.wisematch.modules.chat.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.entity.AiUserPreferences;
import com.wisematch.modules.chat.factories.PreferenceFactories;
import com.wisematch.modules.chat.model.AiUserPreferencesQueryDTO;
import com.wisematch.modules.chat.model.IdRequest;
import com.wisematch.modules.chat.model.UserPreferenceVO;
import com.wisematch.modules.chat.service.AiUserPreferencesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/userPreferences")
@Tag(name = "用户偏好")
@Slf4j
public class AiUserPreferencesController {

    @Autowired
    private AiUserPreferencesService aiUserPreferencesService;


    @PostMapping("/getById")
    @Operation(summary = "根据ID查询")
    public R getById(@RequestBody IdRequest request) {
        AiUserPreferences data = aiUserPreferencesService.getById(request.getId());
        return R.ok().setData(data);
    }

    @PostMapping("/pageQuery")
    @Operation(summary = "分页查询")
    public R pageQuery(@RequestBody AiUserPreferencesQueryDTO queryDTO) {
        Page<AiUserPreferences> page = aiUserPreferencesService.pageQuery(queryDTO);
        return R.ok().setData(page);
    }

    @Operation(summary = "偏好事件触发")
    @PostMapping("/click")
    @NotDoubleSubmit
    public R click(@RequestBody UserPreferenceVO userPreferenceVO) {
        userPreferenceVO.setUserId(UserInfoUtils.getCurrentUserId());
        aiUserPreferencesService.saveOrUpdate(userPreferenceVO);
        return R.ok();
    }

    @PostMapping("/storeList")
    @Operation(summary = "收藏的列表")
    public R storeList(@RequestBody AiUserPreferencesQueryDTO queryDTO) {
        return R.ok().setData(PreferenceFactories
                .showFactory(queryDTO.getBizSence())
                .storeList(queryDTO));
    }

}

