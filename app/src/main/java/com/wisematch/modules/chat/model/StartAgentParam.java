package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 启动智能体参数
 * <AUTHOR>
 * @version ChatParams.java, v0.1 2025-06-17 18:19
 */
@Data
public class StartAgentParam {

    /**
     * 用户Id
     */
    @Schema(description = "用户Id")
    String userId;

    /**
     * roomId
     */
    @Schema(description = "roomId")
    String roomId;

    /**
     * 岗位ID
     */
    @Schema(description = "岗位ID")
    String positionId;

    /**
     * taskId
     */
    @Schema(description = "taskId")
    String taskId = "viewer_agent_task";
    /**
     * 面试官智能体Id
     */
    @Schema(description = "面试官智能体Id")
    String agentId = "1";

    /**
     * 面试官智能体名字
     */
    @Schema(description = "面试官智能体名字")
    String agentName;

    /**
     * 面试官智能体头像
     */
    @Schema(description = "面试官智能体头像")
    String agentPhoto;
    /**
     * 面试官智能体声音
     */
    @Schema(description = "面试官智能体声音")
    String agentVoice;

    /**
     * 简历文本
     */
    @Schema(description = "简历文本")
    String resumeTxt;

    /**
     * 面试配置透传
     */
    String settings;

    /**
     * 平台类型:PC/APP/MINI/H5
     */
    String platform = "PC";


    String userAgent;

    String welcome;

}
