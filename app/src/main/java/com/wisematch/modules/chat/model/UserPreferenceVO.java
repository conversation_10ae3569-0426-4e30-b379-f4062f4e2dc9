package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPreferenceVO {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;

    @Schema(description = "消息或卡片ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String targetId;

    @Schema(description = "收藏状态，0:默认，1-收藏", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer collect;

    @Schema(description = "点赞点踩状态，-1:踩，1:赞，0:默认", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer thumbsUp;

    @Schema(description = "业务场景：WISER_REPLY/MATCHER_REPLY/cardType(传具体的卡片类型字段)", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bizSence;

    @Schema(description = "额外信息", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String extra;

    @Schema(description = "是否查看：0未查看，1查看", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer viewStatus;
}
