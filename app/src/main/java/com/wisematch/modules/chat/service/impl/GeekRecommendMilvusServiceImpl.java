package com.wisematch.modules.chat.service.impl;

import com.wisematch.common.utils.ThreadPoolUtil;
import com.wisematch.modules.chat.config.MilvusTemplate;
import com.wisematch.modules.chat.convertor.*;
import com.wisematch.modules.chat.entity.*;
import com.wisematch.modules.chat.enums.*;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.*;
import com.wisematch.modules.chat.utils.EmbeddingUtils;
import io.milvus.v2.common.IndexParam;
import io.milvus.v2.service.vector.response.QueryResp;
import io.milvus.v2.service.vector.response.SearchResp;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class GeekRecommendMilvusServiceImpl implements GeekRecommendMilvusService {

    // 集合名称常量定义
    private static final String RESUME_PROFILES_COLLECTION = "geek_recommendation_resume_profiles";
    private static final String EDUCATION_EXPERIENCES_COLLECTION = "geek_recommendation_education_experiences";
    private static final String WORK_EXPERIENCES_COLLECTION = "geek_recommendation_work_experiences";
    private static final String PROJECT_EXPERIENCES_COLLECTION = "geek_recommendation_project_experiences";
    private static final String SCHOOL_EXPERIENCES_COLLECTION = "geek_recommendation_school_experiences";
    private static final String REPORT_COLLECTION = "geek_recommendation_report";
    private static final String PORTRAIT_DIMENSION_COLLECTION = "geek_recommendation_portrait_dimension";
    private static final String PORTRAIT_EVALUATION_COLLECTION = "geek_recommendation_portrait_evaluation";

    private static final String GEEK_QUERY_COLLECTION = "geek_query";
    private static final List<String> GEEK_QUERY_TEXT_FIELDS = List.of("key_label", "labels", "summary");

    @Resource
    private MilvusTemplate milvusTemplate;

    @Resource
    private EmbeddingUtils embeddingUtils;


    @Override
    public List<String> chatRecommend(GeekIntent geekIntent, String jobExamineId) {
        try {
            // 并行搜索任务列表
            List<CompletableFuture<List<String>>> futures = new ArrayList<>();

            // 1. 简历主档案搜索任务
            List<String> profileTexts = collectProfileSearchTexts(geekIntent);
            int profileLimit = profileTexts.isEmpty() ? 3 : 5; // 权重调整：无文本5个，有文本8个
            futures.add(CompletableFuture.supplyAsync(() ->
                    searchCollection(RESUME_PROFILES_COLLECTION,
                            profileTexts,
                            combineFilters(buildResumeProfilesFilter(geekIntent), buildBasicFilter(jobExamineId)),
                            "profiles_vector", profileLimit), ThreadPoolUtil.getPool()));

            // 2. 教育经历搜索任务
            List<String> eduTexts = collectEduExperienceSearchTexts(geekIntent);
            int eduLimit = eduTexts.isEmpty() ? 0 : 3; // 权重调整：无文本0个，有文本5个
            futures.add(CompletableFuture.supplyAsync(() ->
                    searchCollection(EDUCATION_EXPERIENCES_COLLECTION,
                            eduTexts,
                            buildBasicFilter(jobExamineId), "education_vector", eduLimit), ThreadPoolUtil.getPool()));

            // 3. 工作经历搜索任务
            List<String> workTexts = collectWorkExperienceSearchTexts(geekIntent);
            int workLimit = workTexts.isEmpty() ? 0 : 3; // 权重调整：无文本0个，有文本5个
            futures.add(CompletableFuture.supplyAsync(() ->
                    searchCollection(WORK_EXPERIENCES_COLLECTION,
                            workTexts,
                            buildBasicFilter(jobExamineId), "work_vector", workLimit), ThreadPoolUtil.getPool()));

            // 4. 项目经历搜索任务
            List<String> projectTexts = collectProjectExperienceSearchTexts(geekIntent);
            int projectLimit = projectTexts.isEmpty() ? 0 : 3; // 权重调整：无文本0个，有文本5个
            futures.add(CompletableFuture.supplyAsync(() ->
                    searchCollection(PROJECT_EXPERIENCES_COLLECTION,
                            projectTexts,
                            buildBasicFilter(jobExamineId), "project_vector", projectLimit), ThreadPoolUtil.getPool()));

            // 5. 校园经历搜索任务
            List<String> schoolTexts = collectSchoolExperienceSearchTexts(geekIntent);
            int schoolLimit = schoolTexts.isEmpty() ? 0 : 3; // 权重调整：无文本0个，有文本5个
            futures.add(CompletableFuture.supplyAsync(() ->
                    searchCollection(SCHOOL_EXPERIENCES_COLLECTION,
                            schoolTexts,
                            buildBasicFilter(jobExamineId), "school_vector", schoolLimit), ThreadPoolUtil.getPool()));

            // 6. 面试报告搜索任务
            List<String> reportTexts = collectInterviewReportSearchTexts(geekIntent);
            int reportLimit = reportTexts.isEmpty() ? 0 : 3; // 权重调整：无文本0个，有文本3个
            futures.add(CompletableFuture.supplyAsync(() ->
                    searchCollection(REPORT_COLLECTION,
                            reportTexts,
                            combineFilters(buildReportFilter(geekIntent), buildBasicFilter(jobExamineId)),
                            "report_vector", reportLimit), ThreadPoolUtil.getPool()));

            // 7. 人才画像维度搜索任务
            List<String> portraitDimensionTexts = collectPortraitDimensionSearchTexts(geekIntent);
            int portraitDimensionLimit = portraitDimensionTexts.isEmpty() ? 0 : 2; // 权重调整：无文本0个，有文本2个
            futures.add(CompletableFuture.supplyAsync(() ->
                    searchCollection(PORTRAIT_DIMENSION_COLLECTION,
                            portraitDimensionTexts,
                            combineFilters(buildPortraitDimensionFilter(geekIntent), buildBasicFilter(jobExamineId)),
                            "portrait_vector", portraitDimensionLimit), ThreadPoolUtil.getPool()));

            // 8. 人才画像评估搜索任务
            List<String> portraitEvaluationTexts = collectPortraitEvaluationSearchTexts(geekIntent);
            int portraitEvaluationLimit = portraitEvaluationTexts.isEmpty() ? 0 : 2; // 权重调整：无文本0个，有文本2个
            futures.add(CompletableFuture.supplyAsync(() ->
                    searchCollection(PORTRAIT_EVALUATION_COLLECTION,
                            portraitEvaluationTexts,
                            buildBasicFilter(jobExamineId), "portrait_vector", portraitEvaluationLimit), ThreadPoolUtil.getPool()));

            // 等待所有搜索任务完成并收集结果，最多等待2秒
            List<String> allRoomIds = new ArrayList<>();
            try {
                CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                allOf.get(2, TimeUnit.SECONDS); // 最多等待2秒

                // 所有任务在2秒内完成，收集结果
                allRoomIds = futures.stream()
                        .map(CompletableFuture::join)
                        .flatMap(List::stream)
                        .distinct() // 去重
                        .toList();

            } catch (TimeoutException e) {
                log.warn("搜索任务超时，2秒内未完成所有搜索，收集已完成的结果");
                // 超时时收集已完成的任务结果
                allRoomIds = futures.stream()
                        .filter(CompletableFuture::isDone) // 只处理已完成的任务
                        .map(future -> {
                            try {
                                return future.get(); // 已完成的任务立即返回结果
                            } catch (Exception ex) {
                                log.warn("获取已完成任务结果失败", ex);
                                return Collections.<String>emptyList();
                            }
                        })
                        .flatMap(List::stream)
                        .distinct() // 去重
                        .collect(Collectors.toList());
            } catch (InterruptedException e) {
                log.error("搜索任务被中断", e);
                Thread.currentThread().interrupt();
            } catch (ExecutionException e) {
                log.error("搜索任务执行异常", e);
            }

            log.info("牛人推荐搜索完成，共找到{}个候选人", allRoomIds.size());

            // 记录权重分配情况
            log.info("权重分配情况 - 简历主档案:{}, 教育经历:{}, 工作经历:{}, 项目经历:{}, 校园经历:{}, 面试报告:{}, 画像维度:{}, 画像评估:{}",
                    profileLimit, eduLimit, workLimit, projectLimit, schoolLimit, reportLimit, portraitDimensionLimit, portraitEvaluationLimit);

            return allRoomIds;

        } catch (Exception e) {
            log.error("执行牛人推荐搜索时发生异常", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> query(String search, String expectedCity, String positionId, Integer pageNum, Integer pageSize) {
        // 1. 处理分页参数
        int page = Optional.ofNullable(pageNum).filter(p -> p > 0).orElse(1);
        int size = Optional.ofNullable(pageSize).filter(s -> s > 0).orElse(10);
        long offset = (long) (page - 1) * size;

        // 2. 拆分搜索关键词（按,|｜和空格拆分）
        List<String> keywords = Optional.ofNullable(search)
                .filter(StringUtils::hasText)
                .map(s -> Arrays.stream(
                                // 正则表达式：匹配,、|、｜（中文竖线）、空格中的任意一种作为分隔符
                                s.split("[,，|｜\\s]+")
                        )
                        .filter(StringUtils::hasText)  // 过滤空字符串
                        .distinct()                    // 去重
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());

        // 3. 构建查询条件
        String filter = buildFilter(keywords, expectedCity, positionId);
        if (filter == null) {
            return Collections.emptyList();
        }

        // 4. 执行查询
        QueryResp resp = milvusTemplate.query(GEEK_QUERY_COLLECTION, filter, Collections.singletonList("room_id"), size, offset);

        // 5. 处理结果并返回（以userId为例）
        return Optional.ofNullable(resp)
                .map(QueryResp::getQueryResults)
                .orElse(Collections.emptyList())
                .stream()
                .map(result -> Optional.ofNullable(result.getEntity().get("room_id"))
                        .map(Object::toString)
                        .orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 构建Milvus查询过滤条件
     */
    private String buildFilter(List<String> keywords, String expectedCity, String positionId) {
        return Stream.of(
                        // 处理文本匹配条件
                        keywords.isEmpty() ? null : buildTextMatchCondition(keywords),
                        // 处理城市精确匹配
                        StringUtils.hasText(expectedCity) ?
                                String.format("expected_city == '%s'", expectedCity) : null,
                        // 处理职位ID精确匹配
                        StringUtils.hasText(positionId) ?
                                String.format("position_id == '%s'", positionId) : null,

                        "del_status == " + WiserConstant.NOT_DELETE,
                        "portrait_status == 1",
                        "portrait_verify_status == 1",
                        "status == " + WiserConstant.IN_USE

                )
                .filter(Objects::nonNull)
                .collect(Collectors.joining(" AND "));
    }


    /**
     * 构建文本匹配条件（关键修复：为OR条件添加括号）
     */
    private String buildTextMatchCondition(List<String> keywords) {
        return keywords.stream()
                // 每个关键词的多字段匹配用OR连接，并整体包裹在括号中
                .map(keyword -> GEEK_QUERY_TEXT_FIELDS.stream()
                        .map(field -> String.format("TEXT_MATCH(%s, '%s')",
                                field, keyword))
                        .collect(Collectors.joining(" OR ", "(", ")")))
                // 所有关键词条件用AND连接
                .collect(Collectors.joining(" AND "));
    }


    /**
     * 查询指定集合中符合条件的记录ID
     */
    private List<Long> queryRecordIds(String collectionName, String filter) {
        try {
            QueryResp resp = milvusTemplate.query(collectionName, filter, List.of("id"), 16384, 0);
            if (resp != null && resp.getQueryResults() != null && !resp.getQueryResults().isEmpty()) {
                List<Long> ids = resp.getQueryResults().stream()
                        .map(item -> (Long) item.getEntity().get("id"))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                log.info("查询集合{}符合条件的旧记录{}条: {}", collectionName, ids.size(), filter);
                return ids;
            }
        } catch (Exception e) {
            log.error("查询集合{}记录ID时发生异常: {}", collectionName, filter, e);
        }
        return Collections.emptyList();
    }



    /**
     * 按照ID删除旧记录
     */
    private void deleteOldRecordsById(Map<String, List<Long>> oldRecordIds) {
        oldRecordIds.forEach((collectionName, ids) -> {
            if (!ids.isEmpty()) {
                try {
                    boolean deleteSuccess = milvusTemplate.deleteById(collectionName, ids);
                    if (deleteSuccess) {
                        log.info("成功删除集合{}中的{}条旧记录", collectionName, ids.size());
                    } else {
                        log.error("删除集合{}中的{}条旧记录失败", collectionName, ids.size());
                    }
                } catch (Exception e) {
                    log.error("删除集合{}中的旧记录时发生异常", collectionName, e);
                }
            }
        });
    }

    /**
     * 搜索单个集合
     */
    private List<String> searchCollection(String collectionName, List<String> vectorTexts, String filter, String embeddingName, Integer limit) {
        try {
            log.info("开始搜索集合: {}, 向量文本数量: {}, 过滤条件: {}",
                    collectionName, vectorTexts.size(), filter);

            if (limit == 0) {
                log.info("跳过集合: {}，因为limit为0", collectionName);
                return Collections.emptyList();
            }

            // 如果没有向量文本，执行标量查询
            if (vectorTexts.isEmpty()) {
                return performScalarQuery(collectionName, filter, limit);
            }

            // 向量化文本
            List<List<Double>> embeddings = embeddingUtils.embedTexts(vectorTexts, 768);

            // 转换为Float格式（Milvus要求）
            List<List<Float>> floatEmbeddings = embeddings.stream()
                    .map(embedding -> embedding.stream()
                            .map(Double::floatValue)
                            .collect(Collectors.toList()))
                    .collect(Collectors.toList());

            // 执行向量搜索
            SearchResp searchResp = milvusTemplate.search(
                    collectionName,
                    floatEmbeddings,
                    limit,
                    List.of("room_id"),
                    filter,
                    embeddingName, // 向量字段名
                    IndexParam.MetricType.COSINE,
                    buildSearchParams(),
                    "room_id", // 按room_id分组
                    1 // 每组返回1个结果
            );

            // 处理搜索结果
            List<String> roomIds = processSearchResults(searchResp, collectionName);
            log.info("集合{}搜索完成，返回{}个结果", collectionName, roomIds.size());
            return roomIds;

        } catch (Exception e) {
            log.error("搜索集合{}失败", collectionName, e);
            // 降级到标量查询
            return performScalarQuery(collectionName, filter, limit);
        }
    }

    /**
     * 执行标量查询（降级方案）
     */
    private List<String> performScalarQuery(String collectionName, String filter, Integer limit) {
        try {
            log.info("执行标量查询，集合: {}, 过滤条件: {}", collectionName, filter);

            // 如果过滤条件为空，直接返回空列表
            if (!StringUtils.hasLength(filter)) {
                log.info("过滤条件为空，跳过集合: {}", collectionName);
                return Collections.emptyList();
            }

            QueryResp queryResp = milvusTemplate.query(
                    collectionName,
                    filter,
                    List.of("room_id"),
                    limit, // 限制返回数量
                    0
            );

            return Optional.ofNullable(queryResp)
                    .map(QueryResp::getQueryResults)
                    .filter(results -> !results.isEmpty())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(result -> {
                        try {
                            return result.getEntity().get("room_id").toString();
                        } catch (Exception e) {
                            log.warn("提取room_id失败", e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("标量查询失败，集合: {}", collectionName, e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理搜索结果，提取room_id
     */
    private List<String> processSearchResults(SearchResp searchResp, String collectionName) {
        return Optional.ofNullable(searchResp)
                .map(SearchResp::getSearchResults)
                .filter(results -> !results.isEmpty())
                .orElse(Collections.emptyList())
                .stream()
                .flatMap(Collection::stream)
                .map(result -> {
                    try {
                        return result.getEntity().get("room_id").toString();
                    } catch (Exception e) {
                        log.warn("从集合{}提取room_id失败", collectionName, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 构建基础过滤条件
     */
    private String buildBasicFilter(String jobExamineId) {
        List<String> conditions = new ArrayList<>();

        // 添加examine_id条件
        if (StringUtils.hasLength(jobExamineId)) {
            conditions.add("examine_id == '" + jobExamineId + "'");
        }

        // 添加del_status条件（只查询未删除的记录）
        conditions.add("del_status == " + WiserConstant.NOT_DELETE);

        //画像状态
        conditions.add("portrait_status == 1");
        //画像审核状态
        conditions.add("portrait_verify_status == 1");
        conditions.add("status == " + WiserConstant.IN_USE);
        // 使用AND连接所有条件
        return String.join(" and ", conditions);
    }

    /**
     * 组合多个过滤条件
     */
    private String combineFilters(String... filters) {
        return Arrays.stream(filters)
                .filter(Objects::nonNull)
                .filter(filter -> !filter.trim().isEmpty())
                .collect(Collectors.joining(" and "));
    }

    /**
     * 构建搜索参数
     */
    private Map<String, Object> buildSearchParams() {
        Map<String, Object> searchParams = new HashMap<>();
        searchParams.put("nprobe", 16); // IVF索引探测参数
        return searchParams;
    }

    private List<String> collectWorkExperienceSearchTexts(GeekIntent geekIntent) {
        return Stream.of(
                        // 工作经历文本
                        Optional.ofNullable(geekIntent.getWorkExperience()).stream().flatMap(workExp -> Stream.of(
                                workExp.getCompanyName(),
                                workExp.getPosition(),
                                workExp.getDesc()
                        ))
                )
                .flatMap(Function.identity()) // 扁平化所有流
                .filter(Objects::nonNull)      // 过滤null值
                .flatMap(Collection::stream)
                .filter(StringUtils::hasLength) // 过滤空字符串
                .distinct()                    // 去重
                .toList();
    }

    private List<String> collectEduExperienceSearchTexts(GeekIntent geekIntent) {
        return Stream.of(
                        // 教育经历文本
                        Optional.ofNullable(geekIntent.getEduExperience()).stream().flatMap(eduExp -> Stream.of(
                                eduExp.getSchoolName(),
                                eduExp.getMajor(),
                                eduExp.getDegree(),
                                eduExp.getDesc()
                        ))
                )
                .flatMap(Function.identity()) // 扁平化所有流
                .filter(Objects::nonNull)      // 过滤null值
                .flatMap(Collection::stream)
                .filter(StringUtils::hasLength) // 过滤空字符串
                .distinct()                    // 去重
                .toList();
    }

    private List<String> collectSchoolExperienceSearchTexts(GeekIntent geekIntent) {
        return Stream.of(
                        // 校园经历文本
                        Optional.ofNullable(geekIntent.getSchoolExperience()).stream().flatMap(schoolExp -> Stream.of(
                                schoolExp.getOrganizationName(),
                                schoolExp.getPosition(),
                                schoolExp.getDesc()
                        ))
                )
                .flatMap(Function.identity()) // 扁平化所有流
                .filter(Objects::nonNull)      // 过滤null值
                .flatMap(Collection::stream)
                .filter(StringUtils::hasLength) // 过滤空字符串
                .distinct()                    // 去重
                .toList();
    }

    private List<String> collectProjectExperienceSearchTexts(GeekIntent geekIntent) {
        return Stream.of(
                        // 校园经历文本
                        Optional.ofNullable(geekIntent.getProjectExperience()).stream().flatMap(projectExp -> Stream.of(
                                projectExp.getProjectName(),
                                projectExp.getProjectRole(),
                                projectExp.getDesc()
                        ))
                )
                .flatMap(Function.identity()) // 扁平化所有流
                .filter(Objects::nonNull)      // 过滤null值
                .flatMap(Collection::stream)
                .filter(StringUtils::hasLength) // 过滤空字符串
                .distinct()                    // 去重
                .toList();
    }

    private List<String> collectProfileSearchTexts(GeekIntent geekIntent) {
        return Stream.of(
                        // 简历主档案文本
                        Optional.ofNullable(geekIntent.getProfile()).stream().flatMap(profile -> Stream.of(
                                profile.getSkills(),
                                profile.getAdditionalInfo(),
                                profile.getWorkExperience()
                        ))
                )
                .flatMap(Function.identity()) // 扁平化所有流
                .filter(Objects::nonNull)      // 过滤null值
                .flatMap(Collection::stream)
                .filter(StringUtils::hasLength) // 过滤空字符串
                .distinct()                    // 去重
                .toList();
    }

    private List<String> collectInterviewReportSearchTexts(GeekIntent geekIntent) {
        return Stream.of(

                        // 面试报告文本
                        Optional.ofNullable(geekIntent.getInterviewReport()).stream().flatMap(reports -> reports.stream()
                                .map(GeekIntent.InterviewReport::getContent))
                )
                .flatMap(Function.identity()) // 扁平化所有流
                .filter(Objects::nonNull)      // 过滤null值
                .flatMap(Collection::stream)
                .filter(StringUtils::hasLength) // 过滤空字符串
                .distinct()                    // 去重
                .toList();
    }

    private List<String> collectPortraitDimensionSearchTexts(GeekIntent geekIntent) {
        return Stream.of(

                        // 人才画像维度文本
                        Optional.ofNullable(geekIntent.getTalentPortraitDimension()).stream().flatMap(dimensions -> dimensions.stream()
                                .flatMap(dimension -> Stream.of(
                                        dimension.getAdvantages()
                                )))
                )
                .flatMap(Function.identity()) // 扁平化所有流
                .filter(Objects::nonNull)      // 过滤null值
                .flatMap(Collection::stream)
                .filter(StringUtils::hasLength) // 过滤空字符串
                .distinct()                    // 去重
                .toList();
    }

    private List<String> collectPortraitEvaluationSearchTexts(GeekIntent geekIntent) {
        return Stream.of(
                        // 人才画像评估文本
                        Optional.ofNullable(geekIntent.getTalentPortraitEvaluation()).stream().flatMap(evaluation -> Stream.of(
                                evaluation.getSummary(),
                                evaluation.getHighlights()
                        ))
                )
                .flatMap(Function.identity()) // 扁平化所有流
                .filter(Objects::nonNull)      // 过滤null值
                .flatMap(Collection::stream)
                .filter(StringUtils::hasLength) // 过滤空字符串
                .distinct()                    // 去重
                .toList();
    }


    /**
     * 构建简历主档案的标量过滤条件
     */
    private String buildResumeProfilesFilter(GeekIntent geekIntent) {
        List<String> scalarFilters = new ArrayList<>();

        if (geekIntent.getProfile() != null) {
            GeekIntent.Profile profile = geekIntent.getProfile();

            // 最高学历过滤
            if (profile.getHighestEducation() != null && !profile.getHighestEducation().isEmpty()) {
                List<String> educationFilters = profile.getHighestEducation().stream()
                        .filter(StringUtils::hasLength)
                        .map(edu -> {
                            GeekEducationEnum education = GeekEducationEnum.getByName(edu);
                            int code = education.getCode();
                            // 根据学历代码构建过滤条件，包含所有大于等于该代码的学历
                            return "highest_education >= " + code;
                        })
                        .collect(Collectors.toList());
                if (!educationFilters.isEmpty()) {
                    scalarFilters.add("(" + String.join(" or ", educationFilters) + ")");
                }
            }

            // 薪资过滤
            if (profile.getSalary() != null && !profile.getSalary().isEmpty()) {
                List<String> salaryFilters = profile.getSalary().stream()
                        .filter(Objects::nonNull)
                        .map(salary -> {
                            List<String> salaryConditions = new ArrayList<>();
                            if (StringUtils.hasLength(salary.getMin())) {
                                salaryConditions.add("salary_max >= " + salary.getMin());
                            }
                            if (StringUtils.hasLength(salary.getMax())) {
                                salaryConditions.add("salary_min <= " + salary.getMax());
                            }
                            return salaryConditions.isEmpty() ? null : "(" + String.join(" and ", salaryConditions) + ")";
                        })
                        .filter(Objects::nonNull)
                        .toList();
                if (!salaryFilters.isEmpty()) {
                    scalarFilters.add("(" + String.join(" or ", salaryFilters) + ")");
                }
            }

            // 工作方式过滤
            if (profile.getWorkWay() != null && !profile.getWorkWay().isEmpty()) {
                List<String> workWayFilters = profile.getWorkWay().stream()
                        .filter(StringUtils::hasLength)
                        .map(way -> {
                            RecruitmentType recruitmentType = RecruitmentType.getByName(way);
                            return "work_way == " + recruitmentType.getCode();
                        })
                        .toList();
                if (!workWayFilters.isEmpty()) {
                    scalarFilters.add("(" + String.join(" or ", workWayFilters) + ")");
                }
            }

            // 性别过滤
            if (profile.getSex() != null && !profile.getSex().isEmpty()) {
                List<String> sexFilters = new ArrayList<>();
                for (Integer sexValue : profile.getSex()) {
                    // 如果选择的是未知(0)，则匹配所有性别(0,1,2)
                    if (sexValue == 0) {
                        sexFilters.add("sex == 0 or sex == 1 or sex == 2");
                    } else {
                        SexEnum sexEnum = SexEnum.getByDtoValue(sexValue);
                        sexFilters.add("sex == " + sexEnum.getDtoValue());
                    }
                }

                if (!sexFilters.isEmpty()) {
                    scalarFilters.add("(" + String.join(" or ", sexFilters) + ")");
                }
            }

            // 工作经验格式定不了，先使用向量搜
        }

        return scalarFilters.isEmpty() ? null : String.join(" and ", scalarFilters);
    }

    /**
     * 构建面试报告的标量过滤条件
     * 逻辑：(考核维度 == A and (考核点 == B or 考核点 == C)) or (考核维度 == D and (考核点 == E or 考核点 == F))
     */
    private String buildReportFilter(GeekIntent geekIntent) {
        // 空值快速判断
        if (geekIntent == null || geekIntent.getInterviewReport() == null || geekIntent.getInterviewReport().isEmpty()) {
            return null;
        }

        // 处理每个面试报告项，生成"(维度条件 and 考核点条件)"格式的过滤条件，不同维度间用OR连接
        List<String> conditions = geekIntent.getInterviewReport().stream()
                .map(report -> {
                    List<String> reportConditions = new ArrayList<>();

                    // 维度过滤条件
                    Optional.ofNullable(report.getDimensions())
                            .filter(StringUtils::hasLength)
                            .ifPresent(dim -> reportConditions.add("dimensions == '" + dim + "'"));

                    // 考核点过滤条件
                    Optional.ofNullable(report.getCheckPoint())
                            .filter(checkPoints -> !checkPoints.isEmpty())
                            .ifPresent(checkPoints -> {
                                List<String> checkPointConditions = checkPoints.stream()
                                        .filter(StringUtils::hasLength)
                                        .map(cp -> "checkpoints == '" + cp + "'")
                                        .collect(Collectors.toList());
                                if (!checkPointConditions.isEmpty()) {
                                    reportConditions.add("(" + String.join(" or ", checkPointConditions) + ")");
                                }
                            });

                    // 同一报告项内的条件用and连接，并包裹在括号中
                    return reportConditions.isEmpty() ? null : "(" + String.join(" and ", reportConditions) + ")";
                })
                // 过滤掉空条件
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (conditions.isEmpty()) {
            return null;
        }

        // 在外层添加括号，确保与其他条件拼接时的逻辑正确性
        return "(" + String.join(" or ", conditions) + ")";
    }


    /**
     * 构建人才画像维度的标量过滤条件
     */
    private String buildPortraitDimensionFilter(GeekIntent geekIntent) {
        // 空值快速判断
        if (geekIntent == null || geekIntent.getTalentPortraitDimension() == null || geekIntent.getTalentPortraitDimension().isEmpty()) {
            return null;
        }

        // 处理维度过滤条件
        return geekIntent.getTalentPortraitDimension().stream()
                .filter(Objects::nonNull)
                .map(GeekIntent.TalentPortraitDimension::getDimensionName)
                .filter(StringUtils::hasLength)
                .map(dimension -> "dimensions == '" + dimension + "'")
                .collect(Collectors.collectingAndThen(
                        Collectors.toList(),
                        dimensions -> {
                            if (dimensions.isEmpty()) {
                                return null;
                            }
                            // 多个维度用or连接并包裹在括号中
                            return "(" + String.join(" or ", dimensions) + ")";
                        }
                ));
    }
}