package com.wisematch.modules.chat.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.modules.chat.entity.AiChatMatcherMemory;
import com.wisematch.modules.chat.enums.MsgRole;
import com.wisematch.modules.chat.mapper.AiChatMatcherMemoryMapper;
import com.wisematch.modules.chat.model.AiChatUserMsg;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.model.UserPreferenceVO;
import com.wisematch.modules.chat.service.AiChatMatcherMemoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * matcher记忆服务
 *
 * <AUTHOR>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiChatMatcherMemoryServiceImpl extends ServiceImpl<AiChatMatcherMemoryMapper, AiChatMatcherMemory> implements AiChatMatcherMemoryService {

    @Override
    public void add(String conversationId, AiChatMatcherMemory aiChatWiserMemory) {
        aiChatWiserMemory.setTimestamp(new Date());
        this.baseMapper.insert(aiChatWiserMemory);
    }

    @Override
    public void addUserMemory(String conversationId, AiChatUserMsg aiChatUserMsg) {
        AiChatMatcherMemory aiChatMemory = new AiChatMatcherMemory();
        aiChatMemory.setType(MsgRole.user.name());
        aiChatMemory.setContent(JSONObject.toJSONString(aiChatUserMsg));
        aiChatMemory.setMsg(aiChatUserMsg.getMsg());
        aiChatMemory.setTimestamp(new Date());
        aiChatMemory.setConversationId(conversationId);
        this.baseMapper.insert(aiChatMemory);
    }

    @Override
    public AiChatMatcherMemory addAssistantMemory(String conversationId, String message) {
        AiChatMatcherMemory aiChatMemory = new AiChatMatcherMemory();
        aiChatMemory.setType(MsgRole.assistant.name());
        aiChatMemory.setContent(message);
        aiChatMemory.setTimestamp(new Date());
        aiChatMemory.setConversationId(conversationId);
        this.baseMapper.insert(aiChatMemory);
        return aiChatMemory;
    }

    @Override
    public AiChatMatcherMemory updateAssistantMemory(String id, ChatMessage chatMessage, String message) {
        AiChatMatcherMemory aiChatMatcherMemory = this.getById(id);
        aiChatMatcherMemory.setContent(JSONObject.toJSONString(chatMessage));
        aiChatMatcherMemory.setMsg(message);
        this.updateById(aiChatMatcherMemory);
        return aiChatMatcherMemory;
    }

    @Override
    public void addAssistantMemory(String conversationId, List<ChatMessage> messages) {
        if (messages != null && !messages.isEmpty()) {
            for (ChatMessage msg : messages) {
                AiChatMatcherMemory aiChatMatcherMemory = this.addAssistantMemory(conversationId, JSONObject.toJSONString(msg));
                msg.setId(String.valueOf(aiChatMatcherMemory.getId()));
            }
        }
    }

    @Override
    public void updateUserPreferences(UserPreferenceVO userPreferenceVO) {
        AiChatMatcherMemory aiChatMatcherMemory = this.getById(userPreferenceVO.getTargetId());
        aiChatMatcherMemory.setCollect(userPreferenceVO.getCollect());
        aiChatMatcherMemory.setThumbsUp(userPreferenceVO.getThumbsUp());
        aiChatMatcherMemory.setExtra(userPreferenceVO.getExtra());
        this.baseMapper.updateById(aiChatMatcherMemory);
    }

    @Override
    public List<AiChatMatcherMemory> getList(String conversationId) {
        QueryWrapper<AiChatMatcherMemory> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiChatMatcherMemory::getConversationId, conversationId).orderByAsc(AiChatMatcherMemory::getId);
        List<AiChatMatcherMemory> list = this.baseMapper.selectList(queryWrapper);
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<org.springframework.ai.chat.messages.Message> getListAsMessages(String chatId) {
        List<AiChatMatcherMemory> history = this.getList(chatId);
        List<org.springframework.ai.chat.messages.Message> messages = new ArrayList<>();
        if (history != null) {
            messages = history.stream().map(x -> {
                JSONObject jsonObject = JSONObject.parseObject(x.getContent());
                String content = jsonObject.getString("msg");
                if (jsonObject.get("cardInfos") != null) {
                    content = content + " ### cardInfos:" + jsonObject.get("cardInfos");
                }
                if (x.getType().equals(MessageType.ASSISTANT.getValue())) {
//                    if (x.getMsg() != null && x.getMsg().contains("==")) {
//                        x.setMsg(x.getMsg().substring(0, x.getMsg().indexOf("==")));
//                    }
                    return new AssistantMessage(content);
                } else {
                    return new UserMessage(content);
                }
            }).collect(Collectors.toList());
        }
        return messages;
    }

}
