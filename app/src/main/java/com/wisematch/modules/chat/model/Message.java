package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version Message.java, v0.1 2025-06-20 17:02
 */
@Data
public class Message {
    /**
     * 消息id
     */
    @Schema(description = "消息id")
    String id;
    /**
     * 回答消息内容
     */
    @Schema(description = "回答消息内容")
    String msg;

    /**
     * 角色
     */
    @Schema(description = "角色")
    String role;
}
