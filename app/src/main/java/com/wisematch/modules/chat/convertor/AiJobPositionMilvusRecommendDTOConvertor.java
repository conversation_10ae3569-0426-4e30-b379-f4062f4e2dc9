package com.wisematch.modules.chat.convertor;

import com.wisematch.modules.chat.entity.AiJobPosition;
import com.wisematch.modules.chat.enums.ExperienceRequirement;
import com.wisematch.modules.chat.enums.MinimumEducation;
import com.wisematch.modules.chat.enums.RecruitmentType;
import com.wisematch.modules.chat.enums.SettlementMethod;
import com.wisematch.modules.chat.event.PositionChangeEventListener;
import com.wisematch.modules.chat.model.AiJobPositionMilvusRecommendDTO;
import com.wisematch.modules.chat.model.PositionTextWithType;
import org.springframework.util.StringUtils;

import java.util.List;

public class AiJobPositionMilvusRecommendDTOConvertor {

    public static AiJobPositionMilvusRecommendDTO convert(AiJobPosition aiJobPosition, List<Double> embeddingContent, PositionTextWithType textWithType) {
        AiJobPositionMilvusRecommendDTO dto = new AiJobPositionMilvusRecommendDTO();

        String id = aiJobPosition.getId();
        dto.setPositionId(id);

        // 设置组织ID
        String orgId = aiJobPosition.getOrgId();
        dto.setOrgId(orgId);

        String type = aiJobPosition.getType();
        dto.setRecruitmentType(RecruitmentType.getByName(type).getCode());
        dto.setSalaryMin(aiJobPosition.getMinPay());
        dto.setSalaryMax(aiJobPosition.getMaxPay());
        dto.setSalaryMonth(12);

        String workExperience = aiJobPosition.getWorkExperience();
        dto.setWorkExperience(ExperienceRequirement.getByName(workExperience).getCode());

        String positionPayType = aiJobPosition.getPositionPayType();
        dto.setSettlementMethod(SettlementMethod.getByName(positionPayType).getCode());

        String educational = aiJobPosition.getEducational();
        dto.setEducation(MinimumEducation.getByName(educational).getCode());
        dto.setAuditStatus(aiJobPosition.getIsVerify());
        dto.setDelStatus(aiJobPosition.getIsDel());
        dto.setPositionStatus(aiJobPosition.getPositionStatus());

        String text = textWithType.getText();
        if (StringUtils.hasLength(text) && text.length() > 100) {
            // 如果文本超过100字，截取前100字
            text = text.substring(0, 100);
        }
        dto.setTextContent(text);
        dto.setTextType(textWithType.getType().getType());

        dto.setEmbedding(embeddingContent);
        return dto;
    }
}