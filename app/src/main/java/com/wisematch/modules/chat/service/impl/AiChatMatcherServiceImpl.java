package com.wisematch.modules.chat.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.cache.RedisCache;
import com.wisematch.common.utils.PageConvertUtils;
import com.wisematch.common.utils.PageUtils;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.entity.AiChatMatcher;
import com.wisematch.modules.chat.entity.AiChatMatcherMemory;
import com.wisematch.modules.chat.enums.ViewerAgentConstant;
import com.wisematch.modules.chat.enums.WiserConstant;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.handler.AiMatcherAgentFacade;
import com.wisematch.modules.chat.mapper.AiChatMatcherMapper;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.AiChatMatcherMemoryService;
import com.wisematch.modules.chat.service.AiChatMatcherService;
import com.wisematch.modules.chat.service.AiUserPreferencesService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.messages.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version AiChatWiserServiceImpl.java, v0.1 2025-06-27 20:34
 */
@Service
@Slf4j
public class AiChatMatcherServiceImpl extends ServiceImpl<AiChatMatcherMapper, AiChatMatcher> implements AiChatMatcherService {

    @Autowired
    AiUserPreferencesService aiUserPreferencesService;
    @Autowired
    private AiChatMatcherMemoryService aiChatMemoryService;
    @Autowired
    private AiMatcherAgentFacade aiMatcherAgentFacade;
    @Autowired
    private RedisCache redisCache;

    @Override
    public HomeMatcher index() {
        HomeMatcher welcome = new HomeMatcher();
        List<CardInfo> list = new ArrayList<>();
        CardInfo cardInfo  = new CardInfo();
        cardInfo.setTitle("帮我推荐一下跨境电商运营的人才");
        cardInfo.setCardType("matcher");
        CardInfo cardInfo2  = new CardInfo();
        cardInfo2.setTitle("我要发布一个招聘岗位");
        cardInfo2.setCardType("matcher");
        CardInfo cardInfo3  = new CardInfo();
        cardInfo3.setTitle("如何快速找到合适的人才需求");
        cardInfo3.setCardType("matcher");
        list.add(cardInfo);list.add(cardInfo2);list.add(cardInfo3);
        welcome.setTips(list);

        return welcome;
    }

    @Override
    public AiChatMatcher init(ChatInitParams chatInitParams) {
        AiChatMatcher aiChatMatcher = new AiChatMatcher();
        aiChatMatcher.setChatId(WiserConstant.CHAT_ROOM_ID_PRE + IdUtil.fastSimpleUUID().substring(0, 32));
        aiChatMatcher.setMatcherId(StringUtils.isNotBlank(chatInitParams.getWiserId())?chatInitParams.getWiserId(): ViewerAgentConstant.MATCHER_AGENT_ID);
        aiChatMatcher.setLastMsg("新对话");
        aiChatMatcher.setUserId(UserInfoUtils.getCurrentUserId());
        aiChatMatcher.setUserName(chatInitParams.getUserName());
        aiChatMatcher.setUserPhoto(chatInitParams.getUserPhoto());
        aiChatMatcher.setCreateTime(new Date());
        aiChatMatcher.setUpdateTime(new Date());
        this.baseMapper.insert(aiChatMatcher);
        return aiChatMatcher;
    }

    @Override
    public Flux<ChatMessage> stream(AiChatUserMsg aiChatUserMsg) {
        aiChatUserMsg.setUserId(UserInfoUtils.getCurrentUserId());

        String msgChatId = aiChatUserMsg.getChatId();

        List<Message> history = aiChatMemoryService.getListAsMessages(msgChatId);

        //新增记忆
        aiChatMemoryService.addUserMemory(msgChatId, aiChatUserMsg);

        //更新消息
        this.updateMatcherChat(aiChatUserMsg);

        ChatMessage chatMessage = new ChatMessage();
        AiChatMatcherMemory aiChatWiserMemory = aiChatMemoryService.addAssistantMemory(msgChatId, JSONObject.toJSONString(chatMessage));
        chatMessage.setId(aiChatWiserMemory.getId().toString());
        chatMessage.setChatId(msgChatId);
        AgentHandlerContext agentHandlerContext = new AgentHandlerContext();
        agentHandlerContext.setReplyId(chatMessage.getId());
        agentHandlerContext.setAiChatUserMsg(aiChatUserMsg);
        agentHandlerContext.setMessages(history);
        Flux<ChatMessage> reply = aiMatcherAgentFacade.stream(agentHandlerContext).map(x -> {
            x.setId(aiChatWiserMemory.getId().toString());
            chatMessage.setRole(x.getRole());
//            wiserMatcherReplyMessage.setName(x.getName());
//            wiserMatcherReplyMessage.setPhoto(x.getPhoto());
            if (StringUtils.isNotBlank(x.getMsg())) {
                if (chatMessage.getMsg() == null) {
                    chatMessage.setMsg(x.getMsg());
                } else {
                    chatMessage.setMsg(chatMessage.getMsg() + x.getMsg());
                }
            }
            if (x.getTips() != null) {
                chatMessage.setTips(x.getTips());
            }
            if (x.getCardInfos() != null && !x.getCardInfos().isEmpty()) {
                chatMessage.setCardInfos(x.getCardInfos());
            }
            return x;
        }).takeUntil(x -> redisCache.get(chatMessage.getId()) != null);

        ChatMessage done = new ChatMessage("[DONE]");
        done.setId(chatMessage.getId());

        return Flux.concat(reply.doFinally(x -> {
            aiChatMemoryService.updateAssistantMemory(chatMessage.getId(), chatMessage, agentHandlerContext.getReply().toString());
        }), Flux.just(done));
    }

    @Override
    public boolean stopStream(String msgId) {
        redisCache.put(msgId, "stop", 60L);
        return true;
    }

    private void updateMatcherChat(AiChatUserMsg aiChatUserMsg){
        QueryWrapper<AiChatMatcher> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiChatMatcher::getChatId, aiChatUserMsg.getChatId());
        AiChatMatcher aiChatMatcher = this.baseMapper.selectOne(queryWrapper);
        if (aiChatMatcher != null) {
            aiChatMatcher.setLastMsg(aiChatUserMsg.getMsg());
            aiChatMatcher.setUpdateTime(new Date());
            this.baseMapper.updateById(aiChatMatcher);
        }
    }


    @Override
    public List<ChatMessage<CardInfoVO>> messages(String chatId) {
        List<AiChatMatcherMemory> aiChatMemories = aiChatMemoryService.getList(chatId);
        return aiChatMemories.stream().map(x->{
            ChatMessage<CardInfoVO> chatMessage = JSONObject.parseObject(
                    x.getContent(),
                    new TypeReference<>() {}
            );
            chatMessage.setRole(x.getType());
            chatMessage.setId(x.getId().toString());
            PreferenceStatusDTO preferenceStatusDTO = new PreferenceStatusDTO();
            preferenceStatusDTO.setThumb(x.getThumbsUp());
            preferenceStatusDTO.setStored(x.getCollect());
            chatMessage.setExtra(JSONObject.parseObject(JSONObject.toJSONString(preferenceStatusDTO)));
            return aiUserPreferencesService.wrapMatcherChatMessage(chatMessage);
        }).toList();
    }

    @Override
    public List<AiChatRecord> matcherList() {
        QueryWrapper<AiChatMatcher> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiChatMatcher::getUserId, UserInfoUtils.getCurrentUserId())
                .orderByDesc(AiChatMatcher::getUpdateTime);
        return this.baseMapper.selectList(queryWrapper).stream().map(x -> {
            AiChatRecord record = new AiChatRecord();
            record.setChatId(x.getChatId());
            record.setMsg(x.getLastMsg());
            record.setUpdateTime(x.getUpdateTime());
            return record;
        }).toList();
    }

    @Override
    public Page<AiChatRecord> historyPage(HistoryPageDTO historyPageDTO) {
        QueryWrapper<AiChatMatcher> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(AiChatMatcher::getUserId, UserInfoUtils.getCurrentUserId())
                .eq(AiChatMatcher::getIsDel, WiserConstant.NOT_DELETE)
                .orderByDesc(AiChatMatcher::getUpdateTime);

        Page<AiChatMatcher> aiChatMatcherPage = PageUtils.doPage(this, historyPageDTO, queryWrapper);

        return PageConvertUtils.convert(
                aiChatMatcherPage,
                AiChatRecord.class,
                (src, dest) -> {
                    dest.setChatId(src.getChatId());
                    dest.setMsg(src.getLastMsg());
                    dest.setUpdateTime(src.getUpdateTime());
                }
        );
    }
}
