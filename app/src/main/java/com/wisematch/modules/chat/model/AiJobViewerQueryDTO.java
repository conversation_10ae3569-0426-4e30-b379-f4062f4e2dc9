package com.wisematch.modules.chat.model;


import com.wisematch.common.model.PageRequest;
import com.wisematch.modules.chat.entity.AiJobViewer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AiJobViewerQueryDTO extends AiJobViewer implements PageRequest {

    @Schema(description = "名字")
    private String name;

    @Schema(description = "头像")
    private String photo;

    @Schema(description = "声音代码")
    private String voice;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "用户Id")
    private String userId;

    @Schema(description = "所在组织Id")
    private String orgId;

    @Schema(description = "使用场景")
    private String bizScene;

    @Schema(description = "页码")
    private Integer pageNum = 1;

    @Schema(description = "每页数量")
    private Integer pageSize = 10;
}

