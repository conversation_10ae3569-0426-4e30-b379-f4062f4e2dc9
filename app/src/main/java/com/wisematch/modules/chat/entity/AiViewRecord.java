package com.wisematch.modules.chat.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wisematch.modules.chat.model.PreApplyVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version AiChatInterview.java, v0.1 2025-06-20 20:12
 */
@Data
@TableName("ai_view_record")
@Schema(description = "AI面试记录")
public class AiViewRecord implements Serializable {

    public static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    public String id;

    @Schema(description = "面试ID")
    public String roomId;

    @Schema(description = "面试ID")
    public String roomToken;

    @Schema(description = "用户ID")
    public String userId;

    @Schema(description = "申请人姓名")
    public String applyName;

    @Schema(description = "申请表格")
    public String submitForm;

    @Schema(description = "申请人手机")
    public String applyPhone;

    @Schema(description = "申请人邮箱")
    public String applyEmail;

    @Schema(description = "面试官Id")
    public String viewerId;

    @Schema(description = "面试官名称")
    public String viewerName;

    @Schema(description = "面试官头像")
    public String viewerPhoto;

    @Schema(description = "面试官声音")
    public String viewerVoice;

    @Schema(description = "面试类型")
    public String chatType;

    @Schema(description = "申请表单")
    public String applyInfos;

    @Schema(description = "申请类型")
    public String applyType;

    @Schema(description = "面试名称")
    public String chatName;

    @Schema(description = "投递岗位Id")
    public String positionId;

    @Schema(description = "模拟面试Id")
    public String trainId;

    @Schema(description = "问题提问到第几个")
    public Integer askIndex;

    @Schema(description = "当前问题追问次数")
    public Integer followTime;

    @Schema(description = "面试时长")
    public Long duration;

    @Schema(description = "面试状态")
    public String status;

    @Schema(description = "欢迎语")
    public String welcome;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    public Date createTime;

    @Schema(description = "更新时间时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date updateTime;

    @Schema(description = "面试开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date startTime;

    @Schema(description = "面试结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date endTime;

    public String bizResponse;

    public String platform;

    @Schema(description = "面试房间状态：OPEN/CLOSED")
    public String roomStatus;

    public String userAgent;


    public PreApplyVO wrapToPreApplyVO() {
        PreApplyVO preApplyVO = new PreApplyVO();
        preApplyVO.setApplyPhone(this.getApplyPhone());
        preApplyVO.setApplyEmail(this.getApplyEmail());
        preApplyVO.setApplyName(this.getApplyName());
        return preApplyVO;
    }

}
