package com.wisematch.modules.chat.agent;

import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatOptions;
import com.alibaba.fastjson.JSONObject;
import com.wisematch.common.cache.GuavaCache;
import com.wisematch.common.utils.ThreadPoolUtil;
import com.wisematch.modules.chat.entity.AiAgentPool;
import com.wisematch.modules.chat.service.AiAgentPoolService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version AgentFacade.java, v0.1 2025-07-19 13:15
 */
@Component
@Slf4j
public class AgentFacade {

    @Autowired
    private AiAgentPoolService aiAgentPoolService;

    private final ChatClient chatClient;

    private GuavaCache guavaCache;


    public AgentFacade(ChatClient.Builder builder) {
        this.chatClient = builder.build();
    }

    public String getPromptBy(String id) {
        return aiAgentPoolService.getById(id).getPrompt();
    }

    public String getPrompt(String agentCode) {
        return aiAgentPoolService.getByCode(agentCode).getPrompt();
    }

    public AiAgentPool getByCode(String agentCode) {
        return aiAgentPoolService.getByCode(agentCode);
    }

    /**
     * 同步返回
     *
     * @param context
     * @return
     */
    public String supply(AgentContext context) {
        long start = System.currentTimeMillis();
        if (StringUtils.isEmpty(context.getPrompt())) {
            throw new RuntimeException("prompt must not be null.");
        }
        AiAgentPool aiAgentPool = context.getAiAgentPool();
        if (aiAgentPool == null) {
            aiAgentPool = aiAgentPoolService.getByCode(context.getAgentCode());
        }
        if (aiAgentPool == null) {
            throw new RuntimeException("agent must not be null.");
        }
        DashScopeChatOptions customOptions = DashScopeChatOptions.builder()
                .withTemperature(aiAgentPool.getTemperature())
                .withModel(aiAgentPool.getModel())
                .withEnableThinking(context.isEnableThinking())
                .build();
        log.info("agent supply, agentCode = {}, param: {}", context.getAgentCode(), JSONObject.toJSONString(customOptions));

        try {
            String reply = chatClient.prompt(new Prompt(context.getPrompt(), customOptions))
                    .user(context.getUserMsg()).call().content();
            log.info("agent {} reply cost:{} ms", aiAgentPool.getAgentName(), (System.currentTimeMillis() - start));
            return reply;
        } catch (Exception e) {
            log.error("agent supply error", e);
        }
        return "";
    }

    /**
     * 流式返回
     *
     * @param context
     * @return
     */
    public Flux<String> supplyStream(AgentContext context) {
        AiAgentPool aiAgentPool = aiAgentPoolService.getByCode(context.getAgentCode());
        DashScopeChatOptions customOptions = DashScopeChatOptions.builder()
                .withTemperature(aiAgentPool.getTemperature())
                .withModel(aiAgentPool.getModel())
                .withEnableThinking(context.isEnableThinking())
                .build();
        log.info("agent supply, agentId = {}, param: {}", context.getAgentCode(), JSONObject.toJSONString(customOptions));
        return chatClient.prompt(new Prompt(context.getPrompt(), customOptions)).messages(context.getHistory())
                .user(context.getUserMsg()).stream().content();
    }

    /**
     * 异步返回
     *
     * @param context
     * @return
     */
    public CompletableFuture<String> supplyAsync(AgentContext context) {
        return ThreadPoolUtil.supplyAsync(() -> this.supply(context));
    }

}
