package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiJobTrain;
import com.wisematch.modules.chat.model.AiJobTrainDTO;
import com.wisematch.modules.chat.model.CardInfo;

import java.util.List;


/**
 * AI智能体服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */
public interface AiJobTrainService extends IService<AiJobTrain>, PreferenceShow  {

    Page<CardInfo> pageQuery(AiJobTrainDTO aiJobTrain);

    List<CardInfo> getList();
    void batchLogicDelete(List<String> ids);

    void logicDelete(String ids);

}
