package com.wisematch.modules.chat.executor;

import com.alibaba.fastjson.JSONObject;
import com.wisematch.modules.chat.handler.viewer.ViewHandlerContext;
import com.wisematch.modules.chat.entity.AiChatLog;
import com.wisematch.modules.chat.service.AiChatLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 启动视频云录像面试记录异步执行器
 *
 * <AUTHOR>
 * @version AgentExecutor.java, v0.1 2025-07-08 19:12
 */
@Service
@Slf4j
public class ChatViewLogExecutor extends AbstractExecutor {

    @Autowired
    private AiChatLogService aiChatLogService;

    @Override
    public boolean execute(ViewHandlerContext context) {
        AiChatLog aiChatLog = new AiChatLog();
        aiChatLog.setConversationId(context.getRoomId());
        aiChatLog.setUserMsg(context.getUserMsg());
        aiChatLog.setSysMsg(context.getLastedAgentMsg());
        if (context.getIntention() != null) {
            aiChatLog.setIntention(JSONObject.toJSONString(context.getIntention()));
            aiChatLog.setIntentionCode(context.getIntention().getCode());
            aiChatLog.setIntentionName(context.getIntention().getIntention());
        }
        aiChatLog.setPrompt(context.getPrompt());
        if (context.getAiAgentPool() != null) {
            aiChatLog.setAgentId(context.getAiAgentPool().getId());
            aiChatLog.setAgentName(context.getAiAgentPool().getAgentName());
        }
        aiChatLog.setTimestamp(new Date());
        aiChatLog.setFollowCount(context.getAiViewRecord().getFollowTime());
        aiChatLog.setQuestion(JSONObject.toJSONString(context.getQuestion()));
        aiChatLog.setCost(System.currentTimeMillis() - context.getStartTime());
        aiChatLogService.save(aiChatLog);
        return true;
    }
}
