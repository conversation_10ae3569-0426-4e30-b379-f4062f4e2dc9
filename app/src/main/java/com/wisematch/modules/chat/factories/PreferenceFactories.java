package com.wisematch.modules.chat.factories;

import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.common.utils.SpringContextUtils;
import com.wisematch.modules.chat.enums.UserPreferenceType;
import com.wisematch.modules.chat.service.*;

public class PreferenceFactories {


    public static PreferenceShow showFactory(String scene){

        if(scene.equals(UserPreferenceType.POSITION.name())){
            return SpringContextUtils.getBean(AiJobPositionService.class);
        }else if (scene.equals(UserPreferenceType.INTERVIEW.name())){
            //scene.startsWith("MATCHER")
            return SpringContextUtils.getBean(AiJobTrainService.class);
        }else {
            throw new RRException(RRExceptionEnum.ILLEGAL_ARGUMENT);
        }
    }

}
