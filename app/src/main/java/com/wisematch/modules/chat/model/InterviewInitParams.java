package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version ChatInitParams.java, v0.1 2025-06-20 16:39
 */
@Data
public class InterviewInitParams {

    /**
     * 用户ID
     */
    public String userId;

    /**
     * 申请人姓名
     */
    @Schema(description = "申请人姓名")
    public String applyName;

    /**
     * 申请人手机
     */
    @Schema(description = "申请人手机")
    public String applyPhone;

    /**
     * 申请人邮箱
     */
    @Schema(description = "申请人邮箱")
    public String applyEmail;

    /**
     * agentId
     */
    @Schema(description = "agentId")
    public String agentId;

    /**
     * 岗位ID
     */
    @Schema(description = "岗位ID")
    public String positionId;

    /**
     * 模拟面试ID
     */
    @Schema(description = "模拟面试ID")
    public String trainId;

    /**
     * 面试官Id
     */
    @Schema(description = "面试官Id")
    public String viewId = "1";

    /**
     * 申请类型，岗位面试-POSITION，模拟面试-TRAIN
     */
    @Schema(description = "申请类型，岗位面试-POSITION，模拟面试-TRAIN")
    public String applyType;

    /**
     * 申请岗位
     */
    @Schema(description = "申请岗位")
    public String applyPosition;

    /**
     * 申请信息，JSON字符串
     */
    @Schema(description = "申请信息，JSON字符串")
    public String applyInfos;

    @Schema(description = "平台类型:APP/PC/H5/MINI")
    public String platform;

    @Schema(description = "开场白")
    public String welcome;

}
