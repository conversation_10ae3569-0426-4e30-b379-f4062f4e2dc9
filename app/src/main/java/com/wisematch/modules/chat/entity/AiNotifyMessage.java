package com.wisematch.modules.chat.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.enums.AiNotifyMessageType;
import com.wisematch.modules.chat.enums.NotifyMsgBizStatus;
import com.wisematch.modules.chat.enums.WiserConstant;
import com.wisematch.modules.chat.model.NotifyCommunicatActionDTO;
import com.wisematch.modules.chat.model.NotifyReplyMsgDTO;
import com.wisematch.modules.common.utils.JsonUtils;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.model.PrivacyProtectionChangeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("ai_notify_message")
@Schema(description = "通知消息")
public class AiNotifyMessage implements Serializable {

    public static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "职位")
    private String position;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "消息类型(加入候选人CANDIDATE，" +
            "加入人才池TALENT_POOL，" +
            "报告生成REPORT，" +
            "系统消息SYSTEM)")
    @TableField("biz_sence")
    private String bizSence;

    @Schema(description = "接收者id")
    private String receiverId;

    @Schema(description = "发送者id")
    private String senderId;

    @Schema(description = "状态：0 未读，1 已读，2 撤回")
    private Integer msgStatus;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "来源业务id")
    private String sourceId;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "失效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedTime;

    @Schema(description = "是否删除")
    private Integer isDel;

    @Schema(description = "logo")
    private String logo;

    @Schema(description = "业务状态")
    private Integer bizStatus;

    @Schema(description = "消息类型")
    private String msgType;

    @Schema(description = "回执内容")
    private String replyMsg;

    @Schema(description = "发送者类型")
    private String senderType;

    @Schema(description = "发送者头像")
    private String senderPhoto;

    @Schema(description = "接收者头像")
    private String receiverPhoto;

    @Schema(description = "发送者姓名")
    private String senderName;

    @Schema(description = "接收者姓名")
    private String receiverName;

    public static AiNotifyMessage replyMsg(AiNotifyMessage aiNotifyMessage
            , NotifyCommunicatActionDTO dto, SysUser sender) {

        aiNotifyMessage.setBizStatus(dto.getBizStatus());

        if (NotifyMsgBizStatus.AGREE.equals(dto.getBizStatus())) {
            if (AiNotifyMessageType.APPLY_VIDEO.name().equals(aiNotifyMessage.getBizSence())) {
                aiNotifyMessage.setReplyMsg(null);
            } else if (AiNotifyMessageType.VX_EXCHANGE.name().equals(aiNotifyMessage.getBizSence())) {
                NotifyReplyMsgDTO notifyReplyMsgDTO = new NotifyReplyMsgDTO(
                        sender.getVxNo(), UserInfoUtils.getLoginUser().getVxNo());
                aiNotifyMessage.setReplyMsg(JsonUtils.toJson(notifyReplyMsgDTO));
            } else if (AiNotifyMessageType.PHONE_EXCHANGE.name().equals(aiNotifyMessage.getBizSence())) {
                NotifyReplyMsgDTO notifyReplyMsgDTO = new NotifyReplyMsgDTO(
                        sender.getMobile(), UserInfoUtils.getLoginUser().getPhone());
                aiNotifyMessage.setReplyMsg(JsonUtils.toJson(notifyReplyMsgDTO));
            }
        } else {
            aiNotifyMessage.setReplyMsg(null);
        }
        return aiNotifyMessage;
    }


    public static void buildMsgForInsert(AiNotifyMessage aiNotifyMessage,
                                         PrivacyProtectionChangeDTO privacy, SysUser receiver){

        if (AiNotifyMessageType.APPLY_VIDEO.name().equals(aiNotifyMessage.getBizSence())) {
            String content = String.format("%s请求查看%s的面试视频", aiNotifyMessage.getSenderName(), aiNotifyMessage.getReceiverName() );
            aiNotifyMessage.setContent(content);
            if (!WiserConstant.PRIVATE_PROTECT.equals(privacy.getVideoProtection())) {
                aiNotifyMessage.setReplyMsg(null);
                aiNotifyMessage.setBizStatus(NotifyMsgBizStatus.AGREE);
            }else {
                aiNotifyMessage.setBizStatus(NotifyMsgBizStatus.SUBMIT);
            }
//            aiNotifyMessage.setMsgType(SHOULD_NOT_RECEIPT.name());
        } else if (AiNotifyMessageType.VX_EXCHANGE.name().equals(aiNotifyMessage.getBizSence())) {
            if (null == receiver.getVxNo()) {
                throw new RRException(RRExceptionEnum.VX_NUMBER_NOT_FOND);
            }
            String content = String.format("%s请求和%s交换微信", aiNotifyMessage.getSenderName(), aiNotifyMessage.getReceiverName() );
            aiNotifyMessage.setContent(content);
            if (!WiserConstant.PRIVATE_PROTECT.equals(privacy.getVxProtection())) {
                NotifyReplyMsgDTO notifyReplyMsgDTO = new NotifyReplyMsgDTO(
                        UserInfoUtils.getLoginUser().getVxNo(), receiver.getVxNo());
                aiNotifyMessage.setReplyMsg(JsonUtils.toJson(notifyReplyMsgDTO));
                aiNotifyMessage.setBizStatus(NotifyMsgBizStatus.AGREE);
            }else {
                aiNotifyMessage.setBizStatus(NotifyMsgBizStatus.SUBMIT);
            }
        } else if (AiNotifyMessageType.PHONE_EXCHANGE.name().equals(aiNotifyMessage.getBizSence())) {
            if (null == receiver.getMobile()) {
                throw new RRException(RRExceptionEnum.PHONE_NUMBER_NOT_FOND);
            }
            String content = String.format("%s请求和%s交换电话", aiNotifyMessage.getSenderName(), aiNotifyMessage.getReceiverName() );
            aiNotifyMessage.setContent(content);
            if (!WiserConstant.PRIVATE_PROTECT.equals(privacy.getPhoneProtection())) {
                NotifyReplyMsgDTO notifyReplyMsgDTO = new NotifyReplyMsgDTO(
                        UserInfoUtils.getLoginUser().getPhone(), receiver.getMobile());
                aiNotifyMessage.setReplyMsg(JsonUtils.toJson(notifyReplyMsgDTO));
                aiNotifyMessage.setBizStatus(NotifyMsgBizStatus.AGREE);
            }else {
                aiNotifyMessage.setBizStatus(NotifyMsgBizStatus.SUBMIT);
            }
        }
    }
}

