package com.wisematch.modules.chat.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 推荐场景-项目经历DTO
 * 对应Milvus集合：geek_recommendation_project_experiences
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RecommendProjectExperiencesDTO {

    /**
     * 关联的用户ID（与推荐简历主档案的userId关联，长度不超过50字符）
     */
    private String userId;

    /**
     * 房间ID
     */
    private String roomId;

    /**
     * 用户删除状态
     * 对应Milvus Int32类型
     */
    private Integer delStatus;

    /**
     * 用户状态
     * 对应Milvus Int32类型
     */
    private Integer status;

    /**
     * 画像状态
     * 对应Milvus Int32类型
     */
    private Integer portraitStatus;

    /**
     * 画像审核状态
     */
    private Integer portraitVerifyStatus;


    /**
     * 岗位胜任力模型关联ID（与userId共同标识人才实体，长度不超过50字符）
     */
    private String examineId;

    /**
     * 开始日期（格式：YYYY.MM，存储为Long类型，如202009表示2020年9月）
     */
    private Long startDate;

    /**
     * 结束日期（格式：YYYY.MM 或“至今”，存储为Long类型，“至今”需特殊编码）
     */
    private Long endDate;

    /**
     * 原始文本内容（项目经历相关文本，如项目名称/职责，长度不超过300字符）
     */
    private String textContent;

    /**
     * 文本类型（标识textContent的内容类型，如“项目名称”“项目角色标签”“项目职责描述”，长度不超过50字符）
     */
    private String textType;

    /**
     * 项目经历向量（768维浮点向量，用于Milvus推荐场景向量检索）
     */
    private List<Double> projectVector;
}
