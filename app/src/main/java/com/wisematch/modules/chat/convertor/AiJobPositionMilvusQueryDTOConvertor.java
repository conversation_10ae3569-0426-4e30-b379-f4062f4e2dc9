package com.wisematch.modules.chat.convertor;

import com.wisematch.modules.chat.model.AiJobPositionMilvusQueryDTO;
import com.wisematch.modules.chat.entity.AiJobPosition;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class AiJobPositionMilvusQueryDTOConvertor {

    // 字段长度限制常量
    private static final int TITLE_MAX_LENGTH = 50;
    private static final int SUMMARY_MAX_LENGTH = 100;
    private static final int CONTENT_MAX_LENGTH = 3000;

    public static AiJobPositionMilvusQueryDTO convert(AiJobPosition aiJobPosition) {
        if (aiJobPosition == null) {
            return null;
        }

        AiJobPositionMilvusQueryDTO dto = new AiJobPositionMilvusQueryDTO();

        // 基本属性映射，带长度限制
        dto.setPositionId(aiJobPosition.getId());
        dto.setOrgId(aiJobPosition.getOrgId());
        dto.setUserId(aiJobPosition.getUserId());
        dto.setTitle(truncateString(aiJobPosition.getPosition(), TITLE_MAX_LENGTH));
        dto.setSummary(truncateString(aiJobPosition.getSummary(), SUMMARY_MAX_LENGTH));
        dto.setCreateTime(aiJobPosition.getCreateTime().getTime());
        dto.setUpdateTime(aiJobPosition.getUpdateTime().getTime());
        dto.setSalaryMax(aiJobPosition.getMaxPay());
        dto.setSalaryMin(aiJobPosition.getMinPay());

        // 处理content字段，使用岗位信息，带长度限制
        dto.setTextContent(truncateString(aiJobPosition.getContent(), CONTENT_MAX_LENGTH));

        // 处理福利列表（按逗号分割成列表）
        dto.setBenefits(splitToList(aiJobPosition.getCompanyPositionBenefit()));

        // 处理技能列表（按逗号分割成列表）
        dto.setSkills(splitToList(aiJobPosition.getCompanyPositionSkill()));

        // 处理审核状态（映射isVerify字段）
        dto.setAuditStatus(aiJobPosition.getIsVerify());

        // 处理删除状态（映射isDel字段）
        dto.setDelStatus(aiJobPosition.getIsDel());

        // 处理职位状态
        dto.setPositionStatus(aiJobPosition.getPositionStatus());

        return dto;
    }

    /**
     * 将字符串截断到指定的最大长度
     * @param str 原始字符串
     * @param maxLength 最大长度限制
     * @return 截断后的字符串，若原始字符串为空则返回null
     */
    private static String truncateString(String str, int maxLength) {
        if (!StringUtils.hasText(str)) {
            return str;
        }
        if (str.length() <= maxLength) {
            return str;
        }
        return str.substring(0, maxLength);
    }

    /**
     * 将以逗号分隔的字符串转换为列表
     * @param str 以逗号分隔的字符串
     * @return 转换后的列表，若输入为空则返回空列表
     */
    private static List<String> splitToList(String str) {
        if (!StringUtils.hasText(str)) {
            return Collections.emptyList();
        }
        return Arrays.asList(str.split(","));
    }
}
