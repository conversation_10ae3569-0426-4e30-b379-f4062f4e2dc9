package com.wisematch.modules.chat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version AiChatInterview.java, v0.1 2025-06-20 20:12
 */
@Data
@TableName("ai_job_competency")
@Schema(description = "胜任力模型")
public class AiJobCompetency implements Serializable {

    public static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    public String id;

    @Schema(description = "职位")
    public String position;

    @Schema(description = "公司")
    public String enterprise;

    @Schema(description = "标签")
    public String lable;
}
