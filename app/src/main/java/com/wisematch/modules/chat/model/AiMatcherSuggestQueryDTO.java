package com.wisematch.modules.chat.model;

import com.wisematch.common.model.PageRequest;
import com.wisematch.modules.chat.entity.AiMatcherSuggest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AiMatcherSuggestQueryDTO extends AiMatcherSuggest implements PageRequest {

    @Schema(description = "起始页")
    private Integer pageNum = 1;

    @Schema(description = "页面大小")
    private Integer pageSize = 10;

}
