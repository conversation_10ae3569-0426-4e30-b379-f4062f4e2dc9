package com.wisematch.modules.chat.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * agent池子
 * <AUTHOR>
 * @version AiAgentVoice.java, v0.1 2025-06-20 20:12
 */
@Data
@TableName("ai_agent_pool")
@Schema(description = "系统的智能体")
public class AiAgentPool implements Serializable {

    public static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    public String id;

    @Schema(description = "智能体名称")
    public String agentName;

    @Schema(description = "智能体唯一码")
    public String agentCode;

    @Schema(description = "智能体头像")
    public String agentPhoto;

    @Schema(description = "大模型")
    public String model;

    @Schema(description = "智能体类型")
    public String prompt;

    @Schema(description = "欢迎语")
    public String welcome;

    @Schema(description = "初始参数")
    public String initParams;


    @Schema(description = "模型参数")
    public Double temperature;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date createTime;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date updateTime;
}
