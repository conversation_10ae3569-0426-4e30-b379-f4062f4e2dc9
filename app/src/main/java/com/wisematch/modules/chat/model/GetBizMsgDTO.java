package com.wisematch.modules.chat.model;

import com.alibaba.fastjson.JSONObject;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.enums.CardType;
import com.wisematch.modules.chat.enums.NotifyMsgBizStatus;
import com.wisematch.modules.common.utils.JsonUtils;
import com.wisematch.modules.sys.entity.SysUser;
import lombok.Data;

@Data
public class GetBizMsgDTO {
    //根据bizStatus查询

    String receiveId;

    String senderId;

    Integer bizStatus;

    String bizSence;

    public GetBizMsgDTO() {
    }

    public GetBizMsgDTO(String receiveId, String senderId, Integer bizStatus, String bizSence) {
        this.receiveId = receiveId;
        this.senderId = senderId;
        this.bizStatus = bizStatus;
        this.bizSence = bizSence;
    }

    public static GetBizMsgDTO getAgreeApplyVideo(String receiveId, String senderId){
        GetBizMsgDTO getBizMsgDTO = new GetBizMsgDTO();
        getBizMsgDTO.setReceiveId(receiveId);
        getBizMsgDTO.setSenderId(senderId);
        getBizMsgDTO.setBizStatus(NotifyMsgBizStatus.AGREE);
        getBizMsgDTO.setBizSence(CardType.APPLY_VIDEO.name());
        return getBizMsgDTO;
    }

    public static GetBizMsgDTO getApplyVideo(String receiveId, String senderId){
        GetBizMsgDTO getBizMsgDTO = new GetBizMsgDTO();
        getBizMsgDTO.setReceiveId(receiveId);
        getBizMsgDTO.setSenderId(senderId);
        getBizMsgDTO.setBizSence(CardType.APPLY_VIDEO.name());
        return getBizMsgDTO;
    }


    public static void putReportPrivate(JSONObject report, SysUser sysUser){

    }



    public static GetBizMsgDTO getApplyPrivacy(String receiveId, String senderId, String bizSence ){
        GetBizMsgDTO getBizMsgDTO = new GetBizMsgDTO();
        getBizMsgDTO.setReceiveId(receiveId);
        getBizMsgDTO.setSenderId(senderId);
        getBizMsgDTO.setBizSence(bizSence);
        return getBizMsgDTO;
    }


    public static void reportPutPhonePrivacy(JSONObject report, AiNotifyMessageVO bizMsg){
        //所以人才报告应该是如果当前用户和senderId相同就拿receiverPhone，

        if(null == bizMsg){
            report.put("phoneMsg", "一键沟通");
            report.put("phoneButton", "一键沟通");
            report.put("phoneBizStatus",NotifyMsgBizStatus.NOT_SUBMIT);
            return;
        }
        if(NotifyMsgBizStatus.AGREE.equals(bizMsg.getBizStatus())){
            String userId = UserInfoUtils.getCurrentUserId();
            if(userId.equals(bizMsg.getSenderId())){
                NotifyReplyMsgDTO notifyReplyMsgDTO = JsonUtils.fromJson(bizMsg.getReplyMsg(), NotifyReplyMsgDTO.class);
                report.put("phoneReply", notifyReplyMsgDTO.getReceiverPrivate());
            }
            report.put("phoneButton", "联系方式");
        }else if(NotifyMsgBizStatus.REFUSE.equals(bizMsg.getBizStatus())){
            report.put("phoneMsg","对方拒绝了你的请求");
            report.put("phoneButton", "已拒绝");
        }else{
            report.put("phoneMsg","请求中");
            report.put("phoneButton", "等待回复");
        }
        report.put("phoneBizStatus",bizMsg.getBizStatus());
    }

    public static void reportPutVxPrivacy(JSONObject report, AiNotifyMessageVO bizMsg){
        if(null == bizMsg){
            report.put("vxMsg","一键沟通");
            report.put("vxButton", null);
            report.put("vxBizStatus",NotifyMsgBizStatus.NOT_SUBMIT);
            return;
        }
        report.put("vxBizStatus",bizMsg.getBizStatus());
        if(NotifyMsgBizStatus.AGREE.equals(bizMsg.getBizStatus())){
            String userId = UserInfoUtils.getCurrentUserId();
            if(userId.equals(bizMsg.getSenderId())){
                NotifyReplyMsgDTO notifyReplyMsgDTO = JsonUtils.fromJson(bizMsg.getReplyMsg(), NotifyReplyMsgDTO.class);
                report.put("vxReply", notifyReplyMsgDTO.getReceiverPrivate());
            }
            report.put("vxButton", null);
        }else if(NotifyMsgBizStatus.REFUSE.equals(bizMsg.getBizStatus())){
            report.put("vxMsg","对方拒绝了你的请求");
            report.put("vxButton", "已拒绝");
        }else{
            report.put("vxMsg","请求中");
            report.put("vxButton", "等待回复");
        }
        report.put("vxBizStatus", bizMsg.getBizStatus());
    }

}
