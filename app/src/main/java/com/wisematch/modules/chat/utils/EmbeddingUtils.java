package com.wisematch.modules.chat.utils;

import com.alibaba.dashscope.embeddings.TextEmbedding;
import com.alibaba.dashscope.embeddings.TextEmbeddingParam;
import com.alibaba.dashscope.embeddings.TextEmbeddingResult;
import com.alibaba.dashscope.embeddings.TextEmbeddingResultItem;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.utils.ThreadPoolUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import com.google.common.collect.Lists;
import java.util.concurrent.CompletableFuture;

/**
 * 向量处理工具类（优化：按text_index关联向量与文本）
 */
@Component
public class EmbeddingUtils {

    @Resource
    private TextEmbedding textEmbedding;

    @Value("${aliyun.embedding.key}")
    private String embeddingKey;

    @Resource(name = "textExecutor")
    private ThreadPoolExecutor textExecutor;

    /**
     * 批量文本向量化（Stream风格并发处理，保持输入顺序）
     * @param texts 待向量化文本列表
     * @return 向量列表（与输入文本顺序严格一致）
     */
    public List<List<Double>> embedTexts(List<String> texts, Integer dimension) {
        if (texts.isEmpty()) {
            return List.of();
        }

        // 每批最大处理数量
        final int batchSize = 10;
        // 并发线程池（根据需求调整线程数）
        List<List<Double>> vectors = Lists.partition(texts, batchSize).stream()
                // 转换为批次处理的CompletableFuture流
                .map(batch -> CompletableFuture.supplyAsync(() -> processBatch(batch, dimension), textExecutor))
                // 收集所有Future
                .collect(Collectors.collectingAndThen(
                        Collectors.toList(),
                        futures -> CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                                .orTimeout(15, TimeUnit.SECONDS)  // 总超时设置
                                .thenApply(v -> futures.stream()
                                        .map(CompletableFuture::join)
                                        .flatMap(List::stream)
                                        .collect(Collectors.toList())
                                )
                                .join()
                ));

        // 校验向量与文本数量是否一致（双重保障）
        if (texts.size() != vectors.size()) {
            throw new RRException("文本与向量数量不匹配：文本数=" + texts.size() + ", 向量数=" + vectors.size());
        }

        return vectors;
    }

    /**
     * 处理单个批次的文本向量化
     */
    private List<List<Double>> processBatch(List<String> batch, Integer dimension) {
        TextEmbeddingParam param = TextEmbeddingParam.builder()
                .model(TextEmbedding.Models.TEXT_EMBEDDING_V4)
                .texts(batch)
                .apiKey(embeddingKey)
                .parameter("dimension", dimension)
                .build();

        try {
            TextEmbeddingResult result = textEmbedding.call(param);
            return result.getOutput().getEmbeddings().stream()
                    .sorted(Comparator.comparing(TextEmbeddingResultItem::getTextIndex))
                    .map(item -> new ArrayList<>(item.getEmbedding()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("批次向量化失败: " + e.getMessage(), e);
        }
    }

}