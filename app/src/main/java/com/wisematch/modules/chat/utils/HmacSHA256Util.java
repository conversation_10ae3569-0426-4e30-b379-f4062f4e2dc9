package com.wisematch.modules.chat.utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

public class HmacSHA256Util {

    /**
     * 使用 HMAC-SHA256 签名字符串，并返回十六进制结果
     *
     * @param message 明文内容（如待签名字符串）
     * @param secretKey 密钥
     * @return 加密后十六进制字符串
     */
    public static String hmacSha256(String message, String secretKey) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(keySpec);
            byte[] rawHmac = mac.doFinal(message.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(rawHmac);
        } catch (Exception e) {
            throw new RuntimeException("HMAC SHA256 加密失败", e);
        }
    }

    /**
     * 将字节数组转为16进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hex = new StringBuilder();
        for (byte b : bytes) {
            String part = Integer.toHexString(0xFF & b);
            if (part.length() == 1) hex.append('0');
            hex.append(part);
        }
        return hex.toString();
    }

    // 示例
    public static void main(String[] args) {
        String message = "appId=123&timestamp=20250723&nonce=abc123";
        String secretKey = "mySecretKey123456";

        String signature = hmacSha256(message, secretKey);
//        System.out.println("HMAC-SHA256 签名结果: " + signature);
    }
}

