package com.wisematch.modules.chat.executor;

import com.wisematch.common.utils.InterviewThreadPoolUtil;
import com.wisematch.modules.rtc.RoomRecordFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 启动视频云录像面试记录异步执行器
 *
 * <AUTHOR>
 * @version AgentExecutor.java, v0.1 2025-07-08 19:12
 */
@Service
@Slf4j
public class StartVideoRecordExecutor {

    @Autowired
    private RoomRecordFacade roomRecordFacade;

    public void executeAsync(String userId, String roomId, String platform) {
        InterviewThreadPoolUtil.supplyAsync(() ->  roomRecordFacade.startRecord(userId, roomId, platform));
    }
}
