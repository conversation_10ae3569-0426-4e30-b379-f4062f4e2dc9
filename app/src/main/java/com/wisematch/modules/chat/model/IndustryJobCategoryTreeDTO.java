package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 行业职位分类树形结构VO
 */
@Data
@Schema(description = "行业职位分类树形结构VO")
public class IndustryJobCategoryTreeDTO {

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "子分类列表")
    private List<IndustryJobCategoryTreeDTO> children = new ArrayList<>();
}

