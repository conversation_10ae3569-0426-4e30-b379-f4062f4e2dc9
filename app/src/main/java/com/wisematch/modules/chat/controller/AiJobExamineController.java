package com.wisematch.modules.chat.controller;


import com.wisematch.common.annotation.SysLog;
import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.modules.chat.entity.AiJobExamine;
import com.wisematch.modules.chat.model.AiJobExamineDTO;
import com.wisematch.modules.chat.model.AiJobExamineQueryDTO;
import com.wisematch.modules.chat.service.AiJobExamineService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version AiJobAssessmentController.java, v0.1 2025-07-01
 */
@RestController
@RequestMapping("/jobAssessments")
@Tag(name = "岗位评估", description = "岗位胜任力评估管理")
public class AiJobExamineController {

    private final AiJobExamineService aiJobAssessmentService;

    public AiJobExamineController(AiJobExamineService aiJobAssessmentService) {
        this.aiJobAssessmentService = aiJobAssessmentService;
    }

    @PostMapping
    @Operation(summary = "新增评估模型")
    @SysLog("新增评估模型")
    @NotDoubleSubmit
    public R create(@RequestBody AiJobExamine dto) {
        aiJobAssessmentService.save(dto);
        return R.ok().setData(dto);
    }

//
//    @PutMapping("/deleteOne")
//    @Operation(summary = "删除评估模型")
//    public R delete(@RequestBody IdRequest idRequest) {
//        aiJobAssessmentService.deleteOne(idRequest.getId());
//       return R.ok();
//    }

    @PutMapping("/{id}")
    @Operation(summary = "修改评估模型")
    @SysLog("修改评估模型")
    @NotDoubleSubmit
    public R update(@PathVariable String id, @RequestBody AiJobExamine dto) {
        dto.setId(id);
        aiJobAssessmentService.updateById(dto);
        return R.ok().setData(dto);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取单条（根据id）")
    public R getById(@PathVariable String id) {
        return R.ok().setData(aiJobAssessmentService.getById(id));
    }

    @PostMapping("/pageQuery")
    @Operation(summary = "分页查询评估列表")
    @NotDoubleSubmit
    public R pageQuery(@RequestBody AiJobExamineDTO aiJobAssessmentDTO) {
        return R.ok().setData(aiJobAssessmentService.pageQuery(aiJobAssessmentDTO));
    }


    @PostMapping("/selectAll")
    @Operation(summary = "分页查询评估列表")
    @NotDoubleSubmit
    public R selectAll(@RequestBody AiJobExamineDTO aiJobAssessmentDTO) {
        return R.ok().setData(aiJobAssessmentService.selectAll(aiJobAssessmentDTO));
    }

//
//    /**
//     * 删除
//     */
//    @PostMapping(value = "/delete")
//    public R delete(@RequestBody String[] ids) {
//        aiJobAssessmentService.batchLogicDelete(Arrays.asList(ids));
//        return R.ok();
//    }


    @PostMapping("/list")
    @Operation(summary = "查询所有模型")
    @NotDoubleSubmit
    public R list(@RequestBody AiJobExamineQueryDTO dto) {
        return R.ok().setData(aiJobAssessmentService.listAll(dto));
    }
}