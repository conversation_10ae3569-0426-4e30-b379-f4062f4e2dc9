package com.wisematch.modules.chat.enums;

import lombok.Getter;

@Getter
public enum InterviewIntentionCode {

    // 枚举实例定义
    INT_99("99", "正常意图"),
    INT_100("100", "正常回答"),
    INT_101("101", "面试追问"),
    INT_106("106", "不想回答"),
    INT_102("102", "话题偏离"),
    INT_104("104", "反向 PUA AI（面试角色错位）");
    private final String code;
    private final String desc;

    InterviewIntentionCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}
