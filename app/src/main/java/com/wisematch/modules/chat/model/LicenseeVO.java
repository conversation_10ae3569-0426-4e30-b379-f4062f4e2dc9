package com.wisematch.modules.chat.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class LicenseeVO implements Serializable {

    private String creditCode;
    private String companyName;
    private String legalPerson;
    private String title;
    private String companyType;
    private String businessAddress;
    private String businessScope;
    private String registeredCapital;
    @JsonProperty("RegistrationDate")
    private String RegistrationDate;
    private String validPeriod;
    private String validFromDate;
    private String validToDate;
    private String issueDate;
    private String companyForm;

}
