package com.wisematch.modules.chat.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wisematch.common.annotation.ResourceAuth;
import com.wisematch.common.annotation.SysLog;
import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.AiOrganizationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.ExecutionException;

@RestController
@RequestMapping("/Organization")
@Tag(name = "企业信息", description = "企业信息")
@Slf4j
public class AiOrganizationController {

    @Autowired
    private AiOrganizationService aiOrganizationService;

    @PostMapping("/associate")
    @Operation(summary = "关联企业组织信息")
    @SysLog("关联企业组织信息")
    @NotDoubleSubmit
    @Deprecated
    public R relation(@RequestBody AiOrganizationInsertDTO org) {
        aiOrganizationService.associate(org);
        return R.ok();
    }

    @PostMapping("/getById")
    @Operation(summary = "根据ID查询")
    public R getById(@RequestBody IdRequest request) {
        return R.ok().setData(aiOrganizationService.getById(request.getId()));
    }

    @GetMapping("/getByUserId")
    @Operation(summary = "根据UserId查询")
    public R getById() {
        return R.ok().setData(aiOrganizationService.getByUserId(UserInfoUtils.getCurrentUserId()));
    }

    @PostMapping("/changeOpenStatus")
    @Operation(summary = "修改是否公开企业信息")
    @SysLog("修改是否公开企业信息")
    @NotDoubleSubmit
    public R changeOpenStatus(@RequestBody ChangeOpenStatusDTO dto) {
        aiOrganizationService.changeOpenStatus(dto);
        return R.ok().setData(dto);
    }

    /**
     * 实名认证
     */
    @SysLog("识别企业信息")
    @PostMapping(value = "/licenseVerify")
    @NotDoubleSubmit
    @Deprecated
    @ResourceAuth(value = "识别企业信息", module = "识别企业信息")
    public R license3MetaVerify(@RequestBody FileSrcDTO fileSrcDTO) throws ExecutionException, InterruptedException, JsonProcessingException {
        return R.ok( ).setData(aiOrganizationService.licenseMetaVerify(fileSrcDTO));
    }

    /**
     * 实名认证
     */
    @SysLog("识别企业信息")
    @PostMapping(value = "/licenseUpload")
    @Operation(summary = "识别企业信息")
    @NotDoubleSubmit
    @ResourceAuth(value = "识别企业信息", module = "识别企业信息")
    public R licenseUpload(@RequestBody FileSrcDTO fileSrcDTO) throws ExecutionException, InterruptedException, JsonProcessingException {
        return R.ok( ).setData(aiOrganizationService.licenseMetaVerify(fileSrcDTO));
    }

    @PostMapping("/certifiedEnterprise")
    @Operation(summary = "企业认证")
    @SysLog("企业认证")
    @NotDoubleSubmit
    public R certifiedEnterprise(@RequestBody CertifiedEnterpriseDTO org) throws ExecutionException, InterruptedException, JsonProcessingException {
        return R.ok().setData(aiOrganizationService.certifiedEnterprise(org));
    }

    @PostMapping("/updateOrg")
    @Operation(summary = "关联企业组织信息")
    @SysLog("关联企业组织信息")
    @NotDoubleSubmit
    public R updateOrg(@RequestBody UpdateOrgDTO org) {
        aiOrganizationService.updateOrg(org);
        return R.ok();
    }

}

