package com.wisematch.modules.chat.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.wisematch.common.model.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AiJobTrainDTO implements PageRequest {

    @Schema(description = "主键ID")
    @TableId
    private String id;

    @Schema(description = "面试类型，1专项实训，0模拟面试")
    private String type;

    private String position;

    @Schema(description = "考核维度ID")
    private String jobExamineId;

    @Schema(description = "面试官ID")
    private String jobViewerId;

    @Schema(description = "展示图片")
    private String logo;

    @Schema(description = "面试摘要")
    private String summary;

    @Schema(description = "关键标签")
    private String keyLabel;

    @Schema(description = "面试标签")
    private String labels;

    @Schema(description = "面试信息")
    private String content;

    @Schema(description = "发布公司")
    private String company;

    @Schema(description = "智能体ID")
    private String agentId;

    @Schema(description = "面试时长（秒）")
    private Integer duration;

    @Schema(description = "是否删除")
    private Integer isDel;

    @Schema(description = "面试难度")
    private String level;

    @Schema(description = "面试状态（0停用，1已发布）")
    private Integer status;

    @Schema(description = "起始页")
    private Integer pageNum = 1;

    @Schema(description = "页面大小")
    private Integer pageSize = 10;

}
