package com.wisematch.modules.chat.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version AiChatWiser.java, v0.1 2025-06-20 20:12
 */
@Data
@TableName("ai_chat_wiser")
@Schema(description = "wiser记录")
public class AiChatWiser implements Serializable {

    public static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    public String id;

    @Schema(description = "会话ID")
    public String chatId;

    @Schema(description = "用户ID")
    public String userId;

    @Schema(description = "用户名称")
    public String userName;

    @Schema(description = "用户头像")
    public String userPhoto;

    @Schema(description = "wiserID")
    public String wiserId;

    @Schema(description = "wiser名称")
    public String wiserName;

    @Schema(description = "0：存在，1：删除")
    public String isDel;

    @Schema(description = "wiser头像")
    public String wiserPhoto;

    @Schema(description = "最后一次消息")
    public String lastMsg;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date createTime;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date updateTime;
}
