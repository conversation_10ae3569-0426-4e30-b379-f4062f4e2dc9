package com.wisematch.modules.chat.handler.viewer;

import com.wisematch.modules.chat.agent.RoomAgentFacade;
import com.wisematch.modules.chat.enums.IntentionConstant;
import com.wisematch.modules.chat.enums.InterviewStatus;
import com.wisematch.modules.chat.executor.ChatViewLogExecutor;
import com.wisematch.modules.chat.model.ChatReplyMsg;
import com.wisematch.modules.chat.service.AiChatMemoryService;
import com.wisematch.modules.chat.service.AiViewRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.Date;

/**
 * 业务判定直接回复
 * <AUTHOR>
 * @version IAgentHandler.java, v0.1 2025-07-15 16:42
 */
@Component
@Slf4j
public class ViewBizReplyHandler implements IViewAgentHandler {

    IViewAgentHandler iViewAgentHandler;

    @Autowired
    private AiChatMemoryService aiChatMemoryService;

    @Autowired
    ChatViewLogExecutor chatViewLogExecutor;

    @Autowired
    AiViewRecordService aiViewRecordService;

    @Autowired
    RoomAgentFacade roomAgentFacade;

    @Override
    public void setNext(IViewAgentHandler iAgentHandler) {
        this.iViewAgentHandler = iAgentHandler;
    }

    @Override
    public Flux<ChatReplyMsg> handle(ViewHandlerContext context) {
        log.info("interview biz reply start, chatId: {}, askIndex: {}, followCount: {}", context.getRoomId(), context.getAiViewRecord().getAskIndex(), context.getAiViewRecord().getFollowTime());
        //正常回答或者追问超限3次或者不想回答
        if (context.getAiAgentPool() == null || (context.getIntention() != null
                && IntentionConstant.CODE_100.equals(context.getIntention().getCode()))
                || context.getAiViewRecord().getFollowTime() > 2) {
            int askIndex = context.getAiViewRecord().askIndex;
            //提问环节，提问下一个问题
            if(context.getQuestionList() != null) {
                if (askIndex + 1 < context.getQuestionList().size()) {
                    askIndex = askIndex + 1;
                    context.getAiViewRecord().setAskIndex(askIndex);
                    context.getAiViewRecord().setFollowTime(0);
                    context.setQuestion(context.getQuestionList().get(askIndex));
                    log.info("面试提问: {}, chatId: {}, askIndex: {}", context.getQuestionList().get(askIndex), context.getRoomId(), context.getAiViewRecord().getAskIndex());
                    String reply = context.getQuestionList().get(askIndex).getQuestion();
                    //增加记忆
                    aiChatMemoryService.addAssistantAsync(context.getRoomId(), reply, context.getAiViewRecord().getAskIndex());
                    context.setLastedAgentMsg(reply);
                    //意图识别完记一次log
                    chatViewLogExecutor.executeAsync(context);
                    return Flux.just(ChatReplyMsg.reply(context.getReplyId(), reply));
                } else {
                    context.getAiViewRecord().setStatus(InterviewStatus.CHATED.name());
                    context.getAiViewRecord().setEndTime(new Date());
                    roomAgentFacade.stopRoomAgent(context.getRoomId());
                    return Flux.just(ChatReplyMsg.reply(context.getReplyId(), "非常感谢你参加本次面试，面试到此结束，请点击关闭按钮退出"));
                }
            }
        }
        return iViewAgentHandler.handle(context);
    }

}
