package com.wisematch.modules.chat.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 求职意向实体类
 * 用于存储求职者的目标岗位信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class JobIntent {

    /**
     * 工作地点数组，城市+区县+详细地址
     */
    private List<String> workLocation;

    /**
     * 招聘类型数组（全职/应届校园招聘/实习生招聘/兼职）
     */
    private List<String> employmentType;

    /**
     * 薪酬要求数组，支持多组薪酬
     */
    private List<Salary> salary;

    /**
     * 经验要求数组，严格匹配枚举（无经验/1年以内/1-3年/3-5年/5-10年/10年以上）
     */
    private List<String> experienceRequirement;

    /**
     * 最低学历数组，严格匹配枚举（不限/初中及以下/中专或中技/高中/大专/本科/硕士/博士）
     */
    private List<String> educationRequirement;

    /**
     * 福利待遇数组，匹配标准化枚举
     */
    private List<String> benefits;

    /**
     * 掌握技能数组，拆分复合技能并映射到标准化枚举
     */
    private List<String> skills;

    /**
     * RAG导向的岗位职责描述数组，按语义完整chunk拆分
     */
    private List<String> jobDescription;

    /**
     * 薪酬内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Salary {
        /**
         * 最低月薪（单位元）
         */
        private String min;

        /**
         * 最高月薪（单位元）
         */
        private String max;

        /**
         * 每年多少薪
         */
        private String months;
    }
}
