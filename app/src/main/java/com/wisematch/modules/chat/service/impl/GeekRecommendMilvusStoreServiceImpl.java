package com.wisematch.modules.chat.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.wisematch.modules.chat.agent.ResumeParseAgent;
import com.wisematch.modules.chat.config.MilvusTemplate;
import com.wisematch.modules.chat.convertor.*;
import com.wisematch.modules.chat.entity.*;
import com.wisematch.modules.chat.enums.AiUserFormType;
import com.wisematch.modules.chat.enums.ApplyType;
import com.wisematch.modules.chat.enums.GeekTextTypeEnum;
import com.wisematch.modules.chat.enums.WiserConstant;
import com.wisematch.modules.chat.mapper.*;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.*;
import com.wisematch.modules.chat.utils.EmbeddingUtils;
import com.wisematch.modules.chat.utils.TextSplitUtils;
import com.wisematch.modules.form.entity.AiUserForm;
import com.wisematch.modules.form.mapper.AiUserFormMapper;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.mapper.SysUserMapper;
import com.wisematch.modules.sys.model.JobApplication;
import io.milvus.v2.service.vector.response.InsertResp;
import io.milvus.v2.service.vector.response.QueryResp;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.wisematch.modules.chat.enums.WiserConstant.NOT_DELETE;

@Service
@Slf4j
public class GeekRecommendMilvusStoreServiceImpl implements GeekRecommendMilvusStoreService {

    // 集合名称常量定义
    private static final String RESUME_PROFILES_COLLECTION = "geek_recommendation_resume_profiles";
    private static final String EDUCATION_EXPERIENCES_COLLECTION = "geek_recommendation_education_experiences";
    private static final String WORK_EXPERIENCES_COLLECTION = "geek_recommendation_work_experiences";
    private static final String PROJECT_EXPERIENCES_COLLECTION = "geek_recommendation_project_experiences";
    private static final String SCHOOL_EXPERIENCES_COLLECTION = "geek_recommendation_school_experiences";
    private static final String REPORT_COLLECTION = "geek_recommendation_report";
    private static final String PORTRAIT_DIMENSION_COLLECTION = "geek_recommendation_portrait_dimension";
    private static final String PORTRAIT_EVALUATION_COLLECTION = "geek_recommendation_portrait_evaluation";

    private static final String GEEK_QUERY_COLLECTION = "geek_query";

    @Resource
    private AiViewRecordMapper aiViewRecordMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private AiJobPositionMapper aiJobPositionMapper;

    @Resource
    private ResumeParseAgent resumeParseAgent;

    @Resource
    private AiViewPrepareMapper aiViewPrepareMapper;

    @Resource
    private AiViewReportMapper aiViewReportMapper;

    @Resource
    private AiViewPortraitMapper aiViewPortraitMapper;

    @Resource
    private AiUserFormMapper aiUserFormMapper;

    @Resource
    private Gson gson;

    @Resource
    private MilvusTemplate milvusTemplate;

    @Resource
    private EmbeddingUtils embeddingUtils;

    @Resource
    private TextSplitUtils textSplitUtils;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    public void cancelAccount(String userId) {
        cancelChatGeekRecommend(userId);
        cancelGeekQuery(userId);
    }

    private void cancelGeekQuery(String userId) {
        Map<String, List<Long>> oldRecordIds = queryOldRecordIdsForQuery(userId);
        oldRecordIds.forEach((collectionName, ids) -> {
            if (!ids.isEmpty()) {
                try {
                    // 使用安全的字段更新方法
                    Map<String, Object> fieldUpdates = new HashMap<>();
                    fieldUpdates.put("del_status", WiserConstant.DELETED);
                    boolean success = milvusTemplate.updateFields(collectionName, ids, fieldUpdates);
                    if (success) {
                        log.info("成功查询更新集合{}中{}条记录的del_status为{}", collectionName, ids.size(), WiserConstant.DELETED);

                    } else {
                        log.error("更新查询集合{}中记录的del_status失败", collectionName);
                    }
                } catch (Exception e) {
                    log.error("更新集合{}中的旧记录时发生异常", collectionName, e);
                }
            }
        });
    }

    private void cancelChatGeekRecommend(String userId) {
        Map<String, List<Long>> oldRecordIds = queryOldRecordIdsForRecommend(userId);
        oldRecordIds.forEach((collectionName, ids) -> {
            if (!ids.isEmpty()) {
                try {
                    // 使用安全的字段更新方法
                    Map<String, Object> fieldUpdates = new HashMap<>();
                    fieldUpdates.put("del_status", WiserConstant.DELETED);

                    boolean success = milvusTemplate.updateFields(collectionName, ids, fieldUpdates);
                    if (success) {
                        log.info("成功更新集合{}中{}条记录的del_status为{}", collectionName, ids.size(), WiserConstant.DELETED);
                    } else {
                        log.error("更新集合{}中记录的del_status失败", collectionName);
                    }

                } catch (Exception e) {
                    log.error("更新集合{}中的旧记录时发生异常", collectionName, e);
                }
            }
        });
    }

    @Override
    public void deleteUser(String userId) {
        deleteChatGeekRecommend(userId);
        deleteGeekQuery(userId);
    }

    private void deleteGeekQuery(String userId) {
        Map<String, List<Long>> oldRecordIds = queryOldRecordIdsForQuery(userId);
        deleteOldRecordsById(oldRecordIds);
    }

    private Map<String, List<Long>> queryOldRecordIdsForQuery(String userId) {
        String queryFilter = "user_id == '" + userId + "'";
        Map<String, List<Long>> oldRecordIds = new HashMap<>(1);
        oldRecordIds.put(GEEK_QUERY_COLLECTION, queryRecordIds(GEEK_QUERY_COLLECTION, queryFilter));
        return oldRecordIds;
    }

    private void deleteChatGeekRecommend(String userId) {
        Map<String, List<Long>> oldRecordIds = queryOldRecordIdsForRecommend(userId);
        deleteOldRecordsById(oldRecordIds);
    }


    private Map<String, List<Long>> queryOldRecordIdsForRecommend(String userId) {
        Map<String, List<Long>> oldRecordIds = new HashMap<>(5);
        String queryFilter = "user_id == '" + userId + "'";

        // 查询简历主档案旧记录ID
        oldRecordIds.put(RESUME_PROFILES_COLLECTION, queryRecordIds(RESUME_PROFILES_COLLECTION, queryFilter));

        // 查询教育经历旧记录ID
        oldRecordIds.put(EDUCATION_EXPERIENCES_COLLECTION, queryRecordIds(EDUCATION_EXPERIENCES_COLLECTION, queryFilter));

        // 查询工作经历旧记录ID
        oldRecordIds.put(WORK_EXPERIENCES_COLLECTION, queryRecordIds(WORK_EXPERIENCES_COLLECTION, queryFilter));

        // 查询项目经历旧记录ID
        oldRecordIds.put(PROJECT_EXPERIENCES_COLLECTION, queryRecordIds(PROJECT_EXPERIENCES_COLLECTION, queryFilter));

        // 查询校园经历旧记录ID
        oldRecordIds.put(SCHOOL_EXPERIENCES_COLLECTION, queryRecordIds(SCHOOL_EXPERIENCES_COLLECTION, queryFilter));

        // 查询报告旧记录ID
        oldRecordIds.put(REPORT_COLLECTION, queryRecordIds(REPORT_COLLECTION, queryFilter));

        // 查询画像维度旧记录ID
        oldRecordIds.put(PORTRAIT_DIMENSION_COLLECTION, queryRecordIds(PORTRAIT_DIMENSION_COLLECTION, queryFilter));

        // 查询画像评估旧记录ID
        oldRecordIds.put(PORTRAIT_EVALUATION_COLLECTION, queryRecordIds(PORTRAIT_EVALUATION_COLLECTION, queryFilter));

        return oldRecordIds;
    }


    private CandidateReport getCandidateReport(AiViewReport aiViewReport) {
        CandidateReport report = gson.fromJson(aiViewReport.getReport(), CandidateReport.class);
        List<CandidateReport.Dimension> fixedDimensions = report.getDimensions().stream()
                .filter(dimension -> dimension.getCheckPoint() != null)
                .toList();
        report.setDimensions(fixedDimensions);
        return report;
    }

    /**
     * 查询指定集合中符合条件的记录ID
     */
    private List<Long> queryRecordIds(String collectionName, String filter) {
        try {
            QueryResp resp = milvusTemplate.query(collectionName, filter, List.of("id"), 16384, 0);
            if (resp != null && resp.getQueryResults() != null && !resp.getQueryResults().isEmpty()) {
                List<Long> ids = resp.getQueryResults().stream()
                        .map(item -> (Long) item.getEntity().get("id"))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                log.info("查询集合{}符合条件的旧记录{}条: {}", collectionName, ids.size(), filter);
                return ids;
            }
        } catch (Exception e) {
            log.error("查询集合{}记录ID时发生异常: {}", collectionName, filter, e);
        }
        return Collections.emptyList();
    }

    /**
     * 查询所有集合中符合条件的旧记录ID
     */
    private Map<String, List<Long>> queryOldRecordIdsForChatRecommend(String userId, String examineId) {
        Map<String, List<Long>> oldRecordIds = new HashMap<>(5);
        String queryFilter = "user_id == '" + userId + "' and examine_id == '" + examineId + "'";

        // 查询简历主档案旧记录ID
        oldRecordIds.put(RESUME_PROFILES_COLLECTION, queryRecordIds(RESUME_PROFILES_COLLECTION, queryFilter));

        // 查询教育经历旧记录ID
        oldRecordIds.put(EDUCATION_EXPERIENCES_COLLECTION, queryRecordIds(EDUCATION_EXPERIENCES_COLLECTION, queryFilter));

        // 查询工作经历旧记录ID
        oldRecordIds.put(WORK_EXPERIENCES_COLLECTION, queryRecordIds(WORK_EXPERIENCES_COLLECTION, queryFilter));

        // 查询项目经历旧记录ID
        oldRecordIds.put(PROJECT_EXPERIENCES_COLLECTION, queryRecordIds(PROJECT_EXPERIENCES_COLLECTION, queryFilter));

        // 查询校园经历旧记录ID
        oldRecordIds.put(SCHOOL_EXPERIENCES_COLLECTION, queryRecordIds(SCHOOL_EXPERIENCES_COLLECTION, queryFilter));

        // 查询报告旧记录ID
        oldRecordIds.put(REPORT_COLLECTION, queryRecordIds(REPORT_COLLECTION, queryFilter));

        // 查询画像维度旧记录ID
        oldRecordIds.put(PORTRAIT_DIMENSION_COLLECTION, queryRecordIds(PORTRAIT_DIMENSION_COLLECTION, queryFilter));

        // 查询画像评估旧记录ID
        oldRecordIds.put(PORTRAIT_EVALUATION_COLLECTION, queryRecordIds(PORTRAIT_EVALUATION_COLLECTION, queryFilter));

        return oldRecordIds;
    }

    private Map<String, List<Long>> queryOldRecordIdsForQuery(String userId, String positionId) {
        String queryFilter = "user_id == '" + userId + "' and position_id == '" + positionId + "'";
        Map<String, List<Long>> oldRecordIds = new HashMap<>(5);

        // 查询简历主档案旧记录ID
        oldRecordIds.put(GEEK_QUERY_COLLECTION, queryRecordIds(GEEK_QUERY_COLLECTION, queryFilter));
        return oldRecordIds;
    }

    @Override
    public void store(String roomId) {
        storeGeekChatRecommend(roomId);
        storeGeekQuery(roomId);
    }

    private void storeGeekQuery(String roomId) {
        AiViewRecord aiViewRecord = aiViewRecordMapper.selectOne(new QueryWrapper<AiViewRecord>().lambda().eq(AiViewRecord::getRoomId, roomId));

        String userId = aiViewRecord.getUserId();

        SysUser sysUser = sysUserMapper.selectById(userId);

        if (sysUser == null) {
            return;
        }

        String positionId = aiViewRecord.getPositionId();

        AiJobPosition position = aiJobPositionMapper.selectById(positionId);
        if (position == null) {
            return;
        }

        Map<String, List<Long>> oldRecordIds = queryOldRecordIdsForQuery(sysUser.getUserId(), positionId);

        AiUserForm aiUserForm = aiUserFormMapper.selectOne(
                new QueryWrapper<AiUserForm>().lambda()
                        .eq(AiUserForm::getUserId, userId)
                        .in(AiUserForm::getFormType, AiUserFormType.PART_TIME_JOB_INTENTION.name(), AiUserFormType.FULL_TIME_JOB_INTENTION.name())
                        .eq(AiUserForm::getIsDel, NOT_DELETE)
                        .eq(AiUserForm::getStatus, 0)
                        .eq(AiUserForm::getPosition, position.getPosition())
                        .orderByDesc(AiUserForm::getUpdateTime)
                        .last("limit 1"));
        AiViewPortrait aiViewPortrait = aiViewPortraitMapper.selectOne(new QueryWrapper<AiViewPortrait>().lambda().eq(AiViewPortrait::getRoomId, roomId));

        if (aiViewPortrait == null) {
            return;
        }

        GeekQuery geekQuery = GeekQueryConvertor.convert(sysUser, aiUserForm, aiViewPortrait, position, objectMapper);
        String jsonStr = gson.toJson(geekQuery);
        JsonObject jsonObject = gson.fromJson(jsonStr, JsonObject.class);
        InsertResp insertResp = milvusTemplate.insert(GEEK_QUERY_COLLECTION, Collections.singletonList(jsonObject));
        if (insertResp != null && !insertResp.getPrimaryKeys().isEmpty()) {
            log.info("成功插入一条用户画像记录到集合{}", GEEK_QUERY_COLLECTION);
        } else {
            log.error("插入用户画像记录到集合{}失败", GEEK_QUERY_COLLECTION);
        }

        deleteOldRecordsById(oldRecordIds);
    }

    private void storeGeekChatRecommend(String roomId) {
        AiViewRecord aiViewRecord = aiViewRecordMapper.selectOne(new QueryWrapper<AiViewRecord>().lambda().eq(AiViewRecord::getRoomId, roomId));
        if (aiViewRecord == null || ApplyType.TRAIN.name().equals(aiViewRecord.getChatType())) {
            return;
        }

        String userId = aiViewRecord.getUserId();

        SysUser sysUser = sysUserMapper.selectById(userId);

        if (sysUser == null) {
            log.error("未找到用户信息，userId={}", userId);
            return;
        }

        String positionId = aiViewRecord.getPositionId();

        AiJobPosition position = aiJobPositionMapper.selectById(positionId);
        if (position == null) {
            log.error("未找到职位信息，positionId={}", positionId);
            return;
        }
        String examineId = position.getJobExamineId();

        // 1. 查询向量库中符合userId和examineId的旧记录ID（先查询ID）
        Map<String, List<Long>> oldRecordIds = queryOldRecordIdsForChatRecommend(userId, examineId);

        // 2. 解析简历DTO
        AiViewPrepare aiViewPrepare = aiViewPrepareMapper.selectOne(new QueryWrapper<AiViewPrepare>().lambda().eq(AiViewPrepare::getRoomId, roomId));
        if (aiViewPrepare == null || !StringUtils.hasLength(aiViewPrepare.getResume())) {
            log.error("未找到简历信息，roomId={}", roomId);
            return;
        }

        String resume = aiViewPrepare.getResume();
        if (!JSONUtil.isTypeJSON(resume)) {
            resume = resumeParseAgent.resumeJsonExtract(resume);
        }

        ResumeDTO resumeDTO = JSONObject.parseObject(resume, ResumeDTO.class);
        if (resumeDTO == null) {
            log.error("解析简历失败，roomId={}", roomId);
            return;
        }

        AiUserForm aiUserForm = aiUserFormMapper.selectOne(new QueryWrapper<AiUserForm>().lambda().eq(AiUserForm::getUserId, userId).eq(AiUserForm::getIsDel, NOT_DELETE).orderByDesc(AiUserForm::getUpdateTime).last("limit 1"));

        AiViewReport aiViewReport = aiViewReportMapper.selectOne(new QueryWrapper<AiViewReport>().lambda().eq(AiViewReport::getRoomId, roomId));
        AiViewPortrait aiViewPortrait = aiViewPortraitMapper.selectOne(new QueryWrapper<AiViewPortrait>().lambda().eq(AiViewPortrait::getRoomId, roomId));

        CandidateReport report = getCandidateReport(aiViewReport);
        CandidatePortrait portrait = gson.fromJson(aiViewPortrait.getPortrait(), CandidatePortrait.class);

        // 3. 处理并存储各类经历（批量插入新记录）
        processAndStoreResumeProfiles(resumeDTO, sysUser, examineId, roomId, aiUserForm, aiViewPortrait);
        processAndStoreEducationExperiences(resumeDTO, sysUser, examineId, roomId, aiViewPortrait);
        processAndStoreWorkExperiences(resumeDTO, sysUser, examineId, roomId, aiViewPortrait);
        processAndStoreProjectExperiences(resumeDTO, sysUser, examineId, roomId, aiViewPortrait);
        processAndStoreSchoolExperiences(resumeDTO, sysUser, examineId, roomId, aiViewPortrait);
        processAndStoreReports(report, sysUser, examineId, roomId, aiViewPortrait);
        processAndStorePortraitDimensions(portrait, sysUser, examineId, roomId, aiViewPortrait);
        processAndStorePortraitEvaluations(portrait, sysUser, examineId, roomId, aiViewPortrait);

        // 4. 最后按照ID删除旧记录
        deleteOldRecordsById(oldRecordIds);

        log.info("简历信息存储到Milvus完成，userId={}, examineId={}", userId, examineId);
    }

    /**
     * 按照ID删除旧记录
     */
    private void deleteOldRecordsById(Map<String, List<Long>> oldRecordIds) {
        oldRecordIds.forEach((collectionName, ids) -> {
            if (!ids.isEmpty()) {
                try {
                    boolean deleteSuccess = milvusTemplate.deleteById(collectionName, ids);
                    if (deleteSuccess) {
                        log.info("成功删除集合{}中的{}条旧记录", collectionName, ids.size());
                    } else {
                        log.error("删除集合{}中的{}条旧记录失败", collectionName, ids.size());
                    }
                } catch (Exception e) {
                    log.error("删除集合{}中的旧记录时发生异常", collectionName, e);
                }
            }
        });
    }

    /**
     * 处理并存储简历主档案
     */
    private void processAndStoreResumeProfiles(ResumeDTO resumeDTO, SysUser sysUser, String examineId, String roomId, AiUserForm aiUserForm, AiViewPortrait aiViewPortrait) {
        List<GeekTextWithType> textWithTypes = new ArrayList<>();

        // 处理Brief中的comments
        if (resumeDTO.getBrief() != null && StringUtils.hasLength(resumeDTO.getBrief().getComments())) {
            textWithTypes.add(new GeekTextWithType(
                    resumeDTO.getBrief().getComments(),
                    GeekTextTypeEnum.RESUME_BRIEF_COMMENTS
            ));
        }

        // 处理Brief中的skills
        if (resumeDTO.getBrief() != null && StringUtils.hasLength(resumeDTO.getBrief().getSkills())) {
            Arrays.stream(resumeDTO.getBrief().getSkills().split("\\|"))
                    .filter(StringUtils::hasLength)  // 过滤空字符串
                    .map(skill -> new GeekTextWithType(skill, GeekTextTypeEnum.RESUME_BRIEF_SKILLS))
                    .forEach(textWithTypes::add);
        }

        // 处理其他需要的字段
        if (StringUtils.hasLength(resumeDTO.getExtraInfo())) {
            textWithTypes.add(new GeekTextWithType(
                    resumeDTO.getExtraInfo(),
                    GeekTextTypeEnum.RESUME_EXTRA_INFO
            ));
        }

        if (StringUtils.hasLength(resumeDTO.getWorkYears())) {
            textWithTypes.add(new GeekTextWithType(
                    resumeDTO.getWorkYears(),
                    GeekTextTypeEnum.RESUME_WORK_EXPERIENCE
            ));
        }

        JobApplication jobApplication = null;
        if (aiUserForm != null) {
            String formJson = aiUserForm.getFormJson();
            jobApplication = gson.fromJson(formJson, JobApplication.class);

            String expectedPosition = jobApplication.getExpectedPosition();
            if (StringUtils.hasLength(expectedPosition)) {
                textWithTypes.add(new GeekTextWithType(
                        expectedPosition,
                        GeekTextTypeEnum.USER_FORM_POSITION_MATCH
                ));
            }

            String jobStatus = jobApplication.getJobStatus();
            if (StringUtils.hasLength(jobStatus)) {
                textWithTypes.add(new GeekTextWithType(
                        jobStatus,
                        GeekTextTypeEnum.USER_FORM_JOB_STATUS
                ));
            }

            String expectedCity = jobApplication.getExpectedCity();
            if (StringUtils.hasLength(expectedCity)) {
                textWithTypes.add(new GeekTextWithType(
                        expectedCity,
                        GeekTextTypeEnum.USER_FORM_REGION_INTENT
                ));
            }

            String expectedIndustry = jobApplication.getExpectedIndustry();
            if (StringUtils.hasLength(expectedIndustry)) {
                textWithTypes.add(new GeekTextWithType(
                        expectedIndustry,
                        GeekTextTypeEnum.USER_FORM_INDUSTRY_MATCH
                ));
            }

            String workCity = jobApplication.getWorkCity();
            if (StringUtils.hasLength(workCity)) {
                textWithTypes.add(new GeekTextWithType(
                        workCity,
                        GeekTextTypeEnum.USER_FORM_WORK_SITE
                ));
            }
        }

        // 批量处理和存储
        batchProcessAndStoreResumeProfiles(textWithTypes, resumeDTO, examineId, sysUser, roomId, jobApplication, aiViewPortrait);
    }

    /**
     * 批量处理并存储简历主档案
     */
    private void batchProcessAndStoreResumeProfiles(List<GeekTextWithType> textWithTypes, ResumeDTO resumeDTO,
                                                    String examineId, SysUser sysUser, String roomId, JobApplication jobApplication, AiViewPortrait aiViewPortrait) {
        if (textWithTypes.isEmpty()) {
            return;
        }

        try {
            // 提取文本并生成向量
            List<String> texts = textWithTypes.stream()
                    .map(GeekTextWithType::getText)
                    .collect(Collectors.toList());

            List<List<Double>> embeddings = embeddingUtils.embedTexts(texts, 768);

            // 转换为Milvus数据
            List<JsonObject> milvusData = new ArrayList<>();
            for (int i = 0; i < textWithTypes.size(); i++) {
                var dto = RecommendResumeProfilesDTOConvertor.convert(resumeDTO, embeddings.get(i),
                        textWithTypes.get(i), sysUser, examineId, roomId, jobApplication, aiViewPortrait);
                String jsonStr = gson.toJson(dto);
                milvusData.add(gson.fromJson(jsonStr, JsonObject.class));
            }

            // 批量插入
            InsertResp insertResp = milvusTemplate.insert(RESUME_PROFILES_COLLECTION, milvusData);
            if (insertResp != null && !insertResp.getPrimaryKeys().isEmpty()) {
                log.info("成功批量插入{}条简历主档案记录到集合{}", milvusData.size(), RESUME_PROFILES_COLLECTION);
            } else {
                log.error("批量插入简历主档案记录到集合{}失败", RESUME_PROFILES_COLLECTION);
            }
        } catch (Exception e) {
            log.error("批量处理并存储简历主档案时发生异常", e);
        }
    }

    /**
     * 处理并存储教育经历
     */
    private void processAndStoreEducationExperiences(ResumeDTO resumeDTO, SysUser sysUser, String examineId, String roomId, AiViewPortrait aiViewPortrait) {
        // 使用Optional处理整个教育经历列表的空值情况
        Optional.ofNullable(resumeDTO.getEduExperience())
                .filter(experiences -> !experiences.isEmpty())
                .ifPresent(experiences -> {
                    // 流式处理每个教育经历
                    List<GeekRecommendMilvusStoreServiceImpl.EduDataWithEmbedding> eduDataList = experiences.stream()
                            .map(edu -> {
                                // 处理文本类型列表
                                List<GeekTextWithType> textWithTypes = new ArrayList<>();

                                // 处理教育经历内容（RAG分chunk）
                                Optional.ofNullable(edu.getContent())
                                        .filter(StringUtils::hasLength)
                                        .map(textSplitUtils::splitGeek)
                                        .ifPresent(chunks -> chunks.forEach(chunk ->
                                                textWithTypes.add(new GeekTextWithType(chunk, GeekTextTypeEnum.EDU_CONTENT))));

                                // 处理学校名称
                                Optional.ofNullable(edu.getName())
                                        .filter(StringUtils::hasLength)
                                        .map(name -> new GeekTextWithType(name, GeekTextTypeEnum.EDU_NAME))
                                        .ifPresent(textWithTypes::add);

                                // 处理标签
                                Optional.ofNullable(edu.getLabel())
                                        .filter(StringUtils::hasLength)
                                        .map(str -> str.split("\\|"))
                                        .ifPresent(labels ->
                                                Arrays.stream(labels)
                                                        .filter(StringUtils::hasLength)
                                                        .map(String::trim)
                                                        .map(label -> new GeekTextWithType(label, GeekTextTypeEnum.EDU_LABEL))
                                                        .forEach(textWithTypes::add));

                                return new GeekRecommendMilvusStoreServiceImpl.EduDataWithEmbedding(edu, textWithTypes);
                            })
                            .collect(Collectors.toList());

                    // 批量处理和存储
                    batchProcessAndStoreEducationExperiences(eduDataList, sysUser, examineId, roomId, aiViewPortrait);
                });
    }


    /**
     * 批量处理并存储教育经历
     */
    private void batchProcessAndStoreEducationExperiences(List<GeekRecommendMilvusStoreServiceImpl.EduDataWithEmbedding> eduDataList, SysUser sysUser, String examineId, String roomId, AiViewPortrait aiViewPortrait) {
        Optional.ofNullable(eduDataList)
                .filter(list -> !list.isEmpty())
                .ifPresent(list -> {
                    try {
                        // 流式处理所有教育经历数据并转换为Milvus格式
                        List<JsonObject> allMilvusData = list.stream()
                                // 过滤掉没有文本数据的条目
                                .filter(eduData -> !eduData.textWithTypes().isEmpty())
                                // 处理每条教育经历数据
                                .flatMap(eduData -> {
                                    // 提取文本并生成向量
                                    List<String> texts = eduData.textWithTypes().stream()
                                            .map(GeekTextWithType::getText)
                                            .collect(Collectors.toList());

                                    List<List<Double>> embeddings = embeddingUtils.embedTexts(texts, 768);

                                    // 转换为Milvus数据并扁平化为流
                                    return IntStream.range(0, eduData.textWithTypes().size())
                                            .mapToObj(i -> {
                                                Object dto = RecommendEducationExperiencesDTOConvertor.convert(
                                                        eduData.eduExperience(),
                                                        embeddings.get(i),
                                                        eduData.textWithTypes().get(i),
                                                        sysUser,
                                                        examineId,
                                                        roomId,
                                                        aiViewPortrait);
                                                String jsonStr = gson.toJson(dto);
                                                return gson.fromJson(jsonStr, JsonObject.class);
                                            });
                                })
                                .collect(Collectors.toList());

                        // 批量插入所有教育经历数据
                        Optional.of(allMilvusData)
                                .filter(data -> !data.isEmpty())
                                .ifPresent(data -> {
                                    InsertResp insertResp = milvusTemplate.insert(EDUCATION_EXPERIENCES_COLLECTION, data);
                                    if (insertResp != null && !insertResp.getPrimaryKeys().isEmpty()) {
                                        log.info("成功批量插入{}条教育经历记录到集合{}", data.size(), EDUCATION_EXPERIENCES_COLLECTION);
                                    } else {
                                        log.error("批量插入教育经历记录到集合{}失败", EDUCATION_EXPERIENCES_COLLECTION);
                                    }
                                });
                    } catch (Exception e) {
                        log.error("批量处理并存储教育经历时发生异常", e);
                    }
                });
    }


    /**
     * 处理并存储工作经历
     */
    private void processAndStoreWorkExperiences(ResumeDTO resumeDTO, SysUser sysUser, String examineId, String roomId, AiViewPortrait aiViewPortrait) {
        Optional.ofNullable(resumeDTO.getWorkExperience())
                .filter(experiences -> !experiences.isEmpty())
                .ifPresent(experiences -> {
                    List<GeekRecommendMilvusStoreServiceImpl.WorkDataWithEmbedding> workDataList = experiences.stream()
                            .map(work -> {
                                List<GeekTextWithType> textWithTypes = new ArrayList<>();

                                // 处理工作经历内容（RAG分chunk）
                                Optional.ofNullable(work.getContent())
                                        .filter(StringUtils::hasLength)
                                        .map(textSplitUtils::splitGeek)
                                        .ifPresent(chunks -> chunks.forEach(chunk ->
                                                textWithTypes.add(new GeekTextWithType(chunk, GeekTextTypeEnum.WORK_CONTENT))));

                                // 处理公司名称
                                Optional.ofNullable(work.getName())
                                        .filter(StringUtils::hasLength)
                                        .map(name -> new GeekTextWithType(name, GeekTextTypeEnum.WORK_NAME))
                                        .ifPresent(textWithTypes::add);

                                // 处理标签
                                Optional.ofNullable(work.getLabel())
                                        .filter(StringUtils::hasLength)
                                        .map(str -> str.split("\\|"))
                                        .ifPresent(labels ->
                                                Arrays.stream(labels)
                                                        .filter(StringUtils::hasLength)
                                                        .map(String::trim)
                                                        .map(label -> new GeekTextWithType(label, GeekTextTypeEnum.WORK_LABEL))
                                                        .forEach(textWithTypes::add));

                                return new GeekRecommendMilvusStoreServiceImpl.WorkDataWithEmbedding(work, textWithTypes);
                            })
                            .collect(Collectors.toList());

                    // 批量处理和存储
                    batchProcessAndStoreWorkExperiences(workDataList, sysUser, examineId, roomId, aiViewPortrait);
                });
    }

    /**
     * 批量处理并存储工作经历
     */
    private void batchProcessAndStoreWorkExperiences(List<GeekRecommendMilvusStoreServiceImpl.WorkDataWithEmbedding> workDataList,
                                                     SysUser sysUser, String examineId, String roomId, AiViewPortrait aiViewPortrait) {
        Optional.ofNullable(workDataList)
                .filter(list -> !list.isEmpty())
                .ifPresent(list -> {
                    try {
                        // 流式处理所有工作经历数据并转换为Milvus格式
                        List<JsonObject> allMilvusData = list.stream()
                                // 过滤掉没有文本数据的条目
                                .filter(workData -> !workData.textWithTypes().isEmpty())
                                // 处理每条工作经历数据
                                .flatMap(workData -> {
                                    // 提取文本并生成向量
                                    List<String> texts = workData.textWithTypes().stream()
                                            .map(GeekTextWithType::getText)
                                            .collect(Collectors.toList());

                                    List<List<Double>> embeddings = embeddingUtils.embedTexts(texts, 768);

                                    // 转换为Milvus数据并扁平化为流
                                    return IntStream.range(0, workData.textWithTypes().size())
                                            .mapToObj(i -> {
                                                Object dto = RecommendWorkExperiencesDTOConvertor.convert(
                                                        workData.workExperience(),
                                                        embeddings.get(i),
                                                        workData.textWithTypes().get(i),
                                                        sysUser,
                                                        examineId,
                                                        roomId,
                                                        aiViewPortrait);
                                                String jsonStr = gson.toJson(dto);
                                                return gson.fromJson(jsonStr, JsonObject.class);
                                            });
                                })
                                .collect(Collectors.toList());

                        // 批量插入所有工作经历数据
                        Optional.of(allMilvusData)
                                .filter(data -> !data.isEmpty())
                                .ifPresent(data -> {
                                    InsertResp insertResp = milvusTemplate.insert(WORK_EXPERIENCES_COLLECTION, data);
                                    if (insertResp != null && !insertResp.getPrimaryKeys().isEmpty()) {
                                        log.info("成功批量插入{}条工作经历记录到集合{}", data.size(), WORK_EXPERIENCES_COLLECTION);
                                    } else {
                                        log.error("批量插入工作经历记录到集合{}失败", WORK_EXPERIENCES_COLLECTION);
                                    }
                                });
                    } catch (Exception e) {
                        log.error("批量处理并存储工作经历时发生异常", e);
                    }
                });
    }


    /**
     * 处理并存储项目经历
     */
    private void processAndStoreProjectExperiences(ResumeDTO resumeDTO, SysUser sysUser, String examineId, String roomId, AiViewPortrait aiViewPortrait) {
        Optional.ofNullable(resumeDTO.getProjectExperience())
                .filter(experiences -> !experiences.isEmpty())
                .ifPresent(experiences -> {
                    List<GeekRecommendMilvusStoreServiceImpl.ProjectDataWithEmbedding> projectDataList = experiences.stream()
                            .map(project -> {
                                List<GeekTextWithType> textWithTypes = new ArrayList<>();

                                // 处理项目经历内容（RAG分chunk）
                                Optional.ofNullable(project.getContent())
                                        .filter(StringUtils::hasLength)
                                        .map(textSplitUtils::splitGeek)
                                        .ifPresent(chunks -> chunks.forEach(chunk ->
                                                textWithTypes.add(new GeekTextWithType(chunk, GeekTextTypeEnum.PROJECT_CONTENT))));

                                // 处理项目名称
                                Optional.ofNullable(project.getName())
                                        .filter(StringUtils::hasLength)
                                        .map(name -> new GeekTextWithType(name, GeekTextTypeEnum.PROJECT_NAME))
                                        .ifPresent(textWithTypes::add);

                                // 处理标签
                                Optional.ofNullable(project.getLabel())
                                        .filter(StringUtils::hasLength)
                                        .map(str -> str.split("\\|"))
                                        .ifPresent(labels ->
                                                Arrays.stream(labels)
                                                        .filter(StringUtils::hasLength)
                                                        .map(String::trim)
                                                        .map(label -> new GeekTextWithType(label, GeekTextTypeEnum.PROJECT_LABEL))
                                                        .forEach(textWithTypes::add));

                                return new GeekRecommendMilvusStoreServiceImpl.ProjectDataWithEmbedding(project, textWithTypes);
                            })
                            .collect(Collectors.toList());

                    // 批量处理和存储
                    batchProcessAndStoreProjectExperiences(projectDataList, sysUser, examineId, roomId, aiViewPortrait);
                });
    }

    /**
     * 批量处理并存储项目经历
     */
    private void batchProcessAndStoreProjectExperiences(List<GeekRecommendMilvusStoreServiceImpl.ProjectDataWithEmbedding> projectDataList,
                                                        SysUser sysUser, String examineId, String roomId, AiViewPortrait aiViewPortrait) {
        Optional.ofNullable(projectDataList)
                .filter(list -> !list.isEmpty())
                .ifPresent(list -> {
                    try {
                        // 流式处理所有项目经历数据并转换为Milvus格式
                        List<JsonObject> allMilvusData = list.stream()
                                // 过滤掉没有文本数据的条目
                                .filter(projectData -> !projectData.textWithTypes().isEmpty())
                                // 处理每条项目经历数据
                                .flatMap(projectData -> {
                                    // 提取文本并生成向量
                                    List<String> texts = projectData.textWithTypes().stream()
                                            .map(GeekTextWithType::getText)
                                            .collect(Collectors.toList());

                                    List<List<Double>> embeddings = embeddingUtils.embedTexts(texts, 768);

                                    // 转换为Milvus数据并扁平化为流
                                    return IntStream.range(0, projectData.textWithTypes().size())
                                            .mapToObj(i -> {
                                                Object dto = RecommendProjectExperiencesDTOConvertor.convert(
                                                        projectData.projectExperience(),
                                                        embeddings.get(i),
                                                        projectData.textWithTypes().get(i),
                                                        sysUser,
                                                        examineId,
                                                        roomId,
                                                        aiViewPortrait);
                                                String jsonStr = gson.toJson(dto);
                                                return gson.fromJson(jsonStr, JsonObject.class);
                                            });
                                })
                                .collect(Collectors.toList());

                        // 批量插入所有项目经历数据
                        Optional.of(allMilvusData)
                                .filter(data -> !data.isEmpty())
                                .ifPresent(data -> {
                                    InsertResp insertResp = milvusTemplate.insert(PROJECT_EXPERIENCES_COLLECTION, data);
                                    if (insertResp != null && !insertResp.getPrimaryKeys().isEmpty()) {
                                        log.info("成功批量插入{}条项目经历记录到集合{}", data.size(), PROJECT_EXPERIENCES_COLLECTION);
                                    } else {
                                        log.error("批量插入项目经历记录到集合{}失败", PROJECT_EXPERIENCES_COLLECTION);
                                    }
                                });
                    } catch (Exception e) {
                        log.error("批量处理并存储项目经历时发生异常", e);
                    }
                });
    }


    /**
     * 处理并存储校园经历
     */
    private void processAndStoreSchoolExperiences(ResumeDTO resumeDTO, SysUser sysUser, String examineId, String roomId, AiViewPortrait aiViewPortrait) {
        Optional.ofNullable(resumeDTO.getSchoolExperience())
                .filter(experiences -> !experiences.isEmpty())
                .ifPresent(experiences -> {
                    List<GeekRecommendMilvusStoreServiceImpl.SchoolDataWithEmbedding> schoolDataList = experiences.stream()
                            .map(school -> {
                                List<GeekTextWithType> textWithTypes = new ArrayList<>();

                                // 处理校园经历内容（RAG分chunk）
                                Optional.ofNullable(school.getContent())
                                        .filter(StringUtils::hasLength)
                                        .map(textSplitUtils::splitGeek)
                                        .ifPresent(chunks -> chunks.forEach(chunk ->
                                                textWithTypes.add(new GeekTextWithType(
                                                        chunk, GeekTextTypeEnum.SCHOOL_CONTENT))));


                                // 处理标签
                                Optional.ofNullable(school.getName())
                                        .filter(StringUtils::hasLength)
                                        .map(label -> new GeekTextWithType(
                                                label, GeekTextTypeEnum.SCHOOL_NAME))
                                        .ifPresent(textWithTypes::add);

                                // 处理标签
                                Optional.ofNullable(school.getLabel())
                                        .filter(StringUtils::hasLength)
                                        .map(str -> str.split("\\|"))
                                        .ifPresent(labels ->
                                                Arrays.stream(labels)
                                                        .filter(StringUtils::hasLength)
                                                        .map(String::trim)
                                                        .map(label -> new GeekTextWithType(label, GeekTextTypeEnum.SCHOOL_LABEL))
                                                        .forEach(textWithTypes::add));

                                return new GeekRecommendMilvusStoreServiceImpl.SchoolDataWithEmbedding(school, textWithTypes);
                            })
                            .collect(Collectors.toList());

                    // 批量处理和存储
                    batchProcessAndStoreSchoolExperiences(
                            schoolDataList, sysUser, examineId, roomId, aiViewPortrait);
                });
    }

    /**
     * 批量处理并存储校园经历
     */
    private void batchProcessAndStoreSchoolExperiences(
            List<GeekRecommendMilvusStoreServiceImpl.SchoolDataWithEmbedding> schoolDataList,
            SysUser sysUser, String examineId, String roomId, AiViewPortrait aiViewPortrait) {
        Optional.ofNullable(schoolDataList)
                .filter(list -> !list.isEmpty())
                .ifPresent(list -> {
                    try {
                        // 流式处理所有校园经历数据并转换为Milvus格式
                        List<JsonObject> allMilvusData = list.stream()
                                // 过滤掉没有文本数据的条目
                                .filter(schoolData -> !schoolData.textWithTypes().isEmpty())
                                // 处理每条校园经历数据
                                .flatMap(schoolData -> {
                                    // 提取文本并生成向量
                                    List<String> texts = schoolData.textWithTypes().stream()
                                            .map(GeekTextWithType::getText)
                                            .collect(Collectors.toList());

                                    List<List<Double>> embeddings = embeddingUtils.embedTexts(texts, 768);

                                    // 转换为Milvus数据并扁平化为流
                                    return IntStream.range(0, schoolData.textWithTypes().size())
                                            .mapToObj(i -> {
                                                Object dto = RecommendSchoolExperiencesDTOConvertor.convert(
                                                        schoolData.schoolExperience(),
                                                        embeddings.get(i),
                                                        schoolData.textWithTypes().get(i),
                                                        sysUser,
                                                        examineId,
                                                        roomId,
                                                        aiViewPortrait);
                                                String jsonStr = gson.toJson(dto);
                                                return gson.fromJson(jsonStr, JsonObject.class);
                                            });
                                })
                                .collect(Collectors.toList());

                        // 批量插入所有校园经历数据
                        Optional.of(allMilvusData)
                                .filter(data -> !data.isEmpty())
                                .ifPresent(data -> {
                                    InsertResp insertResp = milvusTemplate.insert(
                                            SCHOOL_EXPERIENCES_COLLECTION, data);
                                    if (insertResp != null && !insertResp.getPrimaryKeys().isEmpty()) {
                                        log.info("成功批量插入{}条校园经历记录到集合{}",
                                                data.size(), SCHOOL_EXPERIENCES_COLLECTION);
                                    } else {
                                        log.error("批量插入校园经历记录到集合{}失败",
                                                SCHOOL_EXPERIENCES_COLLECTION);
                                    }
                                });
                    } catch (Exception e) {
                        log.error("批量处理并存储校园经历时发生异常", e);
                    }
                });
    }

    /**
     * 处理并存储报告数据
     */
    private void processAndStoreReports(CandidateReport candidateReport, SysUser sysUser, String examineId, String roomId, AiViewPortrait aiViewPortrait) {
        Optional.ofNullable(candidateReport)
                .map(CandidateReport::getDimensions)
                .filter(dimensions -> !dimensions.isEmpty())
                .ifPresent(dimensions -> {
                    List<GeekRecommendMilvusStoreServiceImpl.ReportDataWithEmbedding> reportDataList = dimensions.stream()
                            .map(dimension -> {
                                List<GeekTextWithType> textWithTypes = new ArrayList<>();

                                // 处理评估评语（需要RAG分chunk）
                                Optional.ofNullable(dimension.getComments())
                                        .filter(StringUtils::hasLength)
                                        .map(textSplitUtils::splitGeek)
                                        .ifPresent(chunks -> chunks.forEach(chunk ->
                                                textWithTypes.add(new GeekTextWithType(chunk, GeekTextTypeEnum.REPORT_COMMENTS))));

                                // 处理维度总结（需要RAG分chunk）
                                Optional.ofNullable(dimension.getSummary())
                                        .filter(StringUtils::hasLength)
                                        .ifPresent(summary -> textWithTypes.add(new GeekTextWithType(summary, GeekTextTypeEnum.REPORT_SUMMARY)));

                                return new GeekRecommendMilvusStoreServiceImpl.ReportDataWithEmbedding(dimension, textWithTypes);
                            })
                            .collect(Collectors.toList());

                    // 批量处理和存储
                    batchProcessAndStoreReports(reportDataList, sysUser, examineId, roomId, aiViewPortrait);
                });
    }

    /**
     * 批量处理并存储报告数据
     */
    private void batchProcessAndStoreReports(List<GeekRecommendMilvusStoreServiceImpl.ReportDataWithEmbedding> reportDataList,
                                             SysUser sysUser, String examineId, String roomId, AiViewPortrait aiViewPortrait) {
        Optional.ofNullable(reportDataList)
                .filter(list -> !list.isEmpty())
                .ifPresent(list -> {
                    try {
                        // 流式处理所有报告数据并转换为Milvus格式
                        List<JsonObject> allMilvusData = list.stream()
                                // 过滤掉没有文本数据的条目
                                .filter(reportData -> !reportData.textWithTypes().isEmpty())
                                // 处理每条报告数据
                                .flatMap(reportData -> {
                                    // 提取文本并生成向量
                                    List<String> texts = reportData.textWithTypes().stream()
                                            .map(GeekTextWithType::getText)
                                            .collect(Collectors.toList());

                                    List<List<Double>> embeddings = embeddingUtils.embedTexts(texts, 768);

                                    // 转换为Milvus数据并扁平化为流
                                    return IntStream.range(0, reportData.textWithTypes().size())
                                            .mapToObj(i -> {
                                                var dto = RecommendReportDTOConvertor.convert(
                                                        reportData.reportDimension().getLabels(),
                                                        reportData.reportDimension().getCheckPoint(),
                                                        reportData.reportDimension().getScore(),
                                                        embeddings.get(i),
                                                        reportData.textWithTypes().get(i),
                                                        sysUser,
                                                        examineId,
                                                        roomId,
                                                        aiViewPortrait);
                                                String jsonStr = gson.toJson(dto);
                                                return gson.fromJson(jsonStr, JsonObject.class);
                                            });
                                })
                                .collect(Collectors.toList());

                        // 批量插入所有报告数据
                        Optional.of(allMilvusData)
                                .filter(data -> !data.isEmpty())
                                .ifPresent(data -> {
                                    InsertResp insertResp = milvusTemplate.insert(REPORT_COLLECTION, data);
                                    if (insertResp != null && !insertResp.getPrimaryKeys().isEmpty()) {
                                        log.info("成功批量插入{}条报告记录到集合{}", data.size(), REPORT_COLLECTION);
                                    } else {
                                        log.error("批量插入报告记录到集合{}失败", REPORT_COLLECTION);
                                    }
                                });
                    } catch (Exception e) {
                        log.error("批量处理并存储报告数据时发生异常", e);
                    }
                });
    }

    /**
     * 处理并存储人才画像维度数据
     */
    private void processAndStorePortraitDimensions(CandidatePortrait candidatePortrait, SysUser sysUser, String examineId, String roomId, AiViewPortrait aiViewPortrait) {
        Optional.ofNullable(candidatePortrait)
                .map(CandidatePortrait::getDimensions)
                .filter(dimensions -> !dimensions.isEmpty())
                .ifPresent(dimensions -> {
                    List<GeekRecommendMilvusStoreServiceImpl.PortraitDimensionDataWithEmbedding> dimensionDataList = dimensions.stream()
                            .map(dimension -> {
                                List<GeekTextWithType> textWithTypes = new ArrayList<>();

                                // 处理优势分析（需要RAG分chunk）
                                Optional.ofNullable(dimension.getAdvantages())
                                        .filter(StringUtils::hasLength)
                                        .map(textSplitUtils::splitGeek)
                                        .ifPresent(chunks -> chunks.forEach(chunk ->
                                                textWithTypes.add(new GeekTextWithType(chunk, GeekTextTypeEnum.PORTRAIT_DIMENSION_ADVANTAGES))));

                                // 处理劣势分析（需要RAG分chunk）
                                Optional.ofNullable(dimension.getDisadvantages())
                                        .filter(StringUtils::hasLength)
                                        .map(textSplitUtils::splitGeek)
                                        .ifPresent(chunks -> chunks.forEach(chunk ->
                                                textWithTypes.add(new GeekTextWithType(chunk, GeekTextTypeEnum.PORTRAIT_DIMENSION_DISADVANTAGES))));

                                return new GeekRecommendMilvusStoreServiceImpl.PortraitDimensionDataWithEmbedding(dimension, textWithTypes);
                            })
                            .collect(Collectors.toList());

                    // 批量处理和存储
                    batchProcessAndStorePortraitDimensions(dimensionDataList, sysUser, examineId, roomId, aiViewPortrait);
                });
    }

    /**
     * 批量处理并存储人才画像维度数据
     */
    private void batchProcessAndStorePortraitDimensions(List<GeekRecommendMilvusStoreServiceImpl.PortraitDimensionDataWithEmbedding> dimensionDataList,
                                                        SysUser sysUser, String examineId, String roomId, AiViewPortrait aiViewPortrait) {
        Optional.ofNullable(dimensionDataList)
                .filter(list -> !list.isEmpty())
                .ifPresent(list -> {
                    try {
                        // 流式处理所有维度数据并转换为Milvus格式
                        List<JsonObject> allMilvusData = list.stream()
                                // 过滤掉没有文本数据的条目
                                .filter(dimensionData -> !dimensionData.textWithTypes().isEmpty())
                                // 处理每条维度数据
                                .flatMap(dimensionData -> {
                                    // 提取文本并生成向量
                                    List<String> texts = dimensionData.textWithTypes().stream()
                                            .map(GeekTextWithType::getText)
                                            .collect(Collectors.toList());

                                    List<List<Double>> embeddings = embeddingUtils.embedTexts(texts, 768);

                                    // 转换为Milvus数据并扁平化为流
                                    return IntStream.range(0, dimensionData.textWithTypes().size())
                                            .mapToObj(i -> {
                                                RecommendPortraitDimensionDTO dto = RecommendPortraitDimensionDTOConvertor.convert(
                                                        dimensionData.portraitDimension().getDimensionName(),
                                                        dimensionData.portraitDimension().getScore(),
                                                        embeddings.get(i),
                                                        dimensionData.textWithTypes().get(i),
                                                        sysUser,
                                                        examineId,
                                                        roomId,
                                                        aiViewPortrait);
                                                String jsonStr = gson.toJson(dto);
                                                return gson.fromJson(jsonStr, JsonObject.class);
                                            });
                                })
                                .collect(Collectors.toList());

                        // 批量插入所有维度数据
                        Optional.of(allMilvusData)
                                .filter(data -> !data.isEmpty())
                                .ifPresent(data -> {
                                    InsertResp insertResp = milvusTemplate.insert(PORTRAIT_DIMENSION_COLLECTION, data);
                                    if (insertResp != null && !insertResp.getPrimaryKeys().isEmpty()) {
                                        log.info("成功批量插入{}条人才画像维度记录到集合{}", data.size(), PORTRAIT_DIMENSION_COLLECTION);
                                    } else {
                                        log.error("批量插入人才画像维度记录到集合{}失败", PORTRAIT_DIMENSION_COLLECTION);
                                    }
                                });
                    } catch (Exception e) {
                        log.error("批量处理并存储人才画像维度数据时发生异常", e);
                    }
                });
    }

    /**
     * 处理并存储人才画像评估数据
     */
    private void processAndStorePortraitEvaluations(CandidatePortrait candidatePortrait, SysUser sysUser, String examineId, String roomId, AiViewPortrait aiViewPortrait) {
        Optional.ofNullable(candidatePortrait)
                .ifPresent(portrait -> {
                    List<GeekTextWithType> textWithTypes = new ArrayList<>();

                    // 处理综合评价（需要RAG分chunk）
                    Optional.ofNullable(portrait.getComments())
                            .filter(StringUtils::hasLength)
                            .map(textSplitUtils::splitGeek)
                            .ifPresent(chunks -> chunks.forEach(chunk ->
                                    textWithTypes.add(new GeekTextWithType(chunk, GeekTextTypeEnum.PORTRAIT_COMMENTS))));

                    // 处理评估内容（需要RAG分chunk）
                    Optional.ofNullable(portrait.getEvaluations())
                            .filter(StringUtils::hasLength)
                            .map(textSplitUtils::splitGeek)
                            .ifPresent(chunks -> chunks.forEach(chunk ->
                                    textWithTypes.add(new GeekTextWithType(chunk, GeekTextTypeEnum.PORTRAIT_EVALUATIONS))));

                    // 处理亮点总结（需要RAG分chunk）
                    Optional.ofNullable(portrait.getHighlights())
                            .filter(StringUtils::hasLength)
                            .map(textSplitUtils::splitGeek)
                            .ifPresent(chunks -> chunks.forEach(chunk ->
                                    textWithTypes.add(new GeekTextWithType(chunk, GeekTextTypeEnum.PORTRAIT_HIGHLIGHTS))));

                    // 批量处理和存储
                    batchProcessAndStorePortraitEvaluations(textWithTypes, sysUser, examineId, roomId, aiViewPortrait);
                });
    }

    /**
     * 批量处理并存储人才画像评估数据
     */
    private void batchProcessAndStorePortraitEvaluations(List<GeekTextWithType> textWithTypes, SysUser sysUser, String examineId, String roomId, AiViewPortrait aiViewPortrait) {
        Optional.ofNullable(textWithTypes)
                .filter(list -> !list.isEmpty())
                .ifPresent(list -> {
                    try {
                        // 提取文本并生成向量
                        List<String> texts = list.stream()
                                .map(GeekTextWithType::getText)
                                .collect(Collectors.toList());

                        List<List<Double>> embeddings = embeddingUtils.embedTexts(texts, 768);

                        // 转换为Milvus数据
                        List<JsonObject> milvusData = new ArrayList<>();
                        for (int i = 0; i < list.size(); i++) {
                            var dto = RecommendPortraitEvaluationDTOConvertor.convert(
                                    embeddings.get(i),
                                    list.get(i),
                                    sysUser,
                                    examineId,
                                    roomId,
                                    aiViewPortrait);
                            String jsonStr = gson.toJson(dto);
                            milvusData.add(gson.fromJson(jsonStr, JsonObject.class));
                        }

                        // 批量插入
                        InsertResp insertResp = milvusTemplate.insert(PORTRAIT_EVALUATION_COLLECTION, milvusData);
                        if (insertResp != null && !insertResp.getPrimaryKeys().isEmpty()) {
                            log.info("成功批量插入{}条人才画像评估记录到集合{}", milvusData.size(), PORTRAIT_EVALUATION_COLLECTION);
                        } else {
                            log.error("批量插入人才画像评估记录到集合{}失败", PORTRAIT_EVALUATION_COLLECTION);
                        }
                    } catch (Exception e) {
                        log.error("批量处理并存储人才画像评估数据时发生异常", e);
                    }
                });
    }

    /**
     * 数据包装类 - 教育经历
     */
    private record EduDataWithEmbedding(ResumeDTO.EduExperience eduExperience, List<GeekTextWithType> textWithTypes) {
    }

    /**
     * 数据包装类 - 工作经历
     */
    private record WorkDataWithEmbedding(ResumeDTO.WorkExperience workExperience, List<GeekTextWithType> textWithTypes) {
    }

    /**
     * 数据包装类 - 项目经历
     */
    private record ProjectDataWithEmbedding(ResumeDTO.ProjectExperience projectExperience, List<GeekTextWithType> textWithTypes) {
    }

    /**
     * 数据包装类 - 校园经历
     */
    private record SchoolDataWithEmbedding(ResumeDTO.SchoolExperience schoolExperience, List<GeekTextWithType> textWithTypes) {
    }

    /**
     * 数据包装类 - 报告维度
     */
    private record ReportDataWithEmbedding(CandidateReport.Dimension reportDimension,
                                           List<GeekTextWithType> textWithTypes) {
    }

    /**
     * 数据包装类 - 人才画像维度
     */
    private record PortraitDimensionDataWithEmbedding(CandidatePortrait.Dimension portraitDimension,
                                                      List<GeekTextWithType> textWithTypes) {
    }
}
