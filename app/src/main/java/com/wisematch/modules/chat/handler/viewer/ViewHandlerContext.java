package com.wisematch.modules.chat.handler.viewer;

import com.wisematch.modules.chat.entity.AiAgentPool;
import com.wisematch.modules.chat.entity.AiChatIntention;
import com.wisematch.modules.chat.entity.AiViewRecord;
import com.wisematch.modules.chat.entity.AiViewPrepare;
import com.wisematch.modules.chat.model.ChatViewMsg;
import com.wisematch.modules.chat.model.GenQuestion;
import com.wisematch.modules.chat.model.QuestionVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 面试上下文
 * <AUTHOR>
 * @version AgentContext.java, v0.1 2025-07-15 17:23
 */
@Data
public class ViewHandlerContext {

    String roomId;

    String userId;

    String replyId;

    /**
     * 当前消息
     */
    ChatViewMsg chatViewMsg;

    /**
     * 用户最后一次输入
     */
    String userMsg;

    /**
     * 最后一次系统输出
     */
    String lastedAgentMsg;

    /**
     * 当前问题
     */
    GenQuestion question;

    /**
     * 问题清单
     */
    List<GenQuestion> questionList;

    /**
     * 意图
     */
    AiChatIntention intention;

    /**
     * 意图识别后的agent
     */
    AiAgentPool aiAgentPool;


    AiViewRecord aiViewRecord;


    AiViewPrepare aiViewPrepare;

    long startTime;

    String prompt;
}
