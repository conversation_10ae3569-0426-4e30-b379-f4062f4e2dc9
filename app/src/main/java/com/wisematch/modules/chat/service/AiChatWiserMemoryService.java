package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiChatWiserMemory;
import com.wisematch.modules.chat.model.ChatMessage;
import org.springframework.ai.chat.messages.Message;
import com.wisematch.modules.chat.model.UserPreferenceVO;

import java.util.List;


/**
 * AI Wiser智能体服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */
public interface AiChatWiserMemoryService extends IService<AiChatWiserMemory>{

    AiChatWiserMemory addUserMemory(String conversationId, ChatMessage chatMessage);

    AiChatWiserMemory addAssistantMemory(String conversationId, ChatMessage chatMessage);

    AiChatWiserMemory updateAssistantMemory(String id, ChatMessage chatMessage, String msg);

    AiChatWiserMemory updateAssistantMsg(String id, String msg);

    void updateUserPreferences(UserPreferenceVO userPreferenceVO);

    void addAssistantMemory(String conversationId, List<ChatMessage> messages);

    List<AiChatWiserMemory> getList(String conversationId);

    List<Message> getListAsMessages(String chatId);

}
