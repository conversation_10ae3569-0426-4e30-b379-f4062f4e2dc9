package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiWiserSuggest;
import com.wisematch.modules.chat.model.AiWiserSuggestQueryDTO;

import java.util.List;

public interface AiWiserSuggestService extends IService<AiWiserSuggest> {
    Page<AiWiserSuggest> pageQuery(AiWiserSuggestQueryDTO aiWiserSuggestQueryDTO);
    void batchLogicDelete(List<String> list);
}
