package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.modules.chat.entity.AiExamineAgentMapping;
import com.wisematch.modules.chat.mapper.AiExamineAgentMappingMapper;
import com.wisematch.modules.chat.service.AiExamineAgentMappingService;
import org.springframework.stereotype.Service;

/**
 * AI考察维度与Agent映射Service实现类
 */
@Service
public class AiExamineAgentMappingServiceImpl extends ServiceImpl<AiExamineAgentMappingMapper, AiExamineAgentMapping>
        implements AiExamineAgentMappingService {
    // 继承ServiceImpl后，已实现IService接口的方法
    // 可根据业务需求重写或添加自定义方法
}
