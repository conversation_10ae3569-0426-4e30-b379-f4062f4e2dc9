package com.wisematch.modules.chat.event;

import com.wisematch.modules.chat.enums.PositionOperationType;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class PositionChangeEvent extends ApplicationEvent {

    // 操作类型，记录职位的具体变更类型
    private final PositionOperationType operationType;
    private final String positionId;


    /**
     * 构造方法（必须调用父类构造器）
     * @param source 事件源（通常是触发事件的对象）
     * @param positionId 职位ID
     * @param operationType 操作类型
     */
    public PositionChangeEvent(Object source, String positionId, PositionOperationType operationType) {
        super(source);
        // 职位ID，标识发生变更的职位
        this.operationType = operationType;
        this.positionId = positionId;
    }
}
