package com.wisematch.modules.chat.wrapper;

import com.wisematch.common.utils.SpringContextUtils;
import com.wisematch.modules.chat.wrapper.wiser.*;
import lombok.Getter;

public enum WiserWrapperEnum {

    DEFAULT_WISER("DEFAULT_WISER", DefaultWiserWrapper.class),
    COURSE_SUGGEST("COURSE_SUGGEST",AiCourseSuggestWrapper.class),
    MBTI_RECOMMEND("MBTI_RECOMMEND",AiMbtiSuggestWrapper.class),
    INTERVIEW_RECOMMEND_MODEL_NEW("INTERVIEW_RECOMMEND_MODEL_NEW",AiJobTrainSuggestWrapper.class),
    JOB_RECOMMEND_MODEL_NEW("JOB_RECOMMEND_MODEL_NEW", AiJobPositionSuggestWrapper.class),
    CAREER_PLANNING_NEW("CAREER_PLANNING_NEW", AiCareerPlanningWrapper.class),
    INDUSTRY_INTEL_NEW("INDUSTRY_INTEL_NEW", AiIndustryAnalysisWrapper.class);

    private final Class<? extends AbstractWiseMatchWrapper> wrapperClass;

    @Getter
    private String code;

    WiserWrapperEnum(String code, Class<? extends AbstractWiseMatchWrapper> wrapperClass) {
        this.code = code;
        this.wrapperClass = wrapperClass;
    }

    /**
     * 从 Spring 容器里拿 Bean（自动注入过的）
     */
    public AbstractWiseMatchWrapper getWiserWrapper() {
        return SpringContextUtils.getBean(wrapperClass);
    }



    // 方案1：遍历查找（简单，但每次调用都会循环）
    public static WiserWrapperEnum fromCode(String code) {
        for (WiserWrapperEnum e : WiserWrapperEnum.values()) {
            if (e.code.equalsIgnoreCase(code)) {
                return e;
            }
        }
        return DEFAULT_WISER;
    }

    /**
     * 根据 code 直接获取对应的 Wrapper 实例
     */
    public static AbstractWiseMatchWrapper getWrapperByCode(String code) {
        return fromCode(code).getWiserWrapper();
    }
}
