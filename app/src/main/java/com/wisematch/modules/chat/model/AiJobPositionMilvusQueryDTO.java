package com.wisematch.modules.chat.model;

import lombok.Data;

import java.util.List;

/**
 * Milvus职位向量数据VO，与集合字段结构一一对应
 */
@Data
public class AiJobPositionMilvusQueryDTO {

    private String positionId;

    private String orgId;

    private String userId;

    private String title;

    private String textContent;

    private String summary;

    private List<String> benefits;

    private List<String> skills;

    private Long createTime;

    private Long updateTime;

    /**
     * 薪酬范围最小值
     * 对应Milvus Int32类型
     */
    private Integer salaryMin;

    /**
     * 薪酬范围最大值
     * 对应Milvus Int32类型
     */
    private Integer salaryMax;


    /**
     * 审核状态
     * 对应Milvus Int32类型
     */
    private Integer auditStatus;

    /**
     * 删除状态
     * 对应Milvus Int32类型
     */
    private Integer delStatus;

    /**
     * 职位状态
     * 对应Milvus Int32类型
     */
    private Integer positionStatus;

}