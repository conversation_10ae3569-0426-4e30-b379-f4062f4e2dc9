package com.wisematch.modules.chat.config;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.common.IndexParam;
import io.milvus.v2.service.collection.request.CreateCollectionReq;
import io.milvus.v2.service.collection.request.DropCollectionReq;
import io.milvus.v2.service.collection.request.HasCollectionReq;
import io.milvus.v2.service.index.request.CreateIndexReq;
import io.milvus.v2.service.index.request.DropIndexReq;
import io.milvus.v2.service.vector.request.*;
import io.milvus.v2.service.vector.request.data.BaseVector;
import io.milvus.v2.service.vector.request.data.FloatVec;
import io.milvus.v2.service.vector.response.InsertResp;
import io.milvus.v2.service.vector.response.QueryResp;
import io.milvus.v2.service.vector.response.SearchResp;
import io.milvus.v2.service.vector.response.UpsertResp;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
public class MilvusTemplate {

    @Resource
    private MilvusClientV2 client;

    @Resource
    private Gson gson;

    /**
     * 更新或插入数据（upsert操作）
     * @param upsertReq upsert请求对象
     * @return upsert响应
     */
    public UpsertResp upsert(UpsertReq upsertReq) {
        log.info("开始upsert操作，集合名称: {}, 数据条数: {}",
                upsertReq.getCollectionName(), upsertReq.getData().size());
        try {
            UpsertResp response = client.upsert(upsertReq);
            log.info("upsert操作成功，集合名称: {}, 处理条数: {}",
                    upsertReq.getCollectionName(), upsertReq.getData().size());
            return response;
        } catch (Exception e) {
            log.error("upsert操作失败，集合名称: {}, 数据条数: {}",
                    upsertReq.getCollectionName(), upsertReq.getData().size(), e);
            return null;
        }
    }

    /**
     * 更新或插入数据的重载方法
     * @param collectionName 集合名称
     * @param data 要更新/插入的数据
     * @return upsert响应
     */
    public UpsertResp upsert(String collectionName, List<JsonObject> data) {
        UpsertReq upsertReq = UpsertReq.builder()
                .collectionName(collectionName)
                .data(data)
                .build();
        return upsert(upsertReq);
    }

    /**
     * 安全地更新指定字段，不会覆盖其他字段
     * @param collectionName 集合名称
     * @param ids 要更新的记录ID列表
     * @param fieldUpdates 要更新的字段及其值的映射
     * @return 是否更新成功
     */
    public boolean updateFields(String collectionName, List<Long> ids, Map<String, Object> fieldUpdates) {
        log.info("开始安全更新字段，集合名称: {}, 记录数量: {}, 更新字段: {}",
                collectionName, ids.size(), fieldUpdates.keySet());
        try {
            // 1. 查询现有记录
            List<String> outputFields = List.of("*");
            QueryResp existingRecords = queryById(collectionName, ids, outputFields);

            if (existingRecords == null || existingRecords.getQueryResults().isEmpty()) {
                log.warn("未找到集合{}中ID为{}的记录", collectionName, ids);
                return false;
            }

            // 2. 构建更新数据
            List<JsonObject> updateData = new ArrayList<>();
            for (QueryResp.QueryResult result : existingRecords.getQueryResults()) {
                JsonObject updateRecord = new JsonObject();
                Map<String, Object> entity = result.getEntity();

                // 复制所有原有字段
                for (Map.Entry<String, Object> entry : entity.entrySet()) {
                    String fieldName = entry.getKey();
                    Object fieldValue = entry.getValue();

                    // 如果是要更新的字段，使用新值；否则保留原值
                    if (fieldUpdates.containsKey(fieldName)) {
                        Object newValue = fieldUpdates.get(fieldName);
                        addPropertyToJsonObject(updateRecord, fieldName, newValue);
                    } else {
                        addPropertyToJsonObject(updateRecord, fieldName, fieldValue);
                    }
                }

                // 添加新字段（如果原记录中不存在）
                for (Map.Entry<String, Object> updateEntry : fieldUpdates.entrySet()) {
                    if (!entity.containsKey(updateEntry.getKey())) {
                        addPropertyToJsonObject(updateRecord, updateEntry.getKey(), updateEntry.getValue());
                    }
                }

                updateData.add(updateRecord);
            }

            // 3. 执行upsert
            UpsertResp response = upsert(collectionName, updateData);
            log.info("字段更新成功，集合名称: {}, 更新记录数: {}", collectionName, updateData.size());
            return response != null;

        } catch (Exception e) {
            log.error("安全更新字段失败，集合名称: {}, 记录数量: {}", collectionName, ids.size(), e);
            return false;
        }
    }

    /**
     * 辅助方法：向JsonObject添加属性
     */
    private void addPropertyToJsonObject(JsonObject jsonObject, String fieldName, Object fieldValue) {
        if (fieldValue instanceof Number) {
            jsonObject.addProperty(fieldName, (Number) fieldValue);
        } else if (fieldValue instanceof String) {
            jsonObject.addProperty(fieldName, (String) fieldValue);
        } else if (fieldValue instanceof Boolean) {
            jsonObject.addProperty(fieldName, (Boolean) fieldValue);
        } else if (fieldValue instanceof Character) {
            jsonObject.addProperty(fieldName, (Character) fieldValue);
        } else {
            // 处理向量、数组或复杂对象
            jsonObject.add(fieldName, gson.toJsonTree(fieldValue));
        }
    }

    /**
     * 创建集合
     * @param collectionName 集合名称
     * @param dimension 向量维度
     * @param description 集合描述
     * @return 是否创建成功
     */
    public boolean createCollection(String collectionName, int dimension, String description) {
        log.info("开始创建Milvus集合，集合名称: {}, 向量维度: {}, 描述: {}",
                collectionName, dimension, description);
        try {

            CreateCollectionReq createCollectionReq = CreateCollectionReq.builder()
                    .collectionName(collectionName)
                    .dimension(dimension)
                    .description(description)
                    .build();

            client.createCollection(createCollectionReq);
            log.info("集合创建成功，集合名称: {}", collectionName);
            return true;
        } catch (Exception e) {
            log.error("创建集合失败，集合名称: {}", collectionName, e);
            return false;
        }
    }

    /**
     * 删除集合
     * @param collectionName 集合名称
     * @return 是否删除成功
     */
    public boolean dropCollection(String collectionName) {
        log.info("开始删除Milvus集合，集合名称: {}", collectionName);
        try {
            DropCollectionReq dropCollectionReq = DropCollectionReq.builder()
                    .collectionName(collectionName)
                    .build();

            client.dropCollection(dropCollectionReq);
            log.info("集合删除成功，集合名称: {}", collectionName);
            return true;
        } catch (Exception e) {
            log.error("删除集合失败，集合名称: {}", collectionName, e);
            return false;
        }
    }

    /**
     * 检查集合是否存在
     * @param collectionName 集合名称
     * @return 是否存在
     */
    public boolean hasCollection(String collectionName) {
        log.debug("检查集合是否存在，集合名称: {}", collectionName);
        try {
            HasCollectionReq hasCollectionReq = HasCollectionReq.builder()
                    .collectionName(collectionName)
                    .build();

            boolean exists = client.hasCollection(hasCollectionReq);
            log.debug("集合存在性检查结果，集合名称: {}, 存在: {}", collectionName, exists);
            return exists;
        } catch (Exception e) {
            log.error("检查集合存在性失败，集合名称: {}", collectionName, e);
            return false;
        }
    }

    /**
     * 创建索引
     * @param collectionName 集合名称
     * @param fieldName 字段名称
     * @param indexType 索引类型
     * @param metricType 度量类型
     * @return 是否创建成功
     */
    public boolean createIndex(String collectionName, String fieldName, IndexParam.IndexType indexType, IndexParam.MetricType metricType) {
        log.info("开始创建索引，集合名称: {}, 字段名称: {}, 索引类型: {}, 度量类型: {}",
                collectionName, fieldName, indexType, metricType);
        try {
            List<IndexParam> indexParams = new ArrayList<>();
            IndexParam indexParam = IndexParam.builder()
                    .fieldName(fieldName)
                    .indexType(indexType) // 字符串转枚举
                    .metricType(metricType)
                    .build();
            indexParams.add(indexParam);

            CreateIndexReq createIndexReq = CreateIndexReq.builder()
                    .collectionName(collectionName)
                    .indexParams(indexParams) // 使用 IndexParam 列表
                    .build();

            client.createIndex(createIndexReq);
            log.info("索引创建成功，集合名称: {}, 字段名称: {}", collectionName, fieldName);
            return true;
        } catch (Exception e) {
            log.error("创建索引失败，集合名称: {}, 字段名称: {}", collectionName, fieldName, e);
            return false;
        }
    }

    /**
     * 删除索引
     * @param collectionName 集合名称
     * @param fieldName 字段名称
     * @return 是否删除成功
     */
    public boolean dropIndex(String collectionName, String fieldName) {
        log.info("开始删除索引，集合名称: {}, 字段名称: {}", collectionName, fieldName);
        try {
            DropIndexReq dropIndexReq = DropIndexReq.builder()
                    .collectionName(collectionName)
                    .fieldName(fieldName)
                    .build();

            client.dropIndex(dropIndexReq);
            log.info("索引删除成功，集合名称: {}, 字段名称: {}", collectionName, fieldName);
            return true;
        } catch (Exception e) {
            log.error("删除索引失败，集合名称: {}, 字段名称: {}", collectionName, fieldName, e);
            return false;
        }
    }

    /**
     * 插入数据
     * @param collectionName 集合名称
     * @param data 要插入的数据
     * @return 插入响应
     */
    public InsertResp insert(String collectionName, List<JsonObject> data) {
        log.info("开始插入数据，集合名称: {}, 数据条数: {}", collectionName, data.size());
        try {
            InsertReq insertReq = InsertReq.builder()
                    .collectionName(collectionName)
                    .data(data)
                    .build();

            InsertResp response = client.insert(insertReq);
            log.info("数据插入成功，集合名称: {}, 插入条数: {}, 生成ID: {}",
                    collectionName, data.size(), response.getPrimaryKeys());
            return response;
        } catch (Exception e) {
            log.error("数据插入失败，集合名称: {}, 数据条数: {}", collectionName, data.size(), e);
            return null;
        }
    }

    /**
     * 批量插入向量数据
     * @param collectionName 集合名称
     * @param vectors 向量列表
     * @param ids ID列表
     * @return 插入响应
     */
    public InsertResp insertVectors(String collectionName, List<List<Float>> vectors, List<Long> ids) {
        log.info("开始批量插入向量数据，集合名称: {}, 向量条数: {}", collectionName, vectors.size());
        try {
            List<JsonObject> data = new ArrayList<>();
            for (int i = 0; i < vectors.size(); i++) {
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("id", ids.get(i));
                jsonObject.add("vector", gson.toJsonTree(vectors.get(i)));
                data.add(jsonObject);
            }

            InsertResp response = insert(collectionName, data);
            log.info("向量数据插入完成，集合名称: {}, 插入条数: {}", collectionName, vectors.size());
            return response;
        } catch (Exception e) {
            log.error("批量插入向量数据失败，集合名称: {}, 向量条数: {}", collectionName, vectors.size(), e);
            return null;
        }
    }

    /**
     * 根据ID查询数据
     * @param collectionName 集合名称
     * @param ids ID列表
     * @param outputFields 输出字段
     * @return 查询响应
     */
    public QueryResp queryById(String collectionName, List<Long> ids, List<String> outputFields) {
        log.info("开始根据ID查询数据，集合名称: {}, 查询ID数量: {}, 输出字段: {}",
                collectionName, ids.size(), outputFields);
        try {
            String expr = "id in [" + ids.stream().map(String::valueOf).collect(Collectors.joining(",")) + "]";
            log.debug("ID查询条件: {}", expr);

            QueryReq queryReq = QueryReq.builder()
                    .collectionName(collectionName)
                    .filter(expr)
                    .outputFields(outputFields)
                    .build();

            QueryResp response = client.query(queryReq);
            log.info("ID查询完成，集合名称: {}, 查询ID数量: {}, 返回结果数量: {}",
                    collectionName, ids.size(), response.getQueryResults().size());
            return response;
        } catch (Exception e) {
            log.error("根据ID查询数据失败，集合名称: {}, 查询ID数量: {}", collectionName, ids.size(), e);
            return null;
        }
    }

    /**
     * 条件查询
     * @param collectionName 集合名称
     * @param filter 过滤条件
     * @param outputFields 输出字段
     * @param limit 限制数量
     * @return 查询响应
     */
    public QueryResp query(String collectionName, String filter, List<String> outputFields, long limit, long offset) {
        log.info("开始条件查询，集合名称: {}, 过滤条件: {}, 输出字段: {}, 限制数量: {}",
                collectionName, filter, outputFields, limit);
        try {
            QueryReq queryReq = QueryReq.builder()
                    .collectionName(collectionName)
                    .filter(filter)
                    .outputFields(outputFields)
                    .offset(offset)
                    .limit(limit)
                    .build();

            QueryResp response = client.query(queryReq);
            log.info("条件查询完成，集合名称: {}, 返回结果数量: {}", collectionName, response.getQueryResults().size());
            return response;
        } catch (Exception e) {
            log.error("条件查询失败，集合名称: {}, 过滤条件: {}", collectionName, filter, e);
            return null;
        }
    }

    /**
     * 向量相似性搜索
     * @param collectionName 集合名称
     * @param vectors 查询向量
     * @param limit 返回最相似的K个结果
     * @param outputFields 输出字段
     * @param filter 过滤条件
     * @return 搜索响应
     */
    public SearchResp search(String collectionName,
                             List<List<Float>> vectors,
                             int limit, // limit
                             List<String> outputFields,
                             String filter,
                             String annsField, // 向量字段名作为参数传入
                             IndexParam.MetricType metricType, // 显式指定度量类型
                             Map<String, Object> searchParams,
                             String groupBy,
                             Integer groupSize
                             ) { // 搜索参数
        log.info("开始向量相似性搜索，集合名称: {}, 向量数量: {}, limit: {}, 过滤条件: {}, 向量字段: {}, 度量类型: {}",
                collectionName, vectors.size(), limit, filter, annsField, metricType);
        try {
            // 1. 将List<List<Float>>转换为List<BaseVector>（这里使用Float16Vec为例）
            List<BaseVector> baseVectors = vectors.stream()
                    .map(FloatVec::new) // 利用Float16Vec的构造函数转换
                    .collect(Collectors.toList());

            // 2. 构建SearchReq，使用最新API规范
            SearchReq.SearchReqBuilder<?, ?> builder = SearchReq.builder()
                    .collectionName(collectionName)
                    .data(baseVectors) // 传入转换后的BaseVector列表
                    .annsField(annsField) // 动态指定向量字段名
                    .metricType(metricType) // 显式设置度量类型
                    .limit(limit) // 使用limit替代topK
                    .outputFields(outputFields)
                    .filter(filter)
                    .searchParams(searchParams != null ? searchParams : new HashMap<>());

            // 3. 如果指定了分组字段，则添加分组条件
            if (StringUtils.hasLength(groupBy)) {
                // 设置分组字段
                builder.groupByFieldName(groupBy);
                // 设置每组返回的结果数量，默认取limit的值
                builder.groupSize(groupSize != null ? groupSize : limit);
            }

            SearchResp response = client.search(builder.build());
            log.info("向量搜索完成，集合名称: {}, 返回结果组数: {}", collectionName, response.getSearchResults().size());
            return response;
        } catch (Exception e) {
            log.error("向量相似性搜索失败，集合名称: {}, 向量数量: {}", collectionName, vectors.size(), e);
            return null;
        }
    }


    /**
     * 根据ID删除数据
     * @param collectionName 集合名称
     * @param ids 要删除的ID列表
     * @return 是否删除成功
     */
    public boolean deleteById(String collectionName, List<Long> ids) {
        log.info("开始根据ID删除数据，集合名称: {}, 待删除ID数量: {}", collectionName, ids.size());
        try {
            String expr = "id in [" + ids.stream().map(String::valueOf).collect(Collectors.joining(",")) + "]";
            log.debug("ID删除条件: {}", expr);

            DeleteReq deleteReq = DeleteReq.builder()
                    .collectionName(collectionName)
                    .filter(expr)
                    .build();

            client.delete(deleteReq);
            log.info("根据ID删除数据成功，集合名称: {}, 删除ID数量: {}", collectionName, ids.size());
            return true;
        } catch (Exception e) {
            log.error("根据ID删除数据失败，集合名称: {}, 删除ID数量: {}", collectionName, ids.size(), e);
            return false;
        }
    }

    /**
     * 根据条件删除数据
     * @param collectionName 集合名称
     * @param filter 删除条件
     * @return 是否删除成功
     */
    public boolean deleteByFilter(String collectionName, String filter) {
        log.info("开始根据条件删除数据，集合名称: {}, 删除条件: {}", collectionName, filter);
        try {
            DeleteReq deleteReq = DeleteReq.builder()
                    .collectionName(collectionName)
                    .filter(filter)
                    .build();

            client.delete(deleteReq);
            log.info("根据条件删除数据成功，集合名称: {}, 删除条件: {}", collectionName, filter);
            return true;
        } catch (Exception e) {
            log.error("根据条件删除数据失败，集合名称: {}, 删除条件: {}", collectionName, filter, e);
            return false;
        }
    }
}
