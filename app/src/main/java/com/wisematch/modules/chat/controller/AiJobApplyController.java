package com.wisematch.modules.chat.controller;

import com.wisematch.common.annotation.SysLog;
import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.modules.chat.service.AiJobApplyService;
import com.wisematch.modules.form.model.fullTimeJobApply.FullTimeJobApplyInsertDTO;
import com.wisematch.modules.form.model.partTimeJobApply.PartTimeJobApplyInsertDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/user")
@Tag(name = "兼职和全职工作申请", description = "兼职和全职工作申请")
@Slf4j
@Deprecated
public class AiJobApplyController {

    @Autowired
    AiJobApplyService aiJobApplyService;

    @PostMapping("/fullTimeJobApply/submit")
    @Operation(summary = "全职工作申请")
    @SysLog("全职工作申请")
    @Deprecated
    public R update(@RequestBody FullTimeJobApplyInsertDTO dto) {
        dto.setSalaryRequirements(dto.getSalaryRequirementsStart()+"-"+dto.getSalaryRequirementsEnd());
        aiJobApplyService.insertObj(dto,dto.getRoomId());
        return R.ok();
    }

    @PostMapping("/partTimeJobApply/submit")
    @Operation(summary = "兼职工作申请")
    @SysLog("兼职工作申请")
    @NotDoubleSubmit
    public R partTimeJobApply(@RequestBody PartTimeJobApplyInsertDTO dto) {
        aiJobApplyService.insertObj(dto,dto.getRoomId());
        return R.ok();
    }

}
