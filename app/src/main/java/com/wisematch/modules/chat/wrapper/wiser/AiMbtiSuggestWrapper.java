package com.wisematch.modules.chat.wrapper.wiser;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.wisematch.common.utils.BeanCopyUtils;
import com.wisematch.common.utils.MybatisUtils;
import com.wisematch.modules.chat.entity.AiChatWiser;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.service.AiChatWiserService;
import com.wisematch.modules.chat.service.AiUserResumeService;
import com.wisematch.modules.chat.wrapper.AbstractWiseMatchWrapper;
import com.wisematch.modules.common.utils.JsonUtils;
import com.wisematch.modules.exam.entity.AiExamBank;
import com.wisematch.modules.exam.service.AiAnswerQuestionService;
import com.wisematch.modules.exam.service.AiExamBankService;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 岗位推荐
 */
@Component
@Data
@Slf4j
public class AiMbtiSuggestWrapper extends AbstractWiseMatchWrapper {

    @Autowired
    AiExamBankService aiExamBankService;

    @Resource
    private AiUserResumeService aiUserResumeService;
    @Resource
    private AiChatWiserService chatWiserService;

    @Resource
    private AiAnswerQuestionService aiAnswerQuestionService;

    @Override
    public String promptWrap(AgentHandlerContext context, String prompt) {
        if (prompt.contains("$jobDocuments")) {
            //知识库向量检索
            // 使用ArrayList确保可修改性，收集用户消息
            List<Message> messages = new java.util.ArrayList<>(context.getMessages().stream()
                    .filter(item -> MessageType.USER.equals(item.getMessageType())).toList());

            // 添加当前用户消息
            String currentMsg = context.getAiChatUserMsg().getMsg();
            messages.add(new UserMessage(currentMsg));
            List<CardInfo> cardInfos = aiExamBankService.allBank();
            if (!cardInfos.isEmpty()) {
                prompt = prompt.replace("$jobDocuments", JsonUtils.toJson(cardInfos));
            } else {
                prompt = prompt.replace("$jobDocuments", "[]");
            }
        }
        if (prompt.contains("$resume")) {
            String resume = aiUserResumeService.getCurrentOpened(context.getAiChatUserMsg().getUserId());
            prompt = prompt.replace("$resume", resume);
        }
        return super.promptWrap(context, prompt);
    }

    @Override
    public ChatMessage replyWrap(ChatMessage chatMessage) {

        log.info("进入replyWrap方法："+JsonUtils.toJson(chatMessage));

        String chatId = chatMessage.getChatId();
        AiChatWiser aiChatWiser = chatWiserService.getByConservationId(chatId);

        if (!JSONUtil.isTypeJSON(chatMessage.getMsg())) {
            return super.replyWrap(chatMessage);
        }
        JSONObject replys = JSONObject.parseObject(chatMessage.getMsg());
        try {
            if (replys.get("recommendMbtiId") != null) {
                List<String> rcmdId = (List<String>) replys.get("recommendMbtiId");
                log.info("推荐的mbti_id：", rcmdId);
                if (null != rcmdId && !rcmdId.isEmpty()) {
                    List<AiExamBank> aiJobTrains = MybatisUtils.listByIds(this.aiExamBankService, rcmdId);
                    List<CardInfo> cardInfos = BeanCopyUtils.copyList(aiJobTrains, CardInfo::examBankToCardInfo);
                    cardInfos = aiAnswerQuestionService.cardWrapper(cardInfos, aiChatWiser.getUserId());
                    chatMessage.setCardInfos(cardInfos);
                    return super.replyWrap(chatMessage);
                }
            }
        } catch (Exception e) {
            log.error("性格测试试题推荐异常", e);
        }
        return super.replyWrap(chatMessage);
    }
}
