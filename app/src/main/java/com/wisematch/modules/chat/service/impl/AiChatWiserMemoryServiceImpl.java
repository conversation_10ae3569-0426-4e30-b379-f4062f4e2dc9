package com.wisematch.modules.chat.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.modules.chat.entity.AiChatWiserMemory;
import com.wisematch.modules.chat.enums.MsgRole;
import com.wisematch.modules.chat.mapper.AiChatWiserMemoryMapper;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.model.UserPreferenceVO;
import com.wisematch.modules.chat.service.AiChatWiserMemoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 智能体池
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiChatWiserMemoryServiceImpl extends ServiceImpl<AiChatWiserMemoryMapper, AiChatWiserMemory> implements AiChatWiserMemoryService {

    @Override
    public AiChatWiserMemory addUserMemory(String conversationId,ChatMessage chatMessage) {
        AiChatWiserMemory aiChatMemory = new AiChatWiserMemory();
        aiChatMemory.setType(MsgRole.user.name());
        aiChatMemory.setContent(JSONObject.toJSONString(chatMessage));
        aiChatMemory.setMsg(chatMessage.getMsg());
        aiChatMemory.setTimestamp(new Date());
        aiChatMemory.setConversationId(conversationId);
        this.baseMapper.insert(aiChatMemory);
        return aiChatMemory;
    }

    @Override
    public AiChatWiserMemory addAssistantMemory(String conversationId,ChatMessage chatMessage) {
        AiChatWiserMemory aiChatMemory = new AiChatWiserMemory();
        aiChatMemory.setType(MsgRole.assistant.name());
        aiChatMemory.setContent(JSONObject.toJSONString(chatMessage));
        aiChatMemory.setMsg(chatMessage.getMsg());
        aiChatMemory.setTimestamp(new Date());
        aiChatMemory.setConversationId(conversationId);
        this.baseMapper.insert(aiChatMemory);
        return aiChatMemory;
    }

    @Override
    public AiChatWiserMemory updateAssistantMemory(String id,ChatMessage chatMessage,String msg) {
        AiChatWiserMemory aiChatWiserMemory = this.getById(id);
        aiChatWiserMemory.setContent(JSONObject.toJSONString(chatMessage));
        aiChatWiserMemory.setMsg(msg);
        this.baseMapper.updateById(aiChatWiserMemory);
        return aiChatWiserMemory;
    }

    @Override
    public AiChatWiserMemory updateAssistantMsg(String id, String msg) {
        AiChatWiserMemory aiChatWiserMemory = this.getById(id);
        aiChatWiserMemory.setMsg(msg);
        this.baseMapper.updateById(aiChatWiserMemory);
        return aiChatWiserMemory;
    }

    @Override
    public void updateUserPreferences(UserPreferenceVO userPreferenceVO) {
        AiChatWiserMemory aiChatWiserMemory = this.getById(userPreferenceVO.getTargetId());
        aiChatWiserMemory.setCollect(userPreferenceVO.getCollect());
        aiChatWiserMemory.setThumbsUp(userPreferenceVO.getThumbsUp());
        aiChatWiserMemory.setExtra(userPreferenceVO.getExtra());
        this.baseMapper.updateById(aiChatWiserMemory);
    }

    @Override
    public void addAssistantMemory(String conversationId, List<ChatMessage> messages) {
        if (messages != null && !messages.isEmpty()) {
            for (int i = 0; i < messages.size(); i++) {
                ChatMessage msg = messages.get(i);
                AiChatWiserMemory aiChatWiserMemory = this.addAssistantMemory(conversationId, msg);
                msg.setId(String.valueOf(aiChatWiserMemory.getId()));
                messages.set(i,msg);
            }
        }
    }


    @Override
    public List<AiChatWiserMemory> getList(String conversationId) {
        QueryWrapper<AiChatWiserMemory> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiChatWiserMemory::getConversationId, conversationId).orderByAsc(AiChatWiserMemory::getId);
        List<AiChatWiserMemory> list = this.baseMapper.selectList(queryWrapper);
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<org.springframework.ai.chat.messages.Message> getListAsMessages(String chatId) {
        List<AiChatWiserMemory> history = this.getList(chatId);
        List<org.springframework.ai.chat.messages.Message> messages = new ArrayList<>();
        if (history != null) {
            messages = history.stream().map(x -> {
                JSONObject jsonObject = JSONObject.parseObject(x.getContent());
                String content = jsonObject.getString("msg");
                if (jsonObject.get("cardInfos") != null) {
                    content = content + " ### cardInfos:" + jsonObject.get("cardInfos");
                }
                if (x.getType().equals(MessageType.ASSISTANT.getValue())) {
                    return new AssistantMessage(content);
                } else {
                    return new UserMessage(content);
                }
            }).collect(Collectors.toList());
        }
        return messages;
    }

}
