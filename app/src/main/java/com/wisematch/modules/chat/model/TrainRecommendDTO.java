package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class TrainRecommendDTO {

    @Schema(description = "用户简历")
    String resume;

    @Schema(description = "interviewID")
    String interviewID;

    @Schema(description = "聊天记录")
    List<History> history ;

    @Schema(description = "用户输入")
    String userInput;

}
