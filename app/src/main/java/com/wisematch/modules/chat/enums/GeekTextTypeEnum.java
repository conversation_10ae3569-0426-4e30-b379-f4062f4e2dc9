package com.wisematch.modules.chat.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GeekTextTypeEnum {

    // 简历主档案相关
    RESUME_BRIEF_COMMENTS("resume_brief_comments", "简历简介-备注"),
    RESUME_BRIEF_SKILLS("resume_brief_skills", "简历简介-技能"),
    RESUME_EXTRA_INFO("resume_extra_info", "简历附加信息"),
    RESUME_WORK_EXPERIENCE("resume_work_experience", "简历工作经验"),

    USER_FORM_JOB_STATUS("user_form_comments_job_status", "意向表单-求职状态（离职随时到岗）"),
    USER_FORM_INDUSTRY_MATCH("user_form_comments_industry_match", "意向表单-行业适配（美容行业）"),
    USER_FORM_POSITION_MATCH("user_form_comments_position_match", "意向表单-职位适配（京东运营）"),
    USER_FORM_REGION_INTENT("user_form_comments_region_intent", "意向表单-地域意向（专注阿拉善盟本地求职）"),
    USER_FORM_WORK_SITE("user_form_comments_work_site", "意向表单-现工作地点"),

    // 教育经历相关
    EDU_NAME("edu_name", "教育经历-学校名称"),
    EDU_LABEL("edu_label", "教育经历-学位/专业标签"),
    EDU_CONTENT("edu_content", "教育经历-详细内容（分chunk）"),
    // 工作经历相关
    WORK_NAME("work_name", "工作经历-公司名称"),
    WORK_LABEL("work_label", "工作经历-职位标签"),
    WORK_CONTENT("work_content", "工作经历-详细内容（分chunk）"),
    // 项目经历相关
    PROJECT_NAME("project_name", "项目经历-项目名称"),
    PROJECT_LABEL("project_label", "项目经历-角色标签"),
    PROJECT_CONTENT("project_content", "项目经历-详细内容（分chunk）"),
    // 校园经历相关
    SCHOOL_NAME("school_name", "校园经历-组织名称"),
    SCHOOL_LABEL("school_label", "校园经历-职务标签"),
    SCHOOL_CONTENT("school_content", "校园经历-详细内容（分chunk）"),
    // 人才画像相关
    PORTRAIT_COMMENTS("portrait_comments", "人才画像-综合评价"),
    PORTRAIT_EVALUATIONS("portrait_evaluations", "人才画像-评估内容"),
    PORTRAIT_HIGHLIGHTS("portrait_highlights", "人才画像-亮点总结"),
    PORTRAIT_DIMENSION_ADVANTAGES("portrait_dimension_advantages", "人才画像维度-优势分析"),
    PORTRAIT_DIMENSION_DISADVANTAGES("portrait_dimension_disadvantages", "人才画像维度-劣势分析"),
    // 报告相关
    REPORT_COMMENTS("report_comments", "报告-评估评语"),
    REPORT_SUMMARY("report_summary", "报告-维度总结");

    /**
     * 文本类型标识（存储到Milvus的text_type字段）
     */
    private final String type;

    /**
     * 类型描述
     */
    private final String description;
}
