package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiChatMatcherMemory;
import com.wisematch.modules.chat.model.AiChatUserMsg;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.model.UserPreferenceVO;
import org.springframework.ai.chat.messages.Message;

import java.util.List;


/**
 * AI Matcher智能体服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */
public interface AiChatMatcherMemoryService extends IService<AiChatMatcherMemory>{

    void add(String conversationId, AiChatMatcherMemory aiChatMatcherMemory);

    void addUserMemory(String conversationId, AiChatUserMsg message);

    AiChatMatcherMemory addAssistantMemory(String conversationId, String message);

    AiChatMatcherMemory updateAssistantMemory(String id, ChatMessage chatMessage, String message);

    void addAssistantMemory(String conversationId, List<ChatMessage> messages);

    void updateUserPreferences(UserPreferenceVO userPreferenceVO);

    List<AiChatMatcherMemory> getList(String conversationId);

    List<Message> getListAsMessages(String chatId);


}
