package com.wisematch.modules.chat.model;

import com.wisematch.common.model.PageRequest;
import com.wisematch.modules.chat.entity.AiJobExamine;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AiJobExamineDTO extends AiJobExamine implements PageRequest {

    @Schema(description = "面试名称")
    private String name;

    @Schema(description = "配置信息（岗位胜任力模型）")
    private String content;

    @Schema(description = "用例说明")
    private String usecase;

    @Schema(description = "问题知识库")
    private String questions;

    @Schema(description = "起始页")
    private Integer pageNum = 1 ;

    @Schema(description = "页面大小")
    private Integer pageSize = 10;

}
