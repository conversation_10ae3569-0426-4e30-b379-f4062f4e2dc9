package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.common.cache.GuavaCache;
import com.wisematch.modules.chat.entity.AiAgentPool;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * AI智能体服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */
public interface AiAgentPoolService extends IService<AiAgentPool> {

    String getPromptById(String id);

    default AiAgentPool getByCode(String agentCode) {
        QueryWrapper<AiAgentPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiAgentPool::getAgentCode,agentCode);
        return this.getOne(queryWrapper);
    }
}
