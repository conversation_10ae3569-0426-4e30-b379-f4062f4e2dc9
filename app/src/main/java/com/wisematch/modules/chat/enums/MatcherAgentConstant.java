package com.wisematch.modules.chat.enums;

/**
 * <AUTHOR>
 * @version AgentConstant.java, v0.1 2025-07-08 19:18
 */
public class MatcherAgentConstant {

    /**
     * 默认Matcher
     */
    public static final String DEFAULT_MATCHER = "MATCHER_MISC_RESPONSE";

    /**
     * 默认matcher意图识别agnet
     */
    public static final String MATCHER_INTENTION_AGENT_CODE = "MATCHER_INTENTION_RECOGNITION";
    /**
     * 人才推荐
     */
    @Deprecated
    public static final String GEEK_CHAT_RECOMMEND_PARSE = "GEEK_CHAT_RECOMMEND_PARSE";

    /**
     * 招聘者对牛人需求解析-工作经历
     */
    public static final String GEEK_CHAT_RECOMMEND_WORK_PARSE = "GEEK_CHAT_RECOMMEND_WORK_PARSE";

    /**
     * 招聘者对牛人需求解析-教育经历
     */
    public static final String GEEK_CHAT_RECOMMEND_EDU_PARSE = "GEEK_CHAT_RECOMMEND_EDU_PARSE";

    /**
     * 招聘者对牛人需求解析-项目经历
     */
    public static final String GEEK_CHAT_RECOMMEND_PROJ_PARSE = "GEEK_CHAT_RECOMMEND_PROJ_PARSE";

    /**
     * 招聘者对牛人需求解析-校园经历
     */
    public static final String GEEK_CHAT_RECOMMEND_SCHOOL_PARSE = "GEEK_CHAT_RECOMMEND_SCHOOL_PARSE";

    /**
     * 招聘者对牛人需求解析-简历主档案
     */
    public static final String GEEK_CHAT_RECOMMEND_RESUME_PARSE = "GEEK_CHAT_RECOMMEND_RESUME_PARSE";

    /**
     * 招聘者对牛人需求解析-面试报告
     */
    public static final String GEEK_CHAT_RECOMMEND_REPORT_PARSE = "GEEK_CHAT_RECOMMEND_REPORT_PARSE_V2";

    /**
     * 招聘者对牛人需求解析-人才画像维度
     */
    public static final String GEEK_CHAT_RECOMMEND_PORTRAIT_DIMENSION_PARSE = "GEEK_CHAT_RECOMMEND_PORTRAIT_DIMENSION_PARSE_V2";

    /**
     * 招聘者对牛人需求解析-人才画像评价
     */
    public static final String GEEK_CHAT_RECOMMEND_PORTRAIT_EVALUATION_PARSE = "GEEK_CHAT_RECOMMEND_PORTRAIT_EVALUATION_PARSE";

}
