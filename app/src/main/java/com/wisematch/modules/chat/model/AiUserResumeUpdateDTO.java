package com.wisematch.modules.chat.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AiUserResumeUpdateDTO {


    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "在线简历")
    private String resumeJson;

    @Schema(description = "附件简历")
    private String resumeUrl;

    @Schema(description = "简历文本")
    private String resumeText;

    @Schema(description = "文件名称")
    private String fileName;
}
