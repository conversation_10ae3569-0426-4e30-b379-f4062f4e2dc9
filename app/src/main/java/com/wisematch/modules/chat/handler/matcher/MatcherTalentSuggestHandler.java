package com.wisematch.modules.chat.handler.matcher;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wisematch.modules.chat.agent.AgentContext;
import com.wisematch.modules.chat.agent.AgentFacade;
import com.wisematch.modules.chat.entity.AiChatMatcherLog;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.handler.IAgentHandler;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.service.AiChatMatcherLogService;
import com.wisematch.modules.chat.service.AiViewPortraitService;
import com.wisematch.modules.chat.wrapper.IWiseMatchWrapper;
import com.wisematch.modules.chat.wrapper.MatcherWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.*;

/**
 * 人才匹配
 * <AUTHOR>
 * @version ResumeCheckHandler.java, v0.1 2025-07-15 16:49
 */
@Component
@Slf4j
public class MatcherTalentSuggestHandler implements IAgentHandler {

    IAgentHandler nextHandler;
    @Autowired
    AgentFacade agentFacade;
    @Autowired
    AiViewPortraitService aiViewPortraitService;
    @Autowired
    MatcherWrapper matcherWrapper;
    @Autowired
    AiChatMatcherLogService aiChatMatcherLogService;
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public void setNext(IAgentHandler iAgentHandler) {
        this.nextHandler = iAgentHandler;
    }

    @Override
    public Flux<ChatMessage> handle(AgentHandlerContext context) {

        AgentContext agentContext = new AgentContext();
        agentContext.setUserMsg(context.getAiChatUserMsg().getMsg());
        agentContext.setAgentCode(context.getIntentionVO().getAgentCode());

        List<Message> messages = Optional.ofNullable(context.getMessages()).orElseGet(Collections::emptyList).stream()
                .filter(item -> MessageType.USER.equals(item.getMessageType()))
                .toList();
        agentContext.setHistory(messages);

        log.info("matcher agent reply start. chatId:{}, agentCode:{}", context.getAiChatUserMsg().getChatId(), agentContext.getAgentCode());
        String prompt = agentFacade.getPrompt(agentContext.getAgentCode());
        prompt = prompt.replace("$history", JSONObject.toJSONString(messages));
        prompt = prompt.replace("$userInput",JSONObject.toJSONString(context.getAiChatUserMsg()));
        prompt = prompt.replace("$code", context.getIntentionVO().getCode());

        IWiseMatchWrapper wrapper = matcherWrapper.wrap(agentContext.getAgentCode());
        agentContext.setPrompt(wrapper.promptWrap(context, prompt));

        Flux<String> reply = agentFacade.supplyStream(agentContext).map(x -> {
            x = x.replaceAll(" ", "").replaceAll("\n\n", "\n").replaceAll("\n\n\n", "\n");
            context.getReply().append(x);
            return x;
        }).doFinally(x -> {
            try {
                AiChatMatcherLog matcherLog = new AiChatMatcherLog();
                matcherLog.setSysMsg(context.getReply().toString());
                matcherLog.setUserMsg(context.getAiChatUserMsg().getMsg());
                matcherLog.setConversationId(context.getAiChatUserMsg().getChatId());
                matcherLog.setAgentId(agentContext.getAiAgentPool() == null ? "" : agentContext.getAiAgentPool().getAgentCode());
                matcherLog.setAgentName(agentContext.getAiAgentPool() == null ? "" : agentContext.getAiAgentPool().getAgentName());
                matcherLog.setPrompt(agentContext.getPrompt());
                if (context.getIntentionVO() != null) {
                    matcherLog.setIntention(context.getIntentionVO().getMsg());
                    matcherLog.setIntentionCode(context.getIntentionVO().getCode());
                    matcherLog.setIntentionName(context.getIntentionVO().getName());
                }
                aiChatMatcherLogService.supplyAsync(matcherLog);
            }catch (Exception e){
                log.error("Matcher日志异常", e);
            }
        });
        return wrapper.format(context.getAiChatUserMsg().getChatId(), reply);
    }
}
