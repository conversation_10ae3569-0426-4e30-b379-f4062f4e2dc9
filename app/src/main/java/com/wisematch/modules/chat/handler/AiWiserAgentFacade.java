package com.wisematch.modules.chat.handler;

import com.wisematch.modules.chat.handler.wiser.WiserIntentionHandler;
import com.wisematch.modules.chat.handler.wiser.WiserReplyAgentHandler;
import com.wisematch.modules.chat.handler.wiser.WiserResumeCheckHandler;
import com.wisematch.modules.chat.model.AiChatUserMsg;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.service.AiChatWiserLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

/**
 * Wiser Agent 聊天门面
 * <AUTHOR>
 * @version IntentionAgentFacade.java, v0.1 2025-07-11 11:02
 */
@Component
@Slf4j
public class AiWiserAgentFacade implements InitializingBean {

    @Autowired
    private WiserResumeCheckHandler wiserResumeCheckHandler;

    @Autowired
    private WiserIntentionHandler wiserIntentionHandler;

    @Autowired
    private WiserReplyAgentHandler wiserReplyAgentHandler;

    public Flux<ChatMessage> stream(AgentHandlerContext agentHandlerContext){
        log.info("wiser reply start. chatId:{}", agentHandlerContext.getAiChatUserMsg().getChatId());
        return wiserResumeCheckHandler.handle(agentHandlerContext);
    }

    @Override
    public void afterPropertiesSet(){
        wiserResumeCheckHandler.setNext(wiserIntentionHandler);
        wiserIntentionHandler.setNext(wiserReplyAgentHandler);
    }
}
