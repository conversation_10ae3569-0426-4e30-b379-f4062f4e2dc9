package com.wisematch.modules.chat.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ai_job_train")
@Schema(description = "发布的模拟面试")
public class AiJobTrain {

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    private String position;

    @Schema(description = "考核维度ID")
    private String jobExamineId;

    @Schema(description = "面试官ID")
    private String jobViewerId;

    @Schema(description = "展示图片")
    private String logo;

    @Schema(description = "面试摘要")
    private String summary;

    @Schema(description = "关键标签")
    private String keyLabel;

    @Schema(description = "面试标签")
    private String labels;

    @Schema(description = "面试类型")
    private Integer type;

    @Schema(description = "面试信息")
    private String content;

    @Schema(description = "发布公司")
    private String company;

    @Schema(description = "智能体ID")
    private String agentId;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Schema(description = "面试时长（秒）")
    private Integer duration;

    @Schema(description = "是否删除")
    private Integer isDel;

    @Schema(description = "面试难度")
    private String level;

    @Schema(description = "面试状态（0停用，1已发布）")
    private Integer status;

    @Schema(description = "0未审核，1:审核通过，2审核拒绝")
    private Integer isVerify;

    @Schema(description = "开场白")
    private String welcome;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "是否热门")
    private Integer isHot;

    @Schema(description = "是否")
    private Integer isNewest;
}
