package com.wisematch.modules.chat.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GeekIntent {

    private WorkExperience workExperience;
    private EduExperience eduExperience;
    private ProjectExperience projectExperience;
    private SchoolExperience schoolExperience;
    private Profile profile;
    private List<InterviewReport> interviewReport;
    private List<TalentPortraitDimension> talentPortraitDimension;
    private TalentPortraitEvaluation talentPortraitEvaluation;

    @Data
    public static class WorkExperience {
        private List<String> companyName;
        private List<String> position;
        private List<String> desc;
    }

    @Data
    public static class EduExperience {
        private List<String> schoolName;
        private List<String> major;
        private List<String> degree;
        private List<String> desc;
    }

    @Data
    public static class ProjectExperience {
        private List<String> projectName;
        private List<String> projectRole;
        private List<String> desc;
    }

    @Data
    public static class SchoolExperience {
        private List<String> organizationName;
        private List<String> position;
        private List<String> desc;
    }

    @Data
    public static class Profile {
        private List<String> highestEducation;
        private List<Integer> sex;
        private List<Salary> salary;
        private List<String> workWay;
        private List<String> workExperience;
        private List<String> skills;
        private List<String> additionalInfo;

        @Data
        public static class Salary {
            private String min;
            private String max;
        }
    }

    @Data
    public static class InterviewReport {
        private String dimensions;
        private List<String> checkPoint;
        private List<String> content;
    }

    @Data
    public static class TalentPortraitDimension {
        private String dimensionName;
        private List<String> advantages;
    }

    @Data
    public static class TalentPortraitEvaluation {
        private String dimensionName;
        private List<String> summary;
        private List<String> highlights;
    }
}
