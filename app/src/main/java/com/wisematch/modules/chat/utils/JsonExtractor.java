package com.wisematch.modules.chat.utils;

import java.util.regex.*;


public class JsonExtractor {

    public static String extractJSON(String text) {
        int start = text.indexOf('{');
        if (start == -1) {
            return "";
        }

        int count = 1;
        int end = start + 1;
        while (end < text.length() && count > 0) {
            char c = text.charAt(end);
            if (c == '{') {
                count++;
            } else if (c == '}') {
                count--;
            }
            end++;
        }
        return count == 0 ? text.substring(start, end) : "";
    }

    public static String extractJSONArray(String text) {
        int start = text.indexOf('[');
        if (start == -1) {
            return "";
        }

        int count = 1;
        int end = start + 1;
        while (end < text.length() && count > 0) {
            char c = text.charAt(end);
            if (c == '[') {
                count++;
            } else if (c == ']') {
                count--;
            }
            end++;
        }
        return count == 0 ? text.substring(start, end) : "";
    }

}
