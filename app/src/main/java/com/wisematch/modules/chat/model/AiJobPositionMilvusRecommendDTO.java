package com.wisematch.modules.chat.model;

import lombok.Data;
import java.util.List;

/**
 * Milvus职位向量数据VO，与集合字段结构一一对应
 */
@Data
public class AiJobPositionMilvusRecommendDTO {

    /**
     * 职位ID
     * 对应Milvus VarChar类型，最大长度50
     */
    private String positionId;

    /**
     * 公司ID
     * 对应Milvus VarChar类型，最大长度50
     */
    private String orgId;

    /**
     * 原始文本内容
     * 对应Milvus VarChar类型，最大长度2000
     */
    private String textContent;

    /**
     * 文本类型：title/benefits/skills/summary/content/company/location
     * 对应Milvus VarChar类型，最大长度50
     */
    private String textType;

    /**
     * 招聘类型枚举：社招全职，实习生...
     * 对应Milvus Int32类型
     */
    private Integer recruitmentType;

    /**
     * 结算方式枚举：每月/每日/每小时/每年
     */
    private Integer settlementMethod;

    /**
     * 薪酬范围最小值
     * 对应Milvus Int32类型
     */
    private Integer salaryMin;

    /**
     * 薪酬范围最大值
     * 对应Milvus Int32类型
     */
    private Integer salaryMax;

    /**
     * 多少薪
     * 对应Milvus Int32类型
     */
    private Integer salaryMonth;

    /**
     * 工作经验枚举
     * 对应Milvus Int32类型
     */
    private Integer workExperience;

    /**
     * 学历要求枚举
     * 对应Milvus Int32类型
     */
    private Integer education;

    /**
     * 审核状态
     * 对应Milvus Int32类型
     */
    private Integer auditStatus;

    /**
     * 删除状态
     * 对应Milvus Int32类型
     */
    private Integer delStatus;

    /**
     * 职位状态
     * 对应Milvus Int32类型
     */
    private Integer positionStatus;

    /**
     * 文本向量，768维（未归一化，语义检索用）
     * 对应Milvus FloatVector类型，维度768
     */
    private List<Double> embedding;
}