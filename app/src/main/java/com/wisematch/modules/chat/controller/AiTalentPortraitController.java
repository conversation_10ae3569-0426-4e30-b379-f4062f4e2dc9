package com.wisematch.modules.chat.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.wisematch.common.annotation.SysLog;
import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.model.CandidatesDTO;
import com.wisematch.modules.chat.model.PortraitBrief;
import com.wisematch.modules.chat.model.TalentCenterPageDTO;
import com.wisematch.modules.chat.model.TalentPortrait;
import com.wisematch.modules.chat.service.AiOrganizationService;
import com.wisematch.modules.chat.service.AiViewPortraitService;
import com.wisematch.modules.chat.service.AiViewRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version AiMatcherController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/portrait")
@Tag(name = "人才市场", description = "人才市场")
public class AiTalentPortraitController {

    @Autowired
    AiOrganizationService aiOrganizationService;
    @Autowired
    private AiViewPortraitService aiViewPortraitService;
    @Autowired
    private AiViewRecordService aiViewRecordService;

    @Operation(summary = "生成人才画像")
    @GetMapping("/generate")
    @SysLog("生成人才画像")
    public R generate(@RequestParam("roomId") String roomId) {
        return R.ok().setData(aiViewPortraitService.generatePortrait(roomId));
    }


    @GetMapping("/getPosition/{id}")
    @Operation(summary = "获取单条岗位信息")
    public R getById(@PathVariable String id) {
        return R.ok().setData(aiViewPortraitService.getPositionByPortrait(id));
    }

    @Operation(summary = "（matcher）获取单个人才画像")
    @GetMapping("/detail")
    public R talentPortrait( @RequestParam("id") String id) {
        aiOrganizationService.examCertification(UserInfoUtils.getCurrentUserId());
        return R.ok().setData(aiViewPortraitService.getPortraitById(id));
    }

    @Operation(summary = "（wiser）获取单个人才画像")
    @GetMapping("/info")
    public R talentPortraitInfo( @RequestParam("id") String id) {
        TalentPortrait talentPortrait = aiViewPortraitService.getPortraitById(id);
        PortraitBrief brief = talentPortrait.getBrief();
        if(Objects.nonNull(brief) && StringUtils.isNotBlank(brief.getUserId())){
            if(UserInfoUtils.getCurrentUserId().equals(brief.getUserId())){
                return R.ok().setData(talentPortrait);
            }
        }
        return R.error();
    }

    @Operation(summary = "人才池")
    @GetMapping("/list")
    @Deprecated
    public R talentPortraitList(@RequestParam(value = "search", defaultValue = "") String search) {
        return R.ok().setData(aiViewPortraitService.talentCenter(search));
    }

    @Operation(summary = "人才池")
    @GetMapping("/page")
    @NotDoubleSubmit
    public R talentPortraitPage(TalentCenterPageDTO dto) {
        return R.ok().setData(aiViewPortraitService.talentCenterPage(dto));
    }

    @Operation(summary = "候选人列表")
    @GetMapping("/candidate/list")
    @Deprecated
    public R candidates(CandidatesDTO candidatesDTO) {
        return R.ok().setData(aiViewPortraitService.orgCandidate(UserInfoUtils.getCurrentUserId()));
    }

    @Operation(summary = "候选人card")
    @GetMapping("/candidate/card")
    @NotDoubleSubmit
    public R candidatesCard(CandidatesDTO candidatesDTO) {
        return R.ok().setData(aiViewPortraitService.orgCandidateCard(candidatesDTO));
    }

}
