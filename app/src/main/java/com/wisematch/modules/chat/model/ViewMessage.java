package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version Message.java, v0.1 2025-06-20 17:02
 */
@Data
public class ViewMessage {

    String chatId;

    @Schema(description = "姓名")
    String name;

    @Schema(description = "图片")
    String photo;

    /**
     * 回答消息内容
     */
    @Schema(description = "回答消息内容")
    String content;

    @Schema(description = "角色")
    String role;

    Date dateTime;
}
