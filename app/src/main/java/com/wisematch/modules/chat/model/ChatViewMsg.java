package com.wisematch.modules.chat.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.springframework.ai.chat.messages.Message;

import java.util.List;

/**
 * <AUTHOR>
 * @version RequestMsg.java, v0.1 2025-06-20 16:39
 */
@Data
public class ChatViewMsg {

    List<Message> messages;

    Boolean stream = true;

    Float temperature = 0.5f;

    Integer max_tokens = 1024;

    String model;

    Float top_p;

    String custom;

    JSONObject stream_options;

}
