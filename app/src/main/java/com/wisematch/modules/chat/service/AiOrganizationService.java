package com.wisematch.modules.chat.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.wisematch.modules.chat.entity.AiOrganization;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.common.handler.businessLicenseVerify.BusinessLicenseContext;

import java.util.List;
import java.util.concurrent.ExecutionException;

public interface AiOrganizationService extends IService<AiOrganization> {

    boolean isCertified(String userId);

    AiOrganization getByUserId(String userId);

    AiOrganization saveObj(AiOrganizationVerifyDTO org);

    void updateObj(AiOrganizationVerifyDTO org);

    Page<AiOrganization> pageQuery(AiOrganizationQueryDTO queryDTO);

    List<AiOrganization> listAll(AiOrganizationQueryDTO queryDTO);

    void logicDelete(String ids);

    void batchLogicDelete(List<String> ids);

    License3MetaVerifyVO licenseMetaVerify(FileSrcDTO fileSrcDTO) throws ExecutionException, InterruptedException, JsonProcessingException;

    void associate(AiOrganizationInsertDTO org);

    void associate(AiOrganizationVerifyDTO org);

    AiOrganization getByCreditCode(String code);

    public void changeOpenStatus(ChangeOpenStatusDTO dto);
    public void examCertification(String userId);

    void save(BusinessLicenseContext businessLicenseContext);

    BizStatusVO certifiedEnterprise(CertifiedEnterpriseDTO org) throws ExecutionException, InterruptedException, JsonProcessingException;

    void updateOrg(UpdateOrgDTO org);

}

