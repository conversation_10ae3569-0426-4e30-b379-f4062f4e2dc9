package com.wisematch.modules.chat.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "企业组织信息")
public class AiOrganizationUpdateDTO implements Serializable {

    @Schema(description = "企业徽标")
    private String organizationLogo;

    @Schema(description = "企业名称")
    private String organizationName;

    @Schema(description = "唯一社会信用代码")
    private String uniqueSocialCreditCode;

    @Schema(description = "营业执照")
    private String businessLicense;

    @Schema(description = "企业介绍")
    private String organizationIntroduction;

    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private String id;
//
//    @Schema(description = "0未审核，1已认证，2驳回")
//    private Integer isCertified ;

    private String businessAddress;

    @Schema(description = "官网链接")
    private String businessUrl;
}
