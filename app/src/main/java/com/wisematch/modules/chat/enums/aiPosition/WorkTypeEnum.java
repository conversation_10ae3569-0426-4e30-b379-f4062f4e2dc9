package com.wisematch.modules.chat.enums.aiPosition;

import java.util.ArrayList;
import java.util.List;

public enum WorkTypeEnum {

    END_OFFICE("1", "到岗"),
    CLOUD_OFFICE("2", "云办公");

    private final String key;
    private final String value;

    WorkTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    // 根据 key 获取 value
    public static String getValueByKey(String key) {
        for (WorkTypeEnum item : values()) {
            if (item.key.equals(key)) {
                return item.value;
            }
        }
        return null;
    }

    // 转成列表（每项是 Map<String, String>，包含 key 和 value）
    public static List<PositionMenuModel> toMenu() {
        List<PositionMenuModel> list = new ArrayList<>();
        for (WorkTypeEnum item : values()) {
            PositionMenuModel positionMenuModel = new PositionMenuModel();
            positionMenuModel.setKey(item.key);
            positionMenuModel.setValue(item.value);
            list.add(positionMenuModel);
        }
        return list;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}

