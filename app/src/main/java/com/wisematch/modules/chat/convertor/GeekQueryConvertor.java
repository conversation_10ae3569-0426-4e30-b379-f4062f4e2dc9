package com.wisematch.modules.chat.convertor;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wisematch.common.utils.UserUtils;
import com.wisematch.modules.chat.entity.AiJobPosition;
import com.wisematch.modules.chat.entity.AiViewPortrait;
import com.wisematch.modules.chat.model.CandidatePortrait;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.chat.model.GeekQuery;
import com.wisematch.modules.form.entity.AiUserForm;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.model.JobApplication;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Optional;

@Slf4j
public class GeekQueryConvertor {
    public static GeekQuery convert(SysUser sysUser, AiUserForm aiUserForm, AiViewPortrait aiViewPortrait, AiJobPosition aiJobPosition, ObjectMapper objectMapper) {

        GeekQuery geekQuery = new GeekQuery();
        geekQuery.setUserId(sysUser.getUserId());
        geekQuery.setRoomId(aiViewPortrait.getRoomId());
        geekQuery.setDelStatus(sysUser.getIsDel());
        geekQuery.setStatus(sysUser.getStatus());
        geekQuery.setPortraitStatus(aiViewPortrait.getStatus());
        geekQuery.setPortraitVerifyStatus(aiViewPortrait.getVerifyStatus());
        geekQuery.setExamineId(aiJobPosition.getJobExamineId());
        geekQuery.setPositionId(aiJobPosition.getId());

        Optional<JobApplication> jobApplication = Optional.ofNullable(aiUserForm)
                .map(AiUserForm::getFormJson)
                .flatMap(formJson -> {
                    try {
                        return Optional.of(objectMapper.readValue(formJson, JobApplication.class));
                    } catch (JsonProcessingException e) {
                        log.error("parse job application error", e);
                        return Optional.empty();
                    }
                });

        String summary;
        try {
            summary = Optional.ofNullable(objectMapper.readValue(aiViewPortrait.getPortrait(), CandidatePortrait.class))
                    .map(CandidatePortrait::getComments)
                    .orElse("");
        } catch (JsonProcessingException e) {
            log.error("parse portrait error", e);
            summary = "";
        }
        geekQuery.setSummary(summary);

        String workCity = jobApplication
                .map(JobApplication::getWorkCity)
                .orElse("");
        geekQuery.setWorkCity(workCity);
        String expectedCity = jobApplication
                .map(JobApplication::getExpectedCity)
                .orElse("");
        geekQuery.setExpectedCity(expectedCity);

        Optional<CardInfo> cardInfo = Optional.ofNullable(JSONObject.parseObject(aiViewPortrait.getBrief(), CardInfo.class));
        geekQuery.setLabels(cardInfo.map(CardInfo::getLabels).orElse(""));

        String keyLabel = cardInfo
                .map(CardInfo::getKeyLabel)
                .orElse("");
        String birthDayStr = sysUser.getBirthDayStr();
        Integer age = UserUtils.calculateAge(birthDayStr);
        if (age != null) {
            keyLabel = StringUtils.isEmpty(keyLabel) ? age + "岁" : age + "岁 | " +  keyLabel;
        }
        geekQuery.setKeyLabel(keyLabel);

        geekQuery.setEmbedding(List.of(0d, 0d));

        return geekQuery;
    }
}
