package com.wisematch.modules.chat.event;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.utils.ThreadPoolUtil;
import com.wisematch.modules.chat.model.AiJobPositionMilvusQueryDTO;
import com.wisematch.modules.chat.config.MilvusTemplate;
import com.wisematch.modules.chat.convertor.AiJobPositionMilvusQueryDTOConvertor;
import com.wisematch.modules.chat.convertor.AiJobPositionMilvusRecommendDTOConvertor;
import com.wisematch.modules.chat.entity.AiJobPosition;
import com.wisematch.modules.chat.enums.PositionOperationType;
import com.wisematch.modules.chat.enums.PositionTextTypeEnum;
import com.wisematch.modules.chat.model.AiJobPositionMilvusRecommendDTO;
import com.wisematch.modules.chat.model.PositionTextWithType;
import com.wisematch.modules.chat.service.AiJobPositionService;
import com.wisematch.modules.chat.utils.EmbeddingUtils;
import com.wisematch.modules.chat.utils.TextSplitUtils;
import io.milvus.v2.service.vector.response.InsertResp;
import io.milvus.v2.service.vector.response.QueryResp;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Component
public class PositionChangeEventListener {

    @Resource
    private AiJobPositionService aiJobPositionService;

    @Resource
    private MilvusTemplate milvusTemplate;

    @Resource
    private TextSplitUtils textSplitUtils;

    @Resource
    private EmbeddingUtils embeddingUtils; // 使用优化后的向量工具类

    @Resource
    private Gson gson;

    @EventListener(PositionChangeEvent.class)
    public void consume(PositionChangeEvent event) {
        ThreadPoolUtil.execute(() -> {
            String positionId = event.getPositionId();
            PositionOperationType operationType = event.getOperationType();

            // 1. 基础校验
            validateParams(positionId, operationType);

            log.info("开始处理职位事件：positionId={}, 操作类型={}", positionId, operationType);

            try {
                // 2. 根据操作类型路由处理逻辑
                switch (operationType) {
                    case CREATE:
                        handleCreateRecommend(positionId);
                        break;
                    case UPDATE:
                        handleUpdateRecommend(positionId);
                        break;
                    case DELETE:
                        handleDeleteRecommend(positionId);
                        break;
                    default:
                        log.warn("未定义的职位操作类型：{}，忽略处理", operationType);
                }
                log.info("职位事件处理完成：positionId={}, 操作类型={}", positionId, operationType);
            } catch (Exception e) {
                log.error("职位事件处理失败：positionId={}, 操作类型={}", positionId, operationType, e);
            }
        });
    }

    /**
     * 生成查询向量的Milvus数据
     */
    private List<JsonObject> generateQueryMilvusData(AiJobPosition position) {
        // 仅通过转换器转换实体类，不添加其他额外操作
        AiJobPositionMilvusQueryDTO queryDTO = AiJobPositionMilvusQueryDTOConvertor.convert(position);

        // 直接将DTO转换为Milvus所需的JsonObject
        String jsonStr = gson.toJson(queryDTO);
        JsonObject jsonObject = gson.fromJson(jsonStr, JsonObject.class);

        return Collections.singletonList(jsonObject);
    }


    /**
     * 参数校验
     */
    private void validateParams(String positionId, PositionOperationType operationType) {
        Assert.notNull(positionId, "职位ID不能为空");
        Assert.notNull(operationType, "操作类型不能为空");
    }

    /**
     * 处理职位创建事件
     */
    private void handleCreateRecommend(String positionId) {
        // 查询职位详情并验证
        AiJobPosition position = getValidPosition(positionId);
        if (position == null) {
            return;
        }

        try {
            // 生成并插入向量数据（依赖按索引排序的向量结果）
            List<JsonObject> milvusData = generateMilvusData(position);
            InsertResp insertResp = milvusTemplate.insert("position_recommendation", milvusData);

            if (insertResp != null && !insertResp.getPrimaryKeys().isEmpty()) {
                log.info("职位创建事件：向量库插入成功，positionId={}, 记录数={}", positionId, milvusData.size());
            } else {
                log.error("职位创建事件：向量库插入失败，positionId={}", positionId);
                throw new RRException("向量库插入失败");
            }

        } catch (Exception e) {
            log.error("职位创建事件处理失败", e);
            throw new RRException("创建事件同步失败：" + e.getMessage(), e);
        }
    }

    /**
     * 处理职位更新事件
     */
    private void handleUpdateRecommend(String positionId) {
        // 查询职位详情并验证
        AiJobPosition position = getValidPosition(positionId);
        if (position == null) {
            return;
        }

        try {
            // 1. 先查询向量库中该职位的旧记录ID
            String queryFilter = " position_id == '" + position.getId() + "'";
            QueryResp resp = milvusTemplate.query("position_recommendation", queryFilter, List.of("id"), 16384, 0);
            List<Long> ids = resp.getQueryResults().stream()
                    .map(item -> (Long) item.getEntity().get("id"))
                    .toList();
            if (!ids.isEmpty()) {
                log.info("职位更新事件：查询到旧记录{}条，positionId={}", ids.size(), positionId);
            } else {
                log.warn("职位更新事件：未查询到旧记录，positionId={}", positionId);
            }

            // 2. 生成并插入新的向量数据
            List<JsonObject> milvusData = generateMilvusData(position);
            InsertResp insertResp = milvusTemplate.insert("position_recommendation", milvusData);

            if (insertResp == null || insertResp.getPrimaryKeys().isEmpty()) {
                log.error("职位更新事件：新记录插入失败，positionId={}", positionId);
                throw new RRException("向量库插入新记录失败");
            }
            log.info("职位更新事件：新记录插入成功，positionId={}, 记录数={}", positionId, milvusData.size());

            // 3. 最后删除向量库中该职位的旧记录（使用查询到的ID）
            if (!ids.isEmpty()) {
                // 构建基于ID的删除过滤器
                boolean deleteSuccess = milvusTemplate.deleteById("position_recommendation", ids);

                if (deleteSuccess) {
                    log.info("职位更新事件：旧记录删除成功，positionId={}, 删除条数={}", positionId, ids.size());
                } else {
                    log.error("职位更新事件：旧记录删除失败，positionId={}, 尝试删除的ID列表={}", positionId, ids);
                }
            }

        } catch (Exception e) {
            log.error("职位更新事件处理失败", e);
            throw new RRException("更新事件同步失败：" + e.getMessage(), e);
        }
    }

    /**
     * 处理职位删除事件（保持原有逻辑）
     */
    private void handleDeleteRecommend(String positionId) {
        try {
            // 先删除向量库中该职位的旧记录（使用下划线字段）
            String deleteFilter = " position_id == '" + positionId + "'";
            boolean deleteSuccess = milvusTemplate.deleteByFilter("position_recommendation", deleteFilter);
            if (!deleteSuccess) {
                log.warn("职位更新事件：旧记录删除失败（可能不存在），positionId={}", positionId);
            } else {
                log.info("职位更新事件：旧记录删除成功，positionId={}", positionId);
            }
        } catch (Exception e) {
            throw new RRException("删除事件同步失败：" + e.getMessage(), e);
        }
    }

    // ------------------------------ 公共方法 ------------------------------

    /**
     * 获取有效的职位信息并验证状态
     */
    private AiJobPosition getValidPosition(String positionId) {
        AiJobPosition position = aiJobPositionService.getById(positionId);
        if (position == null) {
            log.error("处理失败：未找到职位信息，positionId={}", positionId);
            return null;
        }

        return position;
    }

    /**
     * 生成Milvus需要的向量数据（核心公共逻辑，依赖按索引排序的向量）
     */
    private List<JsonObject> generateMilvusData(AiJobPosition position) {
        // 提取需要处理的字段
        String summary = position.getSummary();
        String content = position.getContent();
        String benefit = position.getCompanyPositionBenefit();
        String skill = position.getCompanyPositionSkill();
        String location = position.getWorkLocation();

        // 拆分长文本
        List<String> contentSentences = textSplitUtils.splitPosition(content);

        // 处理benefit（英文逗号拆分）
        List<String> benefitItems = splitByComma(benefit);
        // 处理skill（英文逗号拆分）
        List<String> skillItems = splitByComma(skill);

        // 收集所有需要向量化的文本及类型（顺序固定）
        List<PositionTextWithType> allTexts = collectAllTexts(
                summary,
                contentSentences,
                benefitItems,
                skillItems,
                location,
                position.getPosition(),
                position.getCompany()
        );

        // 批量向量化（向量顺序已按text_index与allTexts顺序一致）
        List<String> texts = allTexts.stream()
                .map(PositionTextWithType::getText)
                .collect(Collectors.toList());
        List<List<Double>> embeddings = embeddingUtils.embedTexts(texts, 768);

        return convertToMilvusData(allTexts, embeddings, position);
    }

    /**
     * 按英文逗号拆分字符串（处理空值和空字符串）
     */
    private List<String> splitByComma(String text) {
        List<String> result = new ArrayList<>();
        if (text == null || text.trim().isEmpty()) {
            return result;
        }
        // 按逗号拆分并过滤空字符串
        String[] parts = text.split(",");
        for (String part : parts) {
            String trimmed = part.trim();
            if (!trimmed.isEmpty()) {
                result.add(trimmed);
            }
        }
        return result;
    }

    /**
     * 收集所有需要处理的文本及其类型（固定顺序，与后续向量一一对应）
     */
    private List<PositionTextWithType> collectAllTexts(String summarySentence,
                                               List<String> contentSentences,
                                               List<String> benefitItems,
                                               List<String> skillItems,
                                               String location,
                                               String title,
                                               String companyName) {
        List<PositionTextWithType> allTexts = new ArrayList<>();

        // 1. 添加summary（使用枚举类型）
        if (StringUtils.hasLength(title)) {
            allTexts.add(new PositionTextWithType(summarySentence, PositionTextTypeEnum.SUMMARY));
        }

        // 2. 添加拆分后的content（使用枚举类型）
        allTexts.addAll(contentSentences.stream()
                .filter(StringUtils::hasLength) // 过滤空句子
                .map(sentence -> new PositionTextWithType(sentence, PositionTextTypeEnum.CONTENT))
                .toList());

        // 3. 添加拆分后的benefit（使用枚举类型）
        allTexts.addAll(benefitItems.stream()
                .filter(StringUtils::hasLength) // 过滤空项
                .map(item -> new PositionTextWithType(item, PositionTextTypeEnum.BENEFITS))
                .toList());

        // 4. 添加拆分后的skill（使用枚举类型）
        allTexts.addAll(skillItems.stream()
                .filter(StringUtils::hasLength) // 过滤空项
                .map(item -> new PositionTextWithType(item, PositionTextTypeEnum.SKILLS))
                .toList());

        // 5. 添加location（使用枚举类型）
        if (StringUtils.hasLength(title)) {
            allTexts.add(new PositionTextWithType(location, PositionTextTypeEnum.LOCATION));
        }

        // 6. 添加职位名称
        if (StringUtils.hasLength(title)) {
            allTexts.add(new PositionTextWithType(title, PositionTextTypeEnum.TITLE));
        }

        // 7. 添加公司名称
        if (StringUtils.hasLength(companyName)) {
            allTexts.add(new PositionTextWithType(companyName, PositionTextTypeEnum.COMPANY));
        }

        return allTexts;
    }

    /**
     * 转换为Milvus需要的JsonObject列表（向量与文本按顺序严格对应）
     * 使用Stream API优化循环逻辑
     */
    private List<JsonObject> convertToMilvusData(List<PositionTextWithType> allTexts, List<List<Double>> embeddings,
                                                 AiJobPosition position) {

        // 使用IntStream遍历索引，保持文本与向量的对应关系
        return IntStream.range(0, allTexts.size())
                .mapToObj(index -> {
                    PositionTextWithType textWithType = allTexts.get(index);
                    List<Double> embedding = embeddings.get(index);

                    // 转换为MilvusDTO
                    AiJobPositionMilvusRecommendDTO dto = AiJobPositionMilvusRecommendDTOConvertor
                            .convert(position, embedding, textWithType);

                    // 利用Gson自动转换为下划线命名的JsonObject
                    String jsonStr = gson.toJson(dto);
                    return gson.fromJson(jsonStr, JsonObject.class);
                })
                .collect(Collectors.toList());
    }

}