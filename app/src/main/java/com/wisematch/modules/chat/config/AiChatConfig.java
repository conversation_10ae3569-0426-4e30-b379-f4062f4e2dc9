package com.wisematch.modules.chat.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * <AUTHOR> href="mailto:<EMAIL>">yuluo</a>
 */

@Configuration
@Data
public class AiChatConfig {

	@Value("${spring.volc.access-key-id}")
	private String accessKeyId;

	@Value("${spring.volc.secret-access-key}")
	private String secretAccessKey;

	@Value("${spring.ai.interview.appid}")
	private String appId;

	@Value("${spring.ai.interview.app-key}")
	private String appKey;

	private String token;

	@Value("${spring.ai.interview.asr.appid}")
	private String asrAppId;
	@Value("${spring.ai.interview.asr.token}")
	private String asrToken;
	@Value("${spring.ai.interview.tts.appid}")
	private String ttsAppId;
	@Value("${spring.ai.interview.tts.token}")
	private String ttsToken;
	@Value("${spring.ai.interview.llm.url}")
	private String llmUrl;


}
