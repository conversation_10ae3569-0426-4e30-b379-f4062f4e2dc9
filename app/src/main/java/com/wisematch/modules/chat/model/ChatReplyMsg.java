package com.wisematch.modules.chat.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version RequestMsg.java, v0.1 2025-06-20 16:39
 */
@Data
public class ChatReplyMsg {

    String id;

    long created = System.currentTimeMillis();

    String object = "chat.completion.chunk";

    String model = "wise-match";

    JSONObject stream_options = new JSONObject().fluentPut("include_usage",true);

    List<Choices> choices = new ArrayList<>();

    @Data
    public static class Choices{
        String finish_reason = "null";
        JSONObject delta = new JSONObject().fluentPut("role", "assistant");
        Integer index = 0;
    }

    public static ChatReplyMsg reply(String replyId, String msg){
        ChatReplyMsg replyMsg = new ChatReplyMsg();
        replyMsg.setId(replyId);
        ChatReplyMsg.Choices choices = new ChatReplyMsg.Choices();
        choices.getDelta().put("content", msg);
        replyMsg.setChoices(List.of(choices));
        return replyMsg;
    }
}
