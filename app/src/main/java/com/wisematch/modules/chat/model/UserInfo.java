package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户信息
 * <AUTHOR>
 * @version UserInfo.java, v0.1 2025-07-01 23:20
 */
@Data
public class UserInfo {

    String userId;

    @Schema(description = "用户昵称")
    String userName;

    @Schema(description = "用户电话")
    String phone;

    @Schema(description = "用户头像")
    String userPhoto;

    String token;

}
