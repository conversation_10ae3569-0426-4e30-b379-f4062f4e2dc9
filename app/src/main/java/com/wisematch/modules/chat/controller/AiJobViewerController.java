package com.wisematch.modules.chat.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wisematch.common.annotation.SysLog;
import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.entity.AiJobViewer;
import com.wisematch.modules.chat.model.AiJobViewerQueryDTO;
import com.wisematch.modules.chat.model.IdRequest;
import com.wisematch.modules.chat.service.AiJobViewerService;
import com.wisematch.modules.chat.service.AiOrganizationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

@RestController
@RequestMapping("/jobViewer")
@Tag(name = "面试官", description = "面试官接口")
@Slf4j
public class AiJobViewerController {

    @Autowired
    AiOrganizationService aiOrganizationService;
    @Autowired
    private AiJobViewerService aiJobViewerService;

    @PostMapping("/save")
    @SysLog("保存面试官")
    @Operation(summary = "保存面试官")
    public R save(@RequestBody AiJobViewer viewer) {
        aiOrganizationService.examCertification(UserInfoUtils.getCurrentUserId());
        aiJobViewerService.createObj(viewer);
        return R.ok();
    }

    @PostMapping("/update")
    @Operation(summary = "更新面试官")
    @SysLog("更新面试官")
    public R update(@RequestBody AiJobViewer viewer) {
        aiOrganizationService.examCertification(UserInfoUtils.getCurrentUserId());
        aiJobViewerService.updateById(viewer);
        return R.ok();
    }

    @PostMapping("/getById")
    @Operation(summary = "根据ID查询")
    public R getById(@RequestBody IdRequest request) {
        return R.ok().setData(aiJobViewerService.getById(request.getId()));
    }

    @PostMapping("/pageQuery")
    @Operation(summary = "分页查询面试官")
    @NotDoubleSubmit
    public R pageQuery(@RequestBody AiJobViewerQueryDTO dto) {
        Page<AiJobViewer> page = aiJobViewerService.pageQuery(dto);
        return R.ok().setData(page);
    }

    /**
     * 单个删除
     */
    @Operation(summary = "单个删除")
    @PostMapping(value = "/oneDelete")
    @SysLog("单个删除")
    public R oneDelete(@RequestBody IdRequest idRequest) {
        aiOrganizationService.examCertification(UserInfoUtils.getCurrentUserId());
        aiJobViewerService.logicDelete(idRequest.getId());
        return R.ok();
    }

    /**
     * 批量删除
     */
    @PostMapping(value = "/batchLogicDelete")
    @Operation(summary = "批量删除")
    @SysLog("批量删除")
    public R delete(@RequestBody String[] ids) {
        aiOrganizationService.examCertification(UserInfoUtils.getCurrentUserId());
        aiJobViewerService.batchLogicDelete(Arrays.asList(ids));
        return R.ok();
    }

    @PostMapping("/list")
    @Operation(summary = "查询企业对应的面试官信息")
    @NotDoubleSubmit
    public R list(@RequestBody AiJobViewerQueryDTO dto) {
        return R.ok().setData(aiJobViewerService.listAll(dto));
    }

    @PostMapping("/defaultPhoto")
    @Operation(summary = "默认面试官图片列表")
    @NotDoubleSubmit
    public R list() {
        return R.ok().setData(aiJobViewerService.defaultPhoto());
    }

}
