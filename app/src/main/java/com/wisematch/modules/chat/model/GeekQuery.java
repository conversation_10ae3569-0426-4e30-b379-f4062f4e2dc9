package com.wisematch.modules.chat.model;

import lombok.Data;

import java.util.List;

@Data
public class GeekQuery {


    private String userId;

    private String roomId;

    private Integer delStatus;

    private Integer status;

    private Integer portraitStatus;

    private Integer portraitVerifyStatus;

    private String examineId;

    private String expectedCity;

    private String workCity;

    private String positionId;

    private String summary;

    private String keyLabel;

    private String labels;

    private List<Double> embedding;
}
