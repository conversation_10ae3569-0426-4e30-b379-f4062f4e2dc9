package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.modules.chat.entity.AiViewRecord;
import com.wisematch.modules.chat.mapper.AiViewRecordMapper;
import com.wisematch.modules.chat.service.AiJobApplyService;
import com.wisematch.modules.chat.service.AiViewRecordService;
import com.wisematch.modules.common.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AiJobApplyServiceImpl extends ServiceImpl<AiViewRecordMapper, AiViewRecord> implements AiJobApplyService {
    @Autowired
    AiViewRecordService aiViewRecordService;

    @Override
    public void insertObj(Object params, String roomId) {
        String json = JsonUtils.toJson(params);
        this.baseMapper.update(new LambdaUpdateWrapper<AiViewRecord>()
                .eq(AiViewRecord::getRoomId,roomId).set(AiViewRecord::getSubmitForm,json));

    }
}
