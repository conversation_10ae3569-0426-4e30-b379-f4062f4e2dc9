package com.wisematch.modules.chat.wrapper.wiser.agents;

import com.wisematch.common.utils.AgentsResult;
import com.wisematch.modules.chat.model.AgentsReportReq;
import com.wisematch.modules.chat.model.AgentsReportVO;
import com.wisematch.modules.chat.service.AgentsReportService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class AgentsReportServiceWrapper {

    @Resource
    private AgentsReportService agentsReportService;

    public AgentsReportVO getReport(AgentsReportReq req) {
        return AgentsResult.handleResult(() -> agentsReportService.getReport(req));
    }

}
