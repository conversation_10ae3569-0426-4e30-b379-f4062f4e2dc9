package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.modules.chat.entity.AiAgentPool;
import com.wisematch.modules.chat.mapper.AiAgentPoolMapper;
import com.wisematch.modules.chat.service.AiAgentPoolService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 智能体池
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiAgentPoolServiceImpl extends ServiceImpl<AiAgentPoolMapper, AiAgentPool> implements AiAgentPoolService {

    @Override
    public String getPromptById(String id) {
        return this.getById(id).getPrompt();
    }
}
