package com.wisematch.modules.chat.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class CandidatePortrait {
    //需要RAG
    private String comments;
    //需要RAG
    private String evaluations;
    //需要RAG
    private String highlights;

    private List<Dimension> dimensions;

    /**
     * 评估维度内部类
     */
    @Data
    public static class Dimension {

        private Integer score;
        //需要RAG
        private String advantages;
        //需要RAG
        private String disadvantages;

        @SerializedName("dimensionName")
        private String dimensionName;
    }
}
