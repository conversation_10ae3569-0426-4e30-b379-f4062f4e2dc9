package com.wisematch.modules.chat.enums;

import lombok.Getter;
import java.util.Objects;

/**
 * 结算方式枚举
 */
@Getter
public enum SettlementMethod {
    // 每月
    MONTHLY(0, "每月"),
    // 每日
    DAILY(1, "每日"),
    // 每小时
    HOURLY(2, "每小时"),
    // 每年
    YEARLY(3, "每年");

    private final int code;
    private final String name;

    SettlementMethod(int code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据code获取枚举实例
     * @param code 编码
     * @return 对应的枚举实例
     */
    public static SettlementMethod getByCode(int code) {
        for (SettlementMethod method : values()) {
            if (method.code == code) {
                return method;
            }
        }
        throw new IllegalArgumentException("无效的结算方式code：" + code);
    }

    /**
     * 根据name获取枚举实例
     * @param name 名称
     * @return 对应的枚举实例
     */
    public static SettlementMethod getByName(String name) {
        for (SettlementMethod method : values()) {
            if (Objects.equals(name, method.getName())) {
                return method;
            }
        }
        throw new IllegalArgumentException("无效的结算方式name：" + name);
    }
}
