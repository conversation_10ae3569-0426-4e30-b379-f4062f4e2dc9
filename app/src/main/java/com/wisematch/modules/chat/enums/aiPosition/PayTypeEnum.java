package com.wisematch.modules.chat.enums.aiPosition;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum PayTypeEnum {

    MOUTH("1", "每月"),
    DAY("2", "每日"),
    HOURE("3", "每小时"),
    YEAR("4", "每年");

    private final String key;
    private final String value;

    PayTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    // 根据 key 获取 value
    public static String getValueByKey(String key) {
        for (PayTypeEnum item : values()) {
            if (item.key.equals(key)) {
                return item.value;
            }
        }
        return null;
    }

    // 转成列表（每项是 Map<String, String>，包含 key 和 value）
    public static List<Map<String, String>> toList() {
        List<Map<String, String>> list = new ArrayList<>();
        for (PayTypeEnum item : values()) {
            Map<String, String> map = new HashMap<>();
            map.put(item.key,item.value);
            list.add(map);
        }
        return list;
    }


    public static List<PositionMenuModel> toMenu() {
        List<PositionMenuModel> list = new ArrayList<>();

        for (PayTypeEnum item : values()) {
            PositionMenuModel positionMenuModel = new PositionMenuModel();
            positionMenuModel.setKey(item.key);
            positionMenuModel.setValue(item.value);
            list.add(positionMenuModel);
        }
        return list;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}

