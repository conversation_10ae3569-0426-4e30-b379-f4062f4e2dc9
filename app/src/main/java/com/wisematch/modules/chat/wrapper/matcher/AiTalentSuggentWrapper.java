package com.wisematch.modules.chat.wrapper.matcher;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.wisematch.modules.chat.entity.AiChatMatcherMemory;
import com.wisematch.modules.chat.entity.AiJobPosition;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.*;
import com.wisematch.modules.chat.wrapper.AbstractWiseMatchWrapper;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 模拟面试推荐
 */
@Component
@Data
@Slf4j
public class AiTalentSuggentWrapper extends AbstractWiseMatchWrapper {

    @Autowired
    AiUserResumeService userResumeService;

    @Autowired
    AiViewPortraitService aiViewPortraitService;

    @Resource
    private GeekRecommendMilvusService geekRecommendMilvusService;

    @Resource
    private AiChatMemoryService aiChatMemoryService;

    @Resource
    private AiChatMatcherMemoryService aiChatMatcherMemoryService;

    @Resource
    private AiJobPositionService aiJobPositionService;

    @Override
    public String promptWrap(AgentHandlerContext context, String prompt) {
        // 使用ArrayList确保可修改性，收集用户消息
        List<Message> messages = context.getMessages().stream()
                .filter(item -> MessageType.USER.equals(item.getMessageType()))
                .collect(Collectors.toCollection(ArrayList::new));
        // 添加当前用户消息
        String currentMsg = context.getAiChatUserMsg().getMsg();
        messages.add(new UserMessage(currentMsg));

        Optional<JSONObject> position = aiChatMatcherMemoryService.getList(context.getAiChatUserMsg().getChatId()).stream()
                .sorted(Comparator.comparing(AiChatMatcherMemory::getTimestamp).reversed())
                .filter(aiChatMemory -> JSONUtil.isTypeJSON(aiChatMemory.getContent()))
                .map(aiChatMemory -> JSONObject.parseObject(aiChatMemory.getContent(), AiChatUserMsg.class))
                .filter(aiChatUserMsg -> aiChatUserMsg.getCardInfos() != null)
                .flatMap(aiChatUserMsg -> aiChatUserMsg.getCardInfos().stream())
                .filter(cardInfo -> "POSITION".equals(cardInfo.getCardType()))
                .map(CardInfo::getExtra)
                .findFirst();

        String jobId = position
                .map(item -> item.getString("id"))
                .orElse("");

        String jobExamineId = position
                .map(item -> item.getString("jobExamineId"))
                .orElse("");

        List<PortraitRecommendDTO> briefList = aiViewPortraitService.getChatRecommend(messages, jobExamineId, "帮我识别用户对人才的需求");
        prompt = prompt.replace("$talentsDocuments",JSONObject.toJSONString(briefList));

        if (StringUtils.hasLength(jobId)) {
            AiJobPosition job = aiJobPositionService.getById(jobId);
            prompt = prompt.replace("$jobDescription", job.getContent());
        } else {
            prompt = prompt.replace("$jobDescription", "");
        }

        return prompt;
    }

    @Override
    public ChatMessage replyWrap(ChatMessage chatMessage) {
        if (!JSONUtil.isTypeJSON(chatMessage.getMsg())) {
            return super.replyWrap(chatMessage);
        }
        if (!chatMessage.getMsg().contains("recommendTalentsId")) {
            return super.replyWrap(chatMessage);
        }
        JSONObject replys = JSONObject.parseObject(chatMessage.getMsg());
        try {
            List<String> suggestList = (List<String>) replys.get("recommendTalentsId");
            if (suggestList != null && !suggestList.isEmpty()) {

                List<CardInfo> cardInfos =  aiViewPortraitService.listByIds(suggestList).stream()
                        .map(e -> aiViewPortraitService.portraitCardInfo(e))
                        .toList();
                List<CardInfoVO> cardInfoVOS = aiViewPortraitService.convert(cardInfos);
                chatMessage.setCardInfos(cardInfoVOS);
            }
        } catch (Exception e) {
            log.error("人才推荐异常", e);
        }
        return super.replyWrap(chatMessage);
    }
}
