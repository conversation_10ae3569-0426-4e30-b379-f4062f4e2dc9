package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.utils.PageUtils;
import com.wisematch.modules.chat.entity.*;
import com.wisematch.modules.chat.enums.ApplyType;
import com.wisematch.modules.chat.enums.WiserConstant;
import com.wisematch.modules.chat.mapper.AiJobExamineMapper;
import com.wisematch.modules.chat.model.AiJobExamineDTO;
import com.wisematch.modules.chat.model.AiJobExamineQueryDTO;
import com.wisematch.modules.chat.service.AiJobExamineService;
import com.wisematch.modules.chat.service.AiJobPositionService;
import com.wisematch.modules.chat.service.AiJobTrainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.wisematch.modules.chat.enums.WiserConstant.DELETED;


/**
 * 智能体池
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiJobExamineServiceImpl extends ServiceImpl<AiJobExamineMapper, AiJobExamine> implements AiJobExamineService {

    @Override
    public void deleteOne(String id) {
        this.update(new UpdateWrapper<AiJobExamine>().lambda().eq(AiJobExamine::getId,id).set(AiJobExamine::getUpdateTime,new Date()).set(AiJobExamine::getIsDel,DELETED));
    }

    @Override
    public void batchLogicDelete(List<String> ids) {
        this.baseMapper.batchLogicDelete(ids);
    }

    @Override
    public Page<AiJobExamine> pageQuery(AiJobExamineDTO aiJobExamineDTO) {
        QueryWrapper<AiJobExamine> wrapper = new QueryWrapper<AiJobExamine>();

        if (StringUtils.isNotBlank(aiJobExamineDTO.getName())) {
            wrapper.lambda().like(AiJobExamine::getName, aiJobExamineDTO.getName());
        }
        wrapper.lambda().eq(AiJobExamine::getIsDel, WiserConstant.NOT_DELETE);

        return PageUtils.doPage(this, aiJobExamineDTO, wrapper);
    }

    @Autowired
    AiJobTrainService aiJobTrainService;


    @Autowired
    AiJobPositionService aiJobPositionService;


    @Override
    public AiJobExamine getExamByInterview(AiViewRecord aiViewRecord) {

        String aiJobExamineId = null;
        if (ApplyType.POSITION.name().equals(aiViewRecord.getChatType())) {
            String positionId = aiViewRecord.getPositionId();
            AiJobPosition aiJobPosition = aiJobPositionService.getById(positionId);
            aiJobExamineId = aiJobPosition.getJobExamineId();
        } else if (ApplyType.TRAIN.name().equals(aiViewRecord.getChatType())) {
            String trainId = aiViewRecord.getTrainId();
            AiJobTrain aiJobTrain = aiJobTrainService.getById(trainId);
            aiJobExamineId = aiJobTrain.getJobExamineId();
        }
        return this.getById(aiJobExamineId);
    }

    @Override
    public List<AiJobExamine> listAll(AiJobExamineQueryDTO dto) {
        QueryWrapper<AiJobExamine> wrapper = new QueryWrapper<>();

        if (org.apache.commons.lang.StringUtils.isNotBlank(dto.getName())) {
            wrapper.lambda().like(AiJobExamine::getName, dto.getName());
        }
        wrapper.lambda().eq(AiJobExamine::getIsDel,WiserConstant.NOT_DELETE);
        return this.list(wrapper);
    }

    @Override
    public Page<AiJobExamine> selectAll(AiJobExamineDTO aiJobExamineDTO) {
        QueryWrapper<AiJobExamine> wrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(aiJobExamineDTO.getName())) {
            wrapper.lambda().like(AiJobExamine::getName, aiJobExamineDTO.getName());
        }
        wrapper.lambda().eq(AiJobExamine::getIsDel, WiserConstant.NOT_DELETE);

        return PageUtils.doPage(this, aiJobExamineDTO, wrapper );
    }
}
