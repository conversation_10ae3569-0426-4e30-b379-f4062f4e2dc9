package com.wisematch.modules.chat.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 火山引擎音色声音列表
 * <AUTHOR>
 * @version AiAgentVoice.java, v0.1 2025-06-20 20:12
 */
@Data
@TableName("ai_agent_voice")
@Schema(description = "wiser记录")
public class AiAgentVoice implements Serializable {

    public static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    public String id;

    @Schema(description = "声音名称")
    public String voiceName;

    @Schema(description = "声音代码")
    public String voiceCode;

    @Schema(description = "声音头像")
    public String voicePhoto;

    @Schema(description = "播放地址")
    public String voiceUrl;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date createTime;
}
