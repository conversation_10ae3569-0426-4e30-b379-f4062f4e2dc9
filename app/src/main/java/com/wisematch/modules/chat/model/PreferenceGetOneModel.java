package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PreferenceGetOneModel {

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "目标ID")
    private String targetId;

    @Schema(description = "业务场景： WISER_AGENT:WISER用户对agent回复的反馈，MATCHER_AGENT:MATCHER_用户对agent回复的反馈，USER_POSITION," +
            "用户对推荐职位的反馈:USER_INTERVIEW:用户对推荐面试的反馈，COMPANY_USER:企业对推荐用户的反馈")
    private String bizSence;//业务场景： USER_AGENT:用户对agent回复的反馈，USER_POSITION,用户对推荐职位的反馈:USER_INTERVIEW:用户对推荐面试的反馈，COMPANY_USER:企业对推荐用户的反馈



}
