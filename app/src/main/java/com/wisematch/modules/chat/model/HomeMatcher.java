package com.wisematch.modules.chat.model;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version HomeIndex.java, v0.1 2025-07-01 23:25
 */
@Data
public class HomeMatcher {

    /**
     * 头像
     */
    @Schema(description = "头像")
    String photo;

    /**
     * wiser
     */
    @Schema(description = "wiser")
    String name = "Hi~ 我是Matcher";

    /**
     * slogan
     */
    @Schema(description = "1分钟人岗智能匹配")
    String slogan = "1分钟人岗智能匹配";


    @Schema(description = "输入提示")
    String inputTips = "说说你想要什么样的人才，我可以帮你快速匹配";

    /**
     * 热门推荐
     */
    @Schema(description = "热门推荐")
    List<CardInfo> tips;
}
