package com.wisematch.modules.chat.wrapper.wiser;

import com.alibaba.fastjson.JSONObject;
import com.wisematch.common.utils.BeanCopyUtils;
import com.wisematch.common.utils.MybatisUtils;
import com.wisematch.modules.chat.entity.AiJobTrain;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.service.AiJobTrainService;
import com.wisematch.modules.chat.service.AiUserResumeService;
import com.wisematch.modules.chat.wrapper.AbstractWiseMatchWrapper;
import com.wisematch.modules.common.utils.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 模拟面试推荐
 */
@Component
@Data
@Slf4j
public class AiJobTrainSuggestWrapper extends AbstractWiseMatchWrapper {

    @Autowired
    AiUserResumeService userResumeService;

    @Autowired
    private AiJobTrainService aiJobTrainService;

    @Override
    public String promptWrap(AgentHandlerContext context, String prompt) {
        if (prompt.contains("$jobDocuments")) {
            List<CardInfo> list = this.aiJobTrainService.getList();
            if(!list.isEmpty()){
                prompt = prompt.replace("$jobDocuments", JsonUtils.toJson(list.subList(0, list.size()-1)));
            }

        }
        return super.promptWrap(context, prompt);
    }

    @Override
    public ChatMessage replyWrap(ChatMessage chatMessage) {
        if (!chatMessage.getMsg().contains("recommendInterviewId")) {
            return super.replyWrap(chatMessage);
        }
        JSONObject replys = JSONObject.parseObject(chatMessage.getMsg());
        try {
            List<String> rcmdId = (List<String>) replys.get("recommendInterviewId");
            if (null != rcmdId && !rcmdId.isEmpty()) {
                List<AiJobTrain> aiJobTrains = MybatisUtils.listByIds(this.aiJobTrainService,rcmdId);
                List<CardInfo> cardInfos = BeanCopyUtils.copyList(aiJobTrains,CardInfo::jobTrainToCard);
                chatMessage.setCardInfos(cardInfos);
            }
        } catch (Exception e) {
            log.error("面试推荐异常", e);
        }
        return super.replyWrap(chatMessage);
    }
}
