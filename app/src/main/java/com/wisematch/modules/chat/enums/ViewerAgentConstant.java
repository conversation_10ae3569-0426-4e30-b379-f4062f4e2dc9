package com.wisematch.modules.chat.enums;

import java.util.Set;

/**
 * <AUTHOR>
 * @version AgentConstant.java, v0.1 2025-07-08 19:18
 */
public class ViewerAgentConstant {

    /**
     * AI面试-标签提取
     */
    public static final String INTERVIEW_TAG_EXTRACT = "INTERVIEW_TAG_EXTRACT";

    /**
     * 追问agent code
     */
    public static final String INTERVIEW_QUESTION = "INTERVIEW_QUESTION";

    /**
     * 追问集合
     */
    public static final Set<String> FlOW_UP_SET = Set.of("INTERVIEW_QUESTION","FOLLOWUP_QUESTION_YJ","FOLLOWUP_QUESTION_SZ","INTERVIEW_QUESTION_TRAIN");
    /**
     * 简历解析
     */
    public static final String RESUME_PARSE_JSON = "RESUME_PARSE_JSON";

    /**
     * AI面试-岗位匹配度-MBTI
     */
    public static final String MBTI_TO_JOB = "MBTI_TO_JOB";

    /**
     * 简历解析
     */
    public static final String RESUME_SCORE = "RESUME_SCORE";

    /**
     * 职位信息拆分
     */
    public static final String POSITION_PARSE_LIST = "POSITION_PARSE_LIST";


    /**
     * 候选人信息拆分
     */
    public static final String GEEK_PARSE_LIST = "GEEK_PARSE_LIST";


    /**
     * 面试-问题生成-个人经历
     */
    public static final String INTERVIEW_QUESTION_EXPERIENCE = "INTERVIEW_QUESTION_EXPERIENCE";

    /**
     * 社招-问题生成-问题清单
     */
    public static final String QUESTION_GENERATE_SZ_P1 = "QUESTION_GENERATE_SZ_P1";
    /**
     * 社招-问题生成-简历风险点
     */
    public static final String QUESTION_GENERATE_SZ_P2 = "QUESTION_GENERATE_SZ_P2";

    /**
     * 应届-问题生成-问题清单
     */
    public static final String QUESTION_GENERATE_YJ_P1 = "QUESTION_GENERATE_YJ_P1";

    /**
     * 应届-问题生成-简历风险点
     */
    public static final String QUESTION_GENERATE_YJ_P2 = "QUESTION_GENERATE_YJ_P2";
    /**
     /**
     * 意图判别-是否追问
     */
    public static final String INTERVIEW_INTENTION_ASK = "INTERVIEW_INTENT_QUESTION";

    /**
     * 意图判别-其它意图
     */
    public static final String INTERVIEW_INTENTION_OTHER = "INTERVIEW_INTENT_OTHER";

    /**
     * 默认面试官
     */
    public static final String INTERVIEW_AGENT_ID = "17";

    /**
     * 面试意图识别
     */
    public static final String INTERVIEW_INTENTION_AGENT_ID = "15";


    /**
     * 生成画像agent
     */
    public static final String PORTRAIT = "PORTRAIT";

    /**
     * 默认wiser agnet
     */
    public static final String WISER_AGENT_ID = "7";

    /**
     * 默认流式 wiser agent
     */
    public static final String WISER_AGENT_CODE = "7";

    /**
     * 默认matcher回复
     */
    public static final String DEFAULT_MATCHER_CODE = "MATCHER";

    /**
     * 默认matcher agnet
     */
    public static final String MATCHER_AGENT_ID = "7";

    /**
     * 默认wiser agnet
     */
    public static final String REPORT_AGENT_CODE = "REPORT_AGENT";

    /**
     * 默认wiser意图识别agnet
     */
    public static final String WISER_INTENTION_AGENT_ID = "8";


    public static final String QUESTION = "2";

    /**
     * 简历解析智能体
     */
    public static final String RESUME_PARSE_AGENT_CODE = "RESUME_ANALYSIS_MODEL";

    /**
     * matcher人才推荐
     */
    public static final String MATCHER_TALENT_RECOMMEDN_CODE = "MATCHER_TALENT_RECOMMEDN";

}
