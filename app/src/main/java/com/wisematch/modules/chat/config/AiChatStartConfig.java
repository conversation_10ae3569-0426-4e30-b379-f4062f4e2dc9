package com.wisematch.modules.chat.config;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @version AiChatStartConfig.java, v0.1 2025-06-24 22:56
 */
@Data
public class AiChatStartConfig {

    @JSONField(name = "AppId")
    String AppId;

    @JSONField(name = "RoomId")
    String RoomId;

    @JSONField(name = "TaskId")
    String TaskId;

    @JSONField(name = "Config")
    Config Config = new Config();

    @JSONField(name = "AgentConfig")
    AiChatAgentConfig AgentConfig = new AiChatAgentConfig();

    @Data
    public static class Config {

        @JSONField(name = "ASRConfig")
        AiChatASRConfig ASRConfig = new AiChatASRConfig();

        @JSONField(name = "TTSConfig")
        AiChatTTSConfig TTSConfig = new AiChatTTSConfig();

        @JSONField(name = "LLMConfig")
        AiChatLLMConfig LLMConfig = new AiChatLLMConfig();
    }



//    {
//        AppId: "6854ffb2f69319017564c1f7",
//                RoomId: "ChatRoom01",
//            TaskId: "ChatTask01",
//            AgentConfig: {
//        TargetUserId: [
//        "Huoshan01",
//                服务端使用的 TargetUserId 为客户端 UseId，请保持一致
//    ],
//        WelcomeMessage: "你好，我是小宁，有什么需要帮忙的吗？",
//                UserId: "ChatBot01",
//                EnableConversationStateCallback: true,
//    },
//        Config: {
//            ASRConfig: {
//                Provider: "volcano",
//                        ProviderParams: {
//                    Mode: "smallmodel",
//                            AppId: "**********",
//                            Cluster: "yLpOIpzbGuTKwNogUhNkqXJOtuVxrHIl",
//                },
//            },
//            TTSConfig: {
//                Provider: "volcano_bidirection",
//                        ProviderParams: {
//                    app: {
//                        appid: "**********",
//                                token: "M_DO1jpElFo63yeP6roC0vc18_J6lYxq",
//                    },
//                    audio: {
//                        voice_type: "zh_female_sajiaonvyou_moon_bigtts",
//                                speech_rate: 0,
//                                pitch_rate: 0,
//                    },
//                    ResourceId: "volc.service_type.10029",
//                },
//            },
//            LLMConfig: {
//                Mode: "ArkV3",
//                        EndPointId: "ep-20250604115214-lqnfs",
//                        SystemMessages: [
//                "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。",
//      ],
//                VisionConfig: {
//                    Enable: false,
//                },
//            },
//            InterruptMode: 0,
//        },
//    };
}
