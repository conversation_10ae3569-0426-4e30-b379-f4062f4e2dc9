package com.wisematch.modules.chat.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.wisematch.common.model.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AiNotifyMessagePageDTO implements PageRequest {

    @Schema(description = "起始页")
    private Integer pageNum = 1;

    @Schema(description = "页面大小")
    private Integer pageSize = 10;

    @Schema(description = "当前登录用户类型,WISER/MATCHER")
    private String loginUserType;
//
//    @Schema(description = "内容")
//    private String content;

    @Schema(description = "消息类型(加入候选人TALENT、加入人才池PORTRAIT报告生成REPORT，系统消息SYSTEM、申请查看视频APPLY_VIDEO，交换微信VX_EXCHANGE，交换电话号PHONE_EXCHANGE)", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("biz_sence")
    private String bizSence;


//    @Schema(description = "状态：0 未读，1 已读，2 撤回")
//    private Integer msgStatus;

//    @Schema(description = "优先级")
//    private Integer priority;

}
