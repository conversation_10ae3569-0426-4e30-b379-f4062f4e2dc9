package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Data
public class UserRecommendClickModel{

    @NotEmpty
    private String memoryId;

    @Schema(description = "收藏状态")
    private Integer collect;

    @Schema(description = "点赞状态")
    private Integer thumbsUp;

    @Schema(description = "关联cardInfo的id")
    String recommendId;


    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "使用场景")
    private String bizSence;

}
