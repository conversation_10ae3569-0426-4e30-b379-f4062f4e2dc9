package com.wisematch.modules.chat.controller;

import com.wisematch.common.annotation.SysLog;
import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.modules.chat.entity.AiJobTrain;
import com.wisematch.modules.chat.model.AiJobTrainDTO;
import com.wisematch.modules.chat.service.AiJobTrainService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/jobTrains")
@Tag(name = "模拟面试", description = "面试管理")
public class AiJobTrainController {

    private final AiJobTrainService aiJobTrainService;

    public AiJobTrainController(AiJobTrainService aiJobTrainService) {
        this.aiJobTrainService = aiJobTrainService;
    }

    @PostMapping
    @Operation(summary = "新增")
    @SysLog("新增模拟面试")
    @NotDoubleSubmit
    public R create(@RequestBody AiJobTrain dto) {
        aiJobTrainService.save(dto);
        return R.ok().setData(dto);
    }
//
//    @PostMapping("/deleteOne")
//    @Operation(summary = "删除")
//    public R delete(@RequestBody IdRequest idRequest) {
//        aiJobTrainService.logicDelete(idRequest.getId());
//        return R.ok();
//    }

    @PostMapping("/update")
    @Operation(summary = "修改")
    @SysLog("修改模拟面试")
    @NotDoubleSubmit
    public R update(@RequestBody AiJobTrain dto) {
        aiJobTrainService.updateById(dto);
        return R.ok().setData(dto);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取单条（根据id）")
    public R getById(@PathVariable String id) {
        return R.ok().setData(aiJobTrainService.getById(id));
    }

    @PostMapping("/pageQuery")
    @Operation(summary = "搜索")
    public R pageQuery(
            @RequestBody AiJobTrainDTO aiJobTrain) {

        return R.ok().setData(aiJobTrainService.pageQuery(aiJobTrain));
    }
}
