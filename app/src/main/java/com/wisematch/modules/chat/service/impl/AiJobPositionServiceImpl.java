package com.wisematch.modules.chat.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.common.utils.BeanCopyUtils;
import com.wisematch.common.utils.PageConvertUtils;
import com.wisematch.common.utils.PageUtils;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.agent.AgentContext;
import com.wisematch.modules.chat.agent.AgentFacade;
import com.wisematch.modules.chat.entity.*;
import com.wisematch.modules.chat.enums.*;
import com.wisematch.modules.chat.event.PositionChangeEvent;
import com.wisematch.modules.chat.mapper.AiJobPositionMapper;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.*;
import com.wisematch.modules.common.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.wisematch.modules.chat.enums.WiserConstant.DELETED;

/**
 * 智能体池
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiJobPositionServiceImpl extends ServiceImpl<AiJobPositionMapper, AiJobPosition> implements AiJobPositionService, PreferenceShow {

    @Autowired
    AiUserPreferencesService aiUserPreferencesService;

    @Resource
    private ApplicationContext applicationContext;

    @Autowired
    AiOrganizationService aiOrganizationService;

    @Resource
    private PositionRecommendMilvusService positionRecommendMilvusService;

    @Resource
    private AiAgentPoolService aiAgentPoolService;

    @Resource
    private AgentFacade agentFacade;

    @Resource
    private ObjectMapper objectMapper;

    @Autowired
    private AiChatWiserLogService aiChatWiserLogService;

    @Override
    public List<CardInfo> getRecommend(String chatId, String userMsg, String resume, List<Message> messages) {
        AiAgentPool aiAgentPool = aiAgentPoolService.getByCode(WiserAgentConstant.POSITION_RECOMMEND_PARSE);
        String prompt = aiAgentPool.getPrompt();
        prompt = prompt.replace("$resume", resume);
        prompt = prompt.replace("$history", JSONObject.toJSONString(messages));

        AgentContext agentContext = new AgentContext();
        agentContext.setAgentCode(WiserAgentConstant.POSITION_RECOMMEND_PARSE);
        agentContext.setPrompt(prompt);
        agentContext.setUserMsg(userMsg);
        String reply =  agentFacade.supply(agentContext);

        try {
            AiChatWiserLog wiserLog = new AiChatWiserLog();
            wiserLog.setSysMsg(reply);
            wiserLog.setUserMsg(userMsg);
            wiserLog.setConversationId(chatId);
            wiserLog.setAgentId(aiAgentPool.getAgentCode());
            wiserLog.setAgentName(aiAgentPool.getAgentName());
            wiserLog.setPrompt(agentContext.getPrompt());
            wiserLog.setIntention(aiAgentPool.getAgentName());
            wiserLog.setIntentionCode(aiAgentPool.getAgentCode());
            wiserLog.setIntentionName(aiAgentPool.getAgentName());
            aiChatWiserLogService.supplyAsync(wiserLog);
        }catch (Exception e){
            log.error("Wiser日志异常", e);
        }

        try {
            JobIntent jobIntent = objectMapper.readValue(JsonUtils.extractJSON(reply), JobIntent.class);
            List<String> aiJobPositionIds = positionRecommendMilvusService.chatRecommend(jobIntent);
            if (CollectionUtils.isEmpty(aiJobPositionIds)) {
                return new ArrayList<>();
            }
            List<AiJobPosition> aiJobPositions = listByIds(aiJobPositionIds);
            return positionToCardInfo(aiJobPositions);
        } catch (Exception e) {
            log.error("AiJobPositionServiceImpl#getRecommend", e);
            return new ArrayList<>();
        }
    }

    @Override
    public AiJobPosition detail(String id) {
        AiJobPosition aiJobPosition = this.getById(id);
        String userId = aiJobPosition.getUserId();
        AiOrganization aiOrganization = aiOrganizationService.getByUserId(userId);
        if (ObjectUtils.isNotNull(aiOrganization)) {
            aiJobPosition.setLogo(aiOrganization.getOrganizationLogo());
            aiJobPosition.setBusinessUrl(aiOrganization.getBusinessUrl());
        }
        return aiJobPosition;
    }

    @Override
    public void batchLogicDelete(List<String> ids) {
        this.baseMapper.batchLogicDelete(ids);
    }

    @Override
    public AiJobPosition saveObj(AiJobPosition aiJobPosition) {

        String orgId = UserInfoUtils.getLoginUser().getOrgId();

        if (StringUtils.isBlank(orgId)) {
            throw new RRException(RRExceptionEnum.ORG_NOT_VERIFY);
        }
        aiJobPosition.setOrgId(orgId);
        AiOrganization aiOrganization = aiOrganizationService.getById(orgId);
        aiJobPosition.setLogo(aiOrganization.getOrganizationLogo());
        aiJobPosition.setCompany(aiOrganization.getOrganizationName());
        aiJobPosition.setBusinessUrl(aiOrganization.getBusinessUrl());

        aiJobPosition.setUserId(UserInfoUtils.getCurrentUserId());

        this.baseMapper.insert(aiJobPosition.wrapAiJobPosition(aiJobPosition));
        applicationContext.publishEvent(new PositionChangeEvent(this, aiJobPosition.getId(), PositionOperationType.CREATE));
        return aiJobPosition;
    }

    @Override
    public int updateObjById(AiJobPosition aiJobPosition) {
        aiJobPosition.setIsVerify(WiserConstant.NOT_VERIFY);
        aiJobPosition.setIsDisplayHome(WiserConstant.NOT_DISPLAY_HOME);
        int count = baseMapper.updateById(aiJobPosition.wrapAiJobPosition(aiJobPosition));
        applicationContext.publishEvent(new PositionChangeEvent(this, aiJobPosition.getId(), PositionOperationType.UPDATE));
        return count;
    }

    @Override
    public List<CardInfo> getList() {
        List<AiJobPosition> aiJobPositions = this.list(new QueryWrapper<AiJobPosition>().lambda()
                .eq(AiJobPosition::getPositionStatus, WiserConstant.IN_USE)
                .eq(AiJobPosition::getIsDel, WiserConstant.NOT_DELETE)
                .eq(AiJobPosition::getIsVerify, WiserConstant.VERIFIED)
                .orderByDesc(AiJobPosition::getIsNewest, AiJobPosition::getIsHot, AiJobPosition::getPriority, AiJobPosition::getCreateTime));
        return BeanCopyUtils.copyList(aiJobPositions, CardInfo::positionToCardInfo);
    }

    @Override
    public List<CardInfo> getHomeList() {
        List<AiJobPosition> aiJobPositions = this.list(new QueryWrapper<AiJobPosition>().lambda()
                .eq(AiJobPosition::getPositionStatus, WiserConstant.IN_USE)
                .eq(AiJobPosition::getIsDel, WiserConstant.NOT_DELETE)
                .eq(AiJobPosition::getIsVerify, WiserConstant.VERIFIED)
                .eq(AiJobPosition::getIsDisplayHome, WiserConstant.DISPLAY_HOME)
                .orderByDesc(AiJobPosition::getIsNewest, AiJobPosition::getIsHot, AiJobPosition::getPriority, AiJobPosition::getCreateTime));
        return BeanCopyUtils.copyList(aiJobPositions, CardInfo::positionToCardInfo);
    }

    @Override
    public Page<PositionListVO> pageQueryBack(AiJobPositionQueryDTO aiJobPosition) {
        LambdaQueryWrapper<AiJobPosition> wrapper = new QueryWrapper<AiJobPosition>().lambda();
        if (StringUtils.isNotBlank(aiJobPosition.getPosition())) {
            wrapper.like(AiJobPosition::getPosition, aiJobPosition.getPosition());
        }
        if (ObjectUtils.isNotNull(aiJobPosition.getPositionStatus())) {
            wrapper.eq(AiJobPosition::getPositionStatus, aiJobPosition.getPositionStatus());
        }
        if (ObjectUtils.isNotNull(aiJobPosition.getOrgId())) {
            wrapper.eq(AiJobPosition::getOrgId, aiJobPosition.getOrgId());
        }

        wrapper.eq(AiJobPosition::getUserId, UserInfoUtils.getCurrentUserId());

        wrapper.eq(AiJobPosition::getIsDel, WiserConstant.NOT_DELETE);
        wrapper.orderByDesc(AiJobPosition::getUpdateTime);

        List<AiJobPosition> list = this.list(wrapper);
        Page<AiJobPosition> aiJobPositionPage = new Page<>();
        aiJobPositionPage.setRecords(list);
        //        PageUtils.doPage(this, aiJobPosition, wrapper);
        return PageConvertUtils.convert(
                aiJobPositionPage,
                PositionListVO.class,
                (src, dest) -> {
                    dest.setCandidateNum(0); // TODO
                    dest.setApplyNum(0);     // TODO
                }
        );
    }

    @Override
    public CardInfo getCardInfoById(String id) {
        AiJobPosition aiJobPosition = this.getById(id);
        return CardInfo.positionToCardInfo(aiJobPosition);
    }

    @Override
    public Page<CardInfo> pageQueryFront(AiJobPositionQueryDTO aiJobPosition) {
        QueryWrapper<AiJobPosition> wrapper = new QueryWrapper<>();
        String position = aiJobPosition.getPosition();
        if (StringUtils.isNotBlank(position)) {
            wrapper.lambda().and(w -> w.like(AiJobPosition::getPosition, position).or()
                    .like(AiJobPosition::getContent, position).or()
                    .like(AiJobPosition::getSummary, position));
        }
        if (ObjectUtils.isNotNull(aiJobPosition.getType())) {
            wrapper.lambda().eq(AiJobPosition::getType, aiJobPosition.getType());
        }
        if (ObjectUtils.isNotNull(aiJobPosition.getPositionStatus())) {
            wrapper.lambda().eq(AiJobPosition::getPositionStatus, aiJobPosition.getPositionStatus());
        }
        if (ObjectUtils.isNotNull(aiJobPosition.getUserId())) {
            wrapper.lambda().eq(AiJobPosition::getUserId, aiJobPosition.getUserId());
        }
        if (ObjectUtils.isNotNull(aiJobPosition.getOrgId())) {
            wrapper.lambda().eq(AiJobPosition::getOrgId, aiJobPosition.getOrgId());
        }

        wrapper.lambda().eq(AiJobPosition::getIsDel, WiserConstant.NOT_DELETE);
        wrapper.lambda().eq(AiJobPosition::getPositionStatus, WiserConstant.IN_USE);
        wrapper.lambda().eq(AiJobPosition::getIsVerify, WiserConstant.VERIFIED);

        if (ObjectUtils.isNotNull(aiJobPosition.getSortCreatedTime()) && aiJobPosition.getSortCreatedTime()) {
            wrapper.lambda().orderByDesc(AiJobPosition::getUpdateTime, AiJobPosition::getIsNewest, AiJobPosition::getIsHot, AiJobPosition::getPriority);
        } else if (ObjectUtils.isNotNull(aiJobPosition.getSortMaxSalary()) && aiJobPosition.getSortMaxSalary()) {
            wrapper.lambda().orderByDesc(AiJobPosition::getMaxPay, AiJobPosition::getIsNewest, AiJobPosition::getIsHot, AiJobPosition::getPriority);
        } else {
            wrapper.lambda().orderByDesc(AiJobPosition::getIsNewest, AiJobPosition::getIsHot, AiJobPosition::getPriority, AiJobPosition::getUpdateTime);
        }

        // 正确分页查询
        Page<AiJobPosition> aiJobPositionPage = PageUtils.doPage(this, aiJobPosition, wrapper);

        return PageConvertUtils.convert(
                aiJobPositionPage,
                CardInfo::positionToCardInfo
        );
    }

    @Override
    public void logicDelete(String id) {
        this.update(new UpdateWrapper<AiJobPosition>().lambda().eq(AiJobPosition::getId, id)
                .set(AiJobPosition::getIsDel, DELETED).set(AiJobPosition::getUpdateTime, new Date()));
    }

    @Override
    public List<AiJobPosition> selectAll(AiJobPositionQueryDTO aiJobPosition) {

//        Page<AiJobPosition> page = new Page<>(aiJobPosition.getPageNum(), aiJobPosition.getPageSize());
        QueryWrapper<AiJobPosition> wrapper = new QueryWrapper<AiJobPosition>();
        if (StringUtils.isNotBlank(aiJobPosition.getPosition())) {
            wrapper.lambda().like(AiJobPosition::getPosition, aiJobPosition.getPosition());
        }
        if (ObjectUtils.isNotNull(aiJobPosition.getPositionStatus())) {
            wrapper.lambda().eq(AiJobPosition::getPositionStatus, aiJobPosition.getPositionStatus());
        }
        if (ObjectUtils.isNotNull(aiJobPosition.getUserId())) {
            wrapper.lambda().eq(AiJobPosition::getUserId, aiJobPosition.getUserId());
        }
        if (ObjectUtils.isNotNull(aiJobPosition.getOrgId())) {
            wrapper.lambda().eq(AiJobPosition::getOrgId, aiJobPosition.getOrgId());
        }
        wrapper.lambda().eq(AiJobPosition::getIsDel, WiserConstant.NOT_DELETE);
        return this.list(wrapper);
    }


    @Override
    public Page<CardInfo> getByJobViewId(AiJobPositionQueryDTO aiJobPosition) {

        QueryWrapper<AiJobPosition> wrapper = new QueryWrapper<AiJobPosition>();

        if (null != aiJobPosition.getPositionStatus()) {
            wrapper.lambda().eq(AiJobPosition::getPositionStatus, aiJobPosition.getPositionStatus());
        }

        Page<AiJobPosition> aiJobPositionPage = PageUtils.doPage(this, aiJobPosition, wrapper);

        return PageConvertUtils.convert(
                aiJobPositionPage,
                CardInfo::positionToCardInfo
        );
    }

    @Override
    public List<CardInfo> positionToCardInfo(List<AiJobPosition> aiJobPositions) {

        return aiJobPositions.stream()
                .map(CardInfo::positionToCardInfo)
                .toList();
    }

    @Override
    public void publishDeactivation(PublishDeactivationDTO dto) {
        String positionId = dto.getId();
        this.baseMapper.update(new LambdaUpdateWrapper<AiJobPosition>().eq(AiJobPosition::getId, positionId).set(AiJobPosition::getPositionStatus, dto.getPositionStatus()));
        applicationContext.publishEvent(new PositionChangeEvent(this, positionId, PositionOperationType.UPDATE));
    }

    @Override
    public Page<CardInfo> storeList(AiUserPreferencesQueryDTO queryDTO) {
        queryDTO.setUserId(UserInfoUtils.getCurrentUserId());
        queryDTO.setCollect(WiserConstant.COLLECT);
        queryDTO.setBizSence(UserPreferenceType.POSITION.name());
        Page<AiUserPreferences> aiUserPreferencesPage = aiUserPreferencesService.pageQuery(queryDTO);
        List<AiUserPreferences> aiUserPreferences = aiUserPreferencesPage.getRecords();
        List<String> ids = aiUserPreferences.stream().map(AiUserPreferences::getTargetId).toList();
        Page<CardInfo> cardInfoPage = new Page<>();
        if (!CollectionUtils.isEmpty(ids)) {
            List<AiJobPosition> aiJobPositions = this.listByIds(ids);
            List<CardInfo> cardInfos = this.positionToCardInfo(aiJobPositions);
            cardInfos.forEach(c -> c.setExtra(JsonUtils.toJsonObject(new PreferenceStatusDTO(1))));
            BeanUtils.copyProperties(aiUserPreferencesPage, cardInfoPage);
            cardInfoPage.setRecords(cardInfos);
            return cardInfoPage;
        }
        return cardInfoPage;
    }
}
