package com.wisematch.modules.chat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wisematch.modules.chat.entity.AiUserPreferences;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AiUserPreferencesMapper extends BaseMapper<AiUserPreferences> {
    int batchLogicDelete(List<String> ids);

    Page<AiUserPreferences> selectPreferenceCandidate(
            Page<AiUserPreferences> page,
            @Param("positionId") String positionId,
            @Param("userId") String userId,
            @Param("splitSearch") List<String> splitSearch
    );}

