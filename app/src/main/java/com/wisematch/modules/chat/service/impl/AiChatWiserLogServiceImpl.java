package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.utils.ThreadPoolUtil;
import com.wisematch.modules.chat.entity.AiChatWiserLog;
import com.wisematch.modules.chat.mapper.AiChatWiserLogMapper;
import com.wisematch.modules.chat.service.AiChatWiserLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 智能体池
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiChatWiserLogServiceImpl extends ServiceImpl<AiChatWiserLogMapper, AiChatWiserLog> implements AiChatWiserLogService {

    @Override
    public void supplyAsync(AiChatWiserLog aiChatWiserLog) {
        ThreadPoolUtil.supplyAsync(() -> this.baseMapper.insert(aiChatWiserLog));
    }
}
