package com.wisematch.modules.chat.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.cache.RedisCache;
import com.wisematch.common.utils.PageConvertUtils;
import com.wisematch.common.utils.PageUtils;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.entity.AiChatWiser;
import com.wisematch.modules.chat.entity.AiChatWiserMemory;
import com.wisematch.modules.chat.enums.ViewerAgentConstant;
import com.wisematch.modules.chat.enums.WiserConstant;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.handler.AiWiserAgentFacade;
import com.wisematch.modules.chat.mapper.AiChatWiserMapper;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.*;
import com.wisematch.modules.exam.service.AiAnswerQuestionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.messages.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.wisematch.modules.chat.enums.WiserConstant.DELETED;

/**
 * <AUTHOR>
 * @version AiChatWiserServiceImpl.java, v0.1 2025-06-27 20:34
 */
@Service
@Slf4j
public class AiChatWiserServiceImpl extends ServiceImpl<AiChatWiserMapper, AiChatWiser> implements AiChatWiserService {

    @Autowired
    AiUserPreferencesService aiUserPreferencesService;


    @Autowired
    AiAnswerQuestionService aiAnswerQuestionService;
    @Autowired
    AiAgentPoolService aiAgentPoolService;
    @Autowired
    AiChatWiserMemoryService aiChatMemoryService;
    @Autowired
    AiJobPositionService aiJobPositionService;
    @Autowired
    AiJobTrainService aiJobTrainService;
    @Autowired
    AiWiserAgentFacade aiWiserAgentFacade;
    @Autowired
    RedisCache redisCache;

    @Override
    public HomeWiser index(String userId) {
        HomeWiser home = new HomeWiser();

        if (RandomUtil.randomInt(10) / 2 == 1) {
            CardInfo cardInfo = new CardInfo();
            cardInfo.setTitle("热门岗位等你来投递~");
            cardInfo.setCardType("position");
            CardInfo cardInfo2 = new CardInfo();
            cardInfo2.setTitle("模拟面试快速提升~");
            cardInfo2.setCardType("train");
            CardInfo cardInfo3 = new CardInfo();
            cardInfo3.setTitle("推荐适合我的模拟面试");
            cardInfo3.setCardType("wiser");
            CardInfo cardInfo4 = new CardInfo();
            cardInfo4.setTitle("推荐适合我的模拟面试");
            cardInfo4.setCardType("wiser");
            home.setTips(List.of(cardInfo, cardInfo2, cardInfo3, cardInfo4));
        }
        home.setHotPosition(this.aiJobPositionService.getHomeList());
        home.setHotInterview(this.aiJobTrainService.getList());

        return home;
    }

    @Override
    public AiChatWiserVO initChat(ChatInitParams chatInitParams) {

        AiChatWiser aiChatWiser = new AiChatWiser();
        aiChatWiser.setChatId(IdUtil.fastSimpleUUID());
        aiChatWiser.setUserId(UserInfoUtils.getCurrentUserId());
        aiChatWiser.setLastMsg("新对话");
        aiChatWiser.setWiserId(StringUtils.isNotBlank(chatInitParams.getWiserId()) ? chatInitParams.getWiserId() : ViewerAgentConstant.WISER_AGENT_ID);
        aiChatWiser.setCreateTime(new Date());
        aiChatWiser.setUpdateTime(new Date());
        this.baseMapper.insert(aiChatWiser);
        return AiChatWiserVO.toVO(aiChatWiser);
    }

    @Override
    public Flux<ChatMessage> stream(AiChatUserMsg aiChatUserMsg) {
        String userId = UserInfoUtils.getCurrentUserId();
        aiChatUserMsg.setUserId(userId);

        String msgChatId = aiChatUserMsg.getChatId();

        List<Message> history = aiChatMemoryService.getListAsMessages(msgChatId);

        //新增记忆
        aiChatMemoryService.addUserMemory(msgChatId, aiChatUserMsg);

        //更新消息
        this.updateWiserChat(aiChatUserMsg);

        ChatMessage chatMessage = new ChatMessage();
        AiChatWiserMemory aiChatWiserMemory = aiChatMemoryService.addAssistantMemory(msgChatId, chatMessage);
        chatMessage.setId(aiChatWiserMemory.getId().toString());
        chatMessage.setChatId(msgChatId);

        AgentHandlerContext agentHandlerContext = new AgentHandlerContext();
        agentHandlerContext.setReplyId(chatMessage.getId());
        agentHandlerContext.setAiChatUserMsg(aiChatUserMsg);
        agentHandlerContext.setMessages(history);

        Flux<ChatMessage> reply = aiWiserAgentFacade.stream(agentHandlerContext).map(x -> {
            x.setId(aiChatWiserMemory.getId().toString());
            chatMessage.setRole(x.getRole());
            if (StringUtils.isNotBlank(x.getMsg())) {
                if (chatMessage.getMsg() == null) {
                    chatMessage.setMsg(x.getMsg());
                } else {
                    chatMessage.setMsg(chatMessage.getMsg() + x.getMsg());
                }
            }
            if (x.getTips() != null) {
                chatMessage.setTips(x.getTips());
            }
            if (x.getCardInfos() != null && !x.getCardInfos().isEmpty()) {
                List<? extends CardInfo> cardInfoList = x.getCardInfos();
                List<CardInfo> cardInfos = new ArrayList<>(cardInfoList);
                chatMessage.setCardInfos(aiUserPreferencesService.cardInfoAddPreference(userId, cardInfos));
            }
            return x;
        }).takeUntil(x -> redisCache.get(chatMessage.getId()) != null);
        ChatMessage done = new ChatMessage("[DONE]");
        done.setId(chatMessage.getId());
        return Flux.concat(reply.doFinally(x -> {
            aiChatMemoryService
                    .updateAssistantMemory(chatMessage.getId(), chatMessage, agentHandlerContext.getReply().toString());
        }), Flux.just(done));
    }

    @Override
    public boolean stopStream(String msgId) {
        redisCache.put(msgId, "stop", 60L);
        return true;
    }

    private void updateWiserChat(AiChatUserMsg aiChatUserMsg) {
        QueryWrapper<AiChatWiser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiChatWiser::getChatId, aiChatUserMsg.getChatId());
        AiChatWiser aiChatWiser = this.baseMapper.selectOne(queryWrapper);
        if (aiChatWiser != null) {
            aiChatWiser.setLastMsg(aiChatUserMsg.getMsg());
            aiChatWiser.setUpdateTime(new Date());
            this.baseMapper.updateById(aiChatWiser);
        }
    }

    @Override
    public void messagesWrapper(String memoryId, List<ChatMessage> messages) {
        if (messages != null && !messages.isEmpty()) {
            for (int i = 0; i < messages.size(); i++) {
                ChatMessage msg = messages.get(i);
                msg.setId(memoryId);
                messages.set(i, msg);
            }
        }
    }

    @Override
    public List<ChatMessage> messages(String chatId) {
        List<AiChatWiserMemory> aiChatMemories = aiChatMemoryService.getList(chatId);
        return aiChatMemories.stream().map(x -> {
            ChatMessage chatMessage = JSONObject.parseObject(x.getContent(), ChatMessage.class);
            chatMessage.setRole(x.getType());
            chatMessage.setId(x.getId().toString());
            PreferenceStatusDTO preferenceStatusDTO = new PreferenceStatusDTO();
            preferenceStatusDTO.setThumb(x.getThumbsUp());
            preferenceStatusDTO.setStored(x.getCollect());
            chatMessage.setExtra(JSONObject.parseObject(JSONObject.toJSONString(preferenceStatusDTO)));
            chatMessage = aiUserPreferencesService.wrapWiserChatMessage(chatMessage);
            return aiAnswerQuestionService.wrapWiserChatMessage(chatMessage);
        }).toList();
    }

    @Override
    public List<AiChatRecord> chatRecords(String userId) {
        QueryWrapper<AiChatWiser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiChatWiser::getIsDel, WiserConstant.NOT_DELETE)
                .eq(AiChatWiser::getUserId, userId).orderByDesc(AiChatWiser::getUpdateTime);
        return this.baseMapper.selectList(queryWrapper).stream().map(x -> {
            AiChatRecord record = new AiChatRecord();
            record.setChatId(x.getChatId());
            record.setMsg(x.getLastMsg());
            record.setUpdateTime(x.getUpdateTime());
            return record;
        }).toList();
    }


    @Override
    public Page<AiChatRecord> historyPage(HistoryPageDTO historyPageDTO) {
        QueryWrapper<AiChatWiser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiChatWiser::getIsDel, WiserConstant.NOT_DELETE)
                .eq(AiChatWiser::getUserId, UserInfoUtils.getCurrentUserId())
                .orderByDesc(AiChatWiser::getUpdateTime);

        Page<AiChatWiser> aiChatMatcherPage = PageUtils.doPage(this, historyPageDTO, queryWrapper);

        return PageConvertUtils.convert(
                aiChatMatcherPage,
                AiChatRecord.class,
                (src, dest) -> {
                    dest.setChatId(src.getChatId());
                    dest.setMsg(src.getLastMsg());
                    dest.setUpdateTime(src.getUpdateTime());
                }
        );
    }

    @Override
    public AiChatWiser updateWiser(AiChatWiser wiser) {
        return null;
    }

    @Override
    public void batchLogicDelete(List<String> ids) {
//        this.baseMapper.batchLogicDelete(ids);
    }

    @Override
    public AiChatWiser getByConservationId(String conservationId) {
        return this.getOne(new LambdaQueryWrapper<AiChatWiser>().eq(AiChatWiser::getChatId, conservationId));
    }

    @Override
    @Transactional
    public void deleteChatRecords(String chatId) {
        this.baseMapper.update(new UpdateWrapper<AiChatWiser>().lambda().eq(AiChatWiser::getChatId, chatId).set(AiChatWiser::getIsDel, DELETED));
    }
}
