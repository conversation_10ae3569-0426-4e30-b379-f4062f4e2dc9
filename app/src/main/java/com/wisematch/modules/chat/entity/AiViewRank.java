package com.wisematch.modules.chat.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("ai_view_rank")
@Schema(description = "AI面试评分排行榜")
public class AiViewRank implements Serializable {

    public static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "roomId")
    private String roomId;

    @Schema(description = "报告名称")
    private String position;

    @Schema(description = "面试评分")
    private Integer score;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "头像")
    private String photo;

    @Schema(description = "更新日期")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Schema(description = "训练ID")
    private String trainId;
}
