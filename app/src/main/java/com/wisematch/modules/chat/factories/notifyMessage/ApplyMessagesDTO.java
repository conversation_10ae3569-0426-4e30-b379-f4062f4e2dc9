package com.wisematch.modules.chat.factories.notifyMessage;

import com.wisematch.modules.chat.model.AddApplyMessageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

@Data
public class ApplyMessagesDTO {


    @Schema(description = "标题")
    private String title;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "接收者id")
    private String receiverId;

    @Schema(description = "发送者id")
    private String senderId;

    @Schema(description = "来源业务id")
    private String sourceId;

    @Schema(description = "logo")
    private String logo;

    public static ApplyMessagesDTO getApplyMessagesDTO(AddApplyMessageDTO addApplyMessageDTO){
        ApplyMessagesDTO applyMessagesDTO = new ApplyMessagesDTO();
        BeanUtils.copyProperties(addApplyMessageDTO, applyMessagesDTO);
        return applyMessagesDTO;
    }

}
