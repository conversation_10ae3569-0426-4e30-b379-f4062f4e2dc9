package com.wisematch.modules.chat.handler.viewer;

import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatOptions;
import com.alibaba.fastjson.JSONObject;
import com.wisematch.modules.chat.agent.AgentContext;
import com.wisematch.modules.chat.agent.AgentFacade;
import com.wisematch.modules.chat.entity.AiChatIntention;
import com.wisematch.modules.chat.enums.BizSence;
import com.wisematch.modules.chat.enums.InterviewIntentionCode;
import com.wisematch.modules.chat.model.ChatReplyMsg;
import com.wisematch.modules.chat.service.AiAgentPoolService;
import com.wisematch.modules.chat.service.AiChatIntentionService;
import com.wisematch.modules.chat.enums.ViewerAgentConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.concurrent.CompletableFuture;

/**
 * 记忆存储
 * <AUTHOR>
 * @version IAgentHandler.java, v0.1 2025-07-15 16:42
 */
@Component
@Slf4j
public class ViewIntentionAgentHandler implements IViewAgentHandler {

    IViewAgentHandler iViewAgentHandler;

    @Autowired
    AiAgentPoolService aiAgentPoolService;

    @Autowired
    AiChatIntentionService aiChatIntentionService;

    @Override
    public void setNext(IViewAgentHandler iAgentHandler) {
        this.iViewAgentHandler = iAgentHandler;
    }

    private final ChatClient chatClient;
    @Autowired
    AgentFacade agentFacade;


    public ViewIntentionAgentHandler(ChatClient.Builder builder) {
        this.chatClient = builder.build();
    }

    @Override
    public Flux<ChatReplyMsg> handle(ViewHandlerContext context) {
//        AiChatIntention intention = this.intention(context, context.getUserMsg(), JSONObject.toJSONString(context.getChatViewMsg().getMessages()));
        context.setIntention(this.supplyAsync(context));
        if (context.getIntention() != null && StringUtils.isNotBlank(context.getIntention().getAgentId())) {
            context.setAiAgentPool(this.aiAgentPoolService.getById(context.getIntention().getAgentId()));
        }
        return iViewAgentHandler.handle(context);
    }

    /**
     * 意图识别2.0
     * @param context
     * @return
     */
    private AiChatIntention supplyAsync(ViewHandlerContext context){
        log.info("intention start");
        AgentContext agentContext = new AgentContext();
        agentContext.setAiAgentPool(agentFacade.getByCode(ViewerAgentConstant.INTERVIEW_INTENTION_ASK));
        agentContext.setPrompt(agentContext.initPrompt(agentFacade
                .getPrompt(ViewerAgentConstant.INTERVIEW_INTENTION_ASK), context));
        agentContext.setUserMsg(context.getUserMsg());
        CompletableFuture<String> intentionA = agentFacade.supplyAsync(agentContext);

        AgentContext agentContext2 = new AgentContext();
        agentContext2.setAiAgentPool(agentFacade.getByCode(ViewerAgentConstant.INTERVIEW_INTENTION_OTHER));
        agentContext2.setPrompt(agentContext.initPrompt(agentFacade
                .getPrompt(ViewerAgentConstant.INTERVIEW_INTENTION_OTHER), context));
        agentContext2.setUserMsg(context.getUserMsg());
        CompletableFuture<String> intentionB = agentFacade.supplyAsync(agentContext2);

        CompletableFuture<AiChatIntention> intention = intentionA.thenCombine(intentionB, (replyA, replyB) -> {
            log.info("intention replyA: {}, replyB: {}", replyA, replyB);
            if (InterviewIntentionCode.INT_99.getCode().equals(replyB)) {
                return this.aiChatIntentionService.getIntention(replyA, BizSence.interview.name());
            } else {
                return this.aiChatIntentionService.getIntention(replyB, BizSence.interview.name());
            }
        });
        try {
            return intention.get();
        } catch (Exception e) {
            log.error("intention get error", e);
        }
        return null;
    }

    /**
     * 默认1.0意图识别
     * @param userMsg
     * @param history
     * @return
     */
    private AiChatIntention intention(ViewHandlerContext context, String userMsg, String history) {
        long start = System.currentTimeMillis();
        log.info("interview intention start, chatId: {}", context.getRoomId());
        try {
            DashScopeChatOptions customOptions = DashScopeChatOptions.builder()
                    .withTemperature(0.2)
                    .withModel("qwen-turbo-latest")
                    .build();

            String prompt = aiAgentPoolService.getPromptById(ViewerAgentConstant.INTERVIEW_INTENTION_AGENT_ID);

            prompt = prompt.replace("$history", history).replace("$userMsg", userMsg);

            prompt = prompt.replace("$question", JSONObject.toJSONString(context.getQuestion()));

            String reply = chatClient.prompt(new Prompt(prompt, customOptions))
                    .user(userMsg).call().content();

            log.info("interview intetion end. reply: {}, cost: {} ms", reply, (System.currentTimeMillis() - start));
            return this.aiChatIntentionService.getIntention(reply, BizSence.interview.name());
        } catch (Exception e) {
            log.error("interview intention error", e);
        }
        return null;
    }
}
