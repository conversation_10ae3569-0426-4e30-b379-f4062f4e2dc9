package com.wisematch.modules.chat.convertor;

import com.wisematch.modules.chat.entity.AiViewPortrait;
import com.wisematch.modules.chat.entity.AiViewPrepare;
import com.wisematch.modules.chat.model.PortraitRecommendDTO;

import java.util.Map;

public class PortraitRecommendDTOConvertor {

    public static PortraitRecommendDTO convert(AiViewPortrait portrait, Map<String, AiViewPrepare> roomPrepareMap) {
        PortraitRecommendDTO dto = new PortraitRecommendDTO();
        String roomId = portrait.getRoomId();
        dto.setRecommendTalentsId(portrait.getId());

        // 从AiViewPortrait获取肖像信息（假设字段名为portrait）
        dto.setPortrait(portrait.getPortrait());

        // 关联AiViewPrepare获取用户ID和简历
        AiViewPrepare prepare = roomPrepareMap.get(roomId);
        if (prepare != null) {
            dto.setResume(prepare.getResume()); // 直接取简历信息
        }

        return dto;

    }
}
