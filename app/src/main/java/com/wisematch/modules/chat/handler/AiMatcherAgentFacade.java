package com.wisematch.modules.chat.handler;

import com.wisematch.modules.chat.handler.matcher.MatcherIntentionHandler;
import com.wisematch.modules.chat.handler.matcher.MatcherReplyAgentHandler;
import com.wisematch.modules.chat.handler.matcher.MatcherTalentSuggestHandler;
import com.wisematch.modules.chat.model.ChatMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

/**
 * Wiser Agent 聊天门面
 * <AUTHOR>
 * @version IntentionAgentFacade.java, v0.1 2025-07-11 11:02
 */
@Component
@Slf4j
public class AiMatcherAgentFacade implements InitializingBean {

    @Autowired
    private MatcherIntentionHandler matcherIntentionHandler;
    @Autowired
    private MatcherReplyAgentHandler matcherReplyAgentHandler;
    @Autowired
    private MatcherTalentSuggestHandler matcherTalentSuggestHandler;

    @Override
    public void afterPropertiesSet() {
        matcherIntentionHandler.setNext(matcherReplyAgentHandler);
        matcherReplyAgentHandler.setNext(matcherTalentSuggestHandler);
    }

    public Flux<ChatMessage> stream(AgentHandlerContext agentHandlerContext){
        log.info("matcher reply start. chatId:{}", agentHandlerContext.getAiChatUserMsg().getChatId());
        return matcherIntentionHandler.handle(agentHandlerContext);
    }

}
