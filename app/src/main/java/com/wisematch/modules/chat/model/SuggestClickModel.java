package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SuggestClickModel {

    @Schema(description = "memoryId，消息必传")
    private String memoryId;

    @Schema(description = "收藏状态：1收藏，必传")
    private Integer collect;

    @Schema(description = "点赞点踩状态：1点赞，-1点踩，必传")
    private Integer thumbsUp;

    @Schema(description = "必传，业务场景： " +
            "WISER_AGENT:WISER用户对agent回复的反馈，" +
            "MATCHER_AGENT:MATCHER_用户对agent回复的反馈，" +
            "WISER_POSITION：用户对推荐职位的反馈，" +
            "WISER_INTERVIEW:用户对推荐面试的反馈，" +
            "MATCHER_USER:企业对推荐用户的反馈")
    private String bizSence;//业务场景： WISER_AGENT:WISER用户对agent回复的反馈，MATCHER_AGENT:MATCHER_用户对agent回复的反馈，USER_POSITION,用户对推荐职位的反馈:USER_INTERVIEW:用户对推荐面试的反馈，COMPANY_USER:企业对推荐用户的反馈

    @Schema(description = "关联cardInfo的id，card必传")
    String recommendId;

}
