package com.wisematch.modules.chat.model;


import com.wisematch.common.model.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AiUserPreferencesQueryDTO implements PageRequest {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;

    @Schema(description = "目标ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String targetId;

    @Schema(description = "收藏状态，1：收藏", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer collect;

    @Schema(description = "点赞状态，状态:-1点踩，1点赞", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer thumbsUp;

    @Schema(description = "必传，业务场景： " +
            "WISER_REPLY:WISER用户对agent回复的反馈，" +
            "MATCHER_REPLY:MATCHER_用户对agent回复的反馈，" +
            "POSITION：用户对推荐职位的反馈，" +
            "INTERVIEW:用户对推荐面试的反馈，" +
            "MATCHER_USER:企业对推荐用户的反馈", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bizSence;

    @Schema(description = "起始页", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer pageNum = 1;

    @Schema(description = "页面大小", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer pageSize = 10;
}

