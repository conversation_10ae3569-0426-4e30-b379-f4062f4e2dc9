package com.wisematch.modules.chat.model;

import com.wisematch.modules.chat.enums.ApplyType;
import com.wisematch.modules.chat.enums.PlatformType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version ChatInitParams.java, v0.1 2025-06-20 16:39
 */
@Data
public class ApplyViewParams {


    /**
     * 岗位ID
     */
    public String applyId;

    /**
     * 申请人姓名
     */
    public String applyName;

    /**
     * 申请人手机
     */
    public String applyPhone;

    /**
     * 申请人邮箱
     */
    public String applyEmail;

    /**
     * 申请类型，岗位面试-POSITION，模拟面试-TRAIN
     */
    @Schema(description = "申请类型，岗位面试-POSITION，模拟面试-TRAIN")
    public String applyType = ApplyType.POSITION.name();

    /**
     * 新增参数
     */
    @Schema(description = "平台类型：PC/APP/H5")
    public String platform = PlatformType.PC.name();

    /**
     * 申请信息，JSON字符串
     */
    public String applyInfos;

}
