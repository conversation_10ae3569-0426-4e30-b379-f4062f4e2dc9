package com.wisematch.modules.chat.wrapper;

import com.wisematch.modules.chat.utils.MatcherAgentConstant;
import com.wisematch.modules.chat.wrapper.matcher.AiTalentSuggentWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version WiserWrapper.java, v0.1 2025-08-05 13:38
 */
@Component
@Slf4j
public class MatcherWrapper extends AbstractWiseMatchWrapper {
    @Autowired
    private AiTalentSuggentWrapper aiTalentSuggentWrapper;

    public IWiseMatchWrapper wrap(String agentCode) {
        if (!StringUtils.isNotBlank(agentCode)) {
            return this;
        }
        switch (agentCode) {
            case MatcherAgentConstant.MATCHER_TALENT_RECOMMEDN -> {
                return aiTalentSuggentWrapper;
            }
            default -> {
                return this;
            }
        }
    }

}
