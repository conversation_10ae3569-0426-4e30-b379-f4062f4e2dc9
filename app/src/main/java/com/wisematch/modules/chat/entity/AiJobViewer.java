package com.wisematch.modules.chat.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ai_job_viewer")
@Schema(description = "发布的面试官")
public class AiJobViewer {

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "名字")
    private String name;

    @Schema(description = "头像")
    private String photo;

    @Schema(description = "声音代码")
    private String voice;

    @Schema(description = "结束语")
    private String conclusion;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "用户Id")
    private String userId;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Schema(description = "所在组织Id")
    private String orgId;

    @Schema(description = "是否删除")
    private Integer isDel;

    @Schema(description = "使用场景")
    private String bizScene;

    @Schema(description = "面试官描述")
    private String viewerDesc;

    @Schema(description = "开场白")
    private String welcome;

    @Schema(description = "说话状态")
    private String speakUrl;

    @Schema(description = "倾听状态")
    private String listenUrl;
}
