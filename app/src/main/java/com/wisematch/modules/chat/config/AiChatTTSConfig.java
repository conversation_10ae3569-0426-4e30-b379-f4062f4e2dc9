package com.wisematch.modules.chat.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @version AiChatASRConfig.java, v0.1 2025-06-24 22:55
 */
@Data
public class AiChatTTSConfig {

    @JSONField(name = "Provider")
    String Provider = "volcano_bidirection";

    @JSONField(name = "ProviderParams")
    JSONObject ProviderParams = new JSONObject();


    {
        JSONObject app = new JSONObject();
        app.put("appid","**********");
        app.put("token","M_DO1jpElFo63yeP6roC0vc18_J6lYxq");
        ProviderParams.put("app",app);
        ProviderParams.put("ResourceId","volc.service_type.10029");
        JSONObject audio = new JSONObject();
        audio.put("voice_type","zh_female_qingxinnvsheng_mars_bigtts");
        audio.put("speech_rate",0);
        audio.put("pitch_rate",0);
        ProviderParams.put("audio",audio);
    }
}
