package com.wisematch.modules.chat.controller;

import com.wisematch.common.annotation.SysLog;
import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.common.service.AiSysConfigService;
import com.wisematch.modules.oss.enums.FileType;
import com.wisematch.modules.oss.service.SysOssService;
import com.wisematch.modules.oss.utils.OssProxyUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version AiChatController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/common")
@Tag(name = "通用cotroller", description = "通用cotroller")
public class CommonController {

    @Autowired
    private SysOssService sysOssService;

    @Autowired
    private AiSysConfigService aiSysConfigService;

    /**
     * 获取系统配置参数
     */
    @Operation(summary = "系统配置参数")
    @GetMapping(value = "/sys/config")
    public R sysConfig(){
        return R.ok().setData(aiSysConfigService.getSysConfig());
    }

    /**
     * 上传文件
     */
    @Operation(summary = "同步上传图片")
    @SysLog("同步上传图片")
    @NotDoubleSubmit
    @PostMapping(value = "/uploadProfile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R upload(@RequestParam("file") MultipartFile file){
        return R.ok().setData(sysOssService.ossUpload(file, UserInfoUtils.getCurrentUserId(), FileType.PHOTO.name()));
    }

    // 代理PDF请求
    @GetMapping("/preview")
    @Operation(summary = "暂时支持pdf和图片的预览")
    public void proxyPdf(@RequestParam("url") String ossUrl,
                         HttpServletResponse response) throws IOException {
        OssProxyUtil.proxyFile(ossUrl, response);
    }
//
//    @GetMapping("/doc")
//    @Anonymous
//    public void proxyDocx(@RequestParam("url") String ossUrl, HttpServletResponse response) throws IOException {
//        String googleDocsViewerUrl = "https://view.officeapps.live.com/op/embed.aspx?src=" + URLEncoder.encode(ossUrl, "UTF-8");
//        response.sendRedirect(googleDocsViewerUrl);
//    }

}
