package com.wisematch.modules.chat.model;

import com.wisematch.common.model.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CandidatesDTO implements PageRequest {

    @Schema(description = "搜索条件")
    private String search;

    @Schema(description = "页码")
    private Integer pageNum = 1;

    @Schema(description = "每页数量")
    private Integer pageSize = 100;

    @Schema(description = "职位ID")
    private String positionId;

}
