package com.wisematch.modules.chat.handler;

import com.wisematch.modules.chat.handler.viewer.*;
import com.wisematch.modules.chat.model.ChatReplyMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

/**
 * AI面试官门面,智能体编排
 * <AUTHOR>
 * @version AiInterviewAgentFacade.java, v0.1 2025-07-11 11:02
 */
@Component
@Slf4j
public class AiInterviewAgentFacade {

    @Autowired
    private ViewPrepareAgentHandler viewPrepareAgentHandler;
    @Autowired
    private ViewMemoryAgentHandler viewMemoryAgentHandler;
    @Autowired
    private ViewIntentionAgentHandler viewIntentionAgentHandler;
    @Autowired
    private ViewBizReplyHandler viewBizReplyHandler;
    @Autowired
    private ViewAgentReplyHandler viewAgentReplyHandler;

    private void init(){
        this.viewPrepareAgentHandler.setNext(viewMemoryAgentHandler);
        this.viewMemoryAgentHandler.setNext(viewIntentionAgentHandler);
        this.viewIntentionAgentHandler.setNext(viewBizReplyHandler);
        this.viewBizReplyHandler.setNext(viewAgentReplyHandler);
    }
    public Flux<ChatReplyMsg> stream(ViewHandlerContext context){
        this.init();
        return viewPrepareAgentHandler.handle(context);
    }

}
