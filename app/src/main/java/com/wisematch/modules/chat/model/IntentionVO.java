package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 意图识别交互数据
 */
@Data
public class IntentionVO {

    /**
     * 意图识别码
     */
    @Schema(description = "意图识别码")
    String code;

    /**
     * 消息-可选
     */
    @Schema(description = "消息-可选")
    String msg;

    /**
     * agentId
     */
    String agentId;

    /**
     * 智能体码
     */
    String agentCode;

    String name;

}
