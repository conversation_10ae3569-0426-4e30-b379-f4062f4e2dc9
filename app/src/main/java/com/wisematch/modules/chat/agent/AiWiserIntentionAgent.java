package com.wisematch.modules.chat.agent;

import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatOptions;
import com.alibaba.fastjson.JSONObject;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.modules.chat.entity.AiAgentPool;
import com.wisematch.modules.chat.entity.AiChatIntention;
import com.wisematch.modules.chat.enums.IntentionBizScene;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.model.IntentionVO;
import com.wisematch.modules.chat.model.Message;
import com.wisematch.modules.chat.service.AiAgentPoolService;
import com.wisematch.modules.chat.service.AiChatIntentionService;
import com.wisematch.modules.chat.service.AiChatWiserMemoryService;
import com.wisematch.modules.chat.enums.ViewerAgentConstant;
import com.wisematch.modules.chat.enums.WiserAgentConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 意图识别agent门面
 * <AUTHOR>
 * @version IntentionAgentFacade.java, v0.1 2025-07-11 11:02
 */
@Component
@Slf4j
public class AiWiserIntentionAgent {

    @Autowired
    private AiAgentPoolService aiAgentPoolService;
    @Autowired
    private AiChatIntentionService aiChatIntentionService;
    private final ChatClient chatClient;
    @Autowired
    private AiChatWiserMemoryService aiChatWiserMemoryService;

    @Autowired
    AgentFacade agentFacade;

    public AiWiserIntentionAgent(ChatClient.Builder builder) {
        this.chatClient = builder.build();
    }

    /**
     * 意图识别
     * @param userMsg
     * @param messages
     * @return
     */
    public IntentionVO getIntention(String userMsg, List<Message> messages){
        long start = System.currentTimeMillis();
        AiAgentPool intention = aiAgentPoolService.getById(ViewerAgentConstant.WISER_INTENTION_AGENT_ID);
        log.info("wiser intention agentName: {}, agentId: {}, model: {}", intention.getAgentName(), intention.getId(), intention.getModel());

        String prompt = intention.getPrompt();
        prompt = prompt.replace("$history",JSONObject.toJSONString(messages));
        prompt = prompt.replace("$userInput",userMsg);

        DashScopeChatOptions customOptions = DashScopeChatOptions.builder()
                .withTemperature(intention.getTemperature())
                .withModel(intention.getModel())
                .build();

        String reply =  this.chatClient.prompt(new Prompt(prompt, customOptions))
                .user(userMsg)
                .call().content();
        IntentionVO intentionVO = new IntentionVO();
        try {
            AiChatIntention aiChatIntention = aiChatIntentionService.getIntention(reply, IntentionBizScene.wiser.name());
            intentionVO.setCode(aiChatIntention != null ? aiChatIntention.getCode() : ViewerAgentConstant.WISER_AGENT_CODE);
            intentionVO.setAgentId(aiChatIntention != null ? aiChatIntention.getAgentId() : ViewerAgentConstant.WISER_AGENT_ID);
            log.info("intention reply:{},agentId:{}", reply, intentionVO.getAgentId());
        }catch (Exception e){
            log.error("意图识别异常,reply={}", reply, e);
            throw new RRException(RRExceptionEnum.AI_SERVICE_ERROR);
        }
        log.info("意图识别耗时:" + (System.currentTimeMillis() - start) + "ms");
        return intentionVO;
    }

    /**
     * 意图识别
     * @param userMsg
     * @return
     */
    public IntentionVO streamIntention(AgentHandlerContext context, String userMsg){
        long start = System.currentTimeMillis();
        AiAgentPool intention = aiAgentPoolService.getByCode(WiserAgentConstant.WISER_INTENTION_NEW);
        log.info("wiser intention agentName: {}, agentId: {}, model: {}", intention.getAgentName(), intention.getId(), intention.getModel());

        String prompt = intention.getPrompt();
        prompt = prompt.replace("$userInput",userMsg);

        AgentContext agentContext = new AgentContext();
        agentContext.setPrompt(prompt);
        agentContext.setUserMsg(userMsg);
        agentContext.setAgentCode(WiserAgentConstant.WISER_INTENTION_NEW);
        agentContext.setHistory(context.getMessages());
        String reply =  agentFacade.supply(agentContext);
        IntentionVO intentionVO = new IntentionVO();
        try {
            AiChatIntention aiChatIntention = aiChatIntentionService.getIntention(reply, IntentionBizScene.wiser.name());
            if (aiChatIntention == null) {
                intentionVO.setAgentCode(WiserAgentConstant.DEFAULT_WISER);
                log.info("intention reply:{},agentCode:{}", reply, WiserAgentConstant.DEFAULT_WISER);
            } else {
                intentionVO.setCode(aiChatIntention.getCode());
                intentionVO.setAgentId(aiChatIntention.getAgentId());
                intentionVO.setName(aiChatIntention.getIntention());
                intentionVO.setMsg(reply);
                log.info("intention reply:{},agentCode:{}", reply, intentionVO.getAgentId());
            }
        }catch (Exception e){
            log.error("意图识别异常,reply={}", reply, e);
            throw new RRException(RRExceptionEnum.AI_SERVICE_ERROR);
        }
        log.info("意图识别耗时:" + (System.currentTimeMillis() - start) + "ms");
        return intentionVO;
    }

}
