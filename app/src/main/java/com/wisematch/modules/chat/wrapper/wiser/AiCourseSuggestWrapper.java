
package com.wisematch.modules.chat.wrapper.wiser;

import com.wisematch.modules.chat.enums.CardType;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.service.AiUserResumeService;
import com.wisematch.modules.chat.wrapper.AbstractWiseMatchWrapper;
import com.wisematch.modules.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
public class AiCourseSuggestWrapper extends AbstractWiseMatchWrapper {

    @Autowired
    private AiUserResumeService aiUserResumeService;

    @Override
    public String promptWrap(AgentHandlerContext context, String prompt) {
        if (prompt.contains("$courseDocuments")) {
            prompt = prompt.replace("$courseDocuments", JsonUtils.toJson(getCards()));
        }
        if (prompt.contains("$resume")) {
            prompt = prompt.replace("$resume", aiUserResumeService.getCurrentOpenedOrText(context.getAiChatUserMsg().getUserId()));
        }
        return super.promptWrap(context, prompt);
    }

    @Override
    public ChatMessage replyWrap(ChatMessage chatMessage) {
        chatMessage.setCardInfos(getCards());
        return super.replyWrap(chatMessage);
    }

    public static List<CardInfo> getCards(){

        return Arrays.asList(
                // 电商运营基础面试课
                new CardInfo() {{
                    setId("card_001");
                    setTitle("电商运营面试黄金30问");
                    setLogo("https://cn.bing.com/images/search?view=detailV2&ccid=drG1EJWG&id=13276276E3AE1988E67368C33676CB64D9B7B102&thid=OIP.drG1EJWGPmZYkRoIuTnkmAHaE8&mediaurl=https%3a%2f%2fzeast.oss-cn-shanghai.aliyuncs.com%2fsite%2fAC0773067CF6BEC6.jpg&exph=534&expw=800&q=%e8%bf%90%e8%90%a5%e8%af%be%e7%a8%8b&simid=608050937987954136&FORM=IRPRST&ck=BEB1734A7597BC408FED06DA7A355283&selectedIndex=11&itb=0");
                    setCardType(CardType.COURSE.name());
                    setStatus("published");
                    setSummary("掌握电商运营岗位高频面试题解析，涵盖流量转化、活动策划等核心场景");
                    setKeyLabel("热门");
                    setLabels("电商基础,面试技巧,应届生必备");
                    setAgentId("agent_edu_001");
                    setViewId("interviewer_005");
                    setUserId("user_789");
                }},

                // 数据化运营专项课
                new CardInfo() {{
                    setId("card_002");
                    setTitle("电商数据分析师面试实战");
                    setLogo("https://cn.bing.com/images/search?view=detailV2&ccid=K55G8uIx&id=21A3B81B17DF5D321B154DF3F9731F28D629135A&thid=OIP.K55G8uIxzJWJZmlZRMSirQHaLH&mediaurl=https%3a%2f%2fimg.tukuppt.com%2fpreview%2f00%2f36%2f78%2f57%2f367857644b6dcccb94fshow.jpg&exph=1170&expw=780&q=%e8%bf%90%e8%90%a5%e8%af%be%e7%a8%8b&simid=608027989995298875&FORM=IRPRST&ck=C563465A31F2B60CD83911ACEF15A724&selectedIndex=25&itb=0");
                    setCardType(CardType.COURSE.name());
                    setSummary("从SQL到GMV分析，拆解大厂电商数据分析岗面试全流程");
                    setKeyLabel("进阶");
                    setLabels("数据分析,SQL,业务洞察");
                    setAgentId("agent_tech_003");
                    setViewId("interviewer_012");
                    setUserId("user_123");
                }},

                // 跨境电商模拟面试
                new CardInfo() {{
                    setId("card_003");
                    setTitle("亚马逊运营专家模拟面试");
                    setLogo("https://cn.bing.com/images/search?view=detailV2&ccid=YRnby8yp&id=EB4CEA261A969021CA9E5395B57D2FD791804DD8&thid=OIP.YRnby8ypP6n6d6YKz0DSKgHaKd&mediaurl=https%3a%2f%2fpic.616pic.com%2fad_preview%2f00%2f16%2f68%2f647a0dfd89a4c.jpg-0.jpg!%2ffw%2f800%2fquality%2f90%2funsharp%2ftrue%2fcompress%2ftrue%3f%3e&exph=1130&expw=800&q=%e8%bf%90%e8%90%a5%e8%af%be%e7%a8%8b&simid=608000940260030593&FORM=IRPRST&ck=2CDA7007B9E510A65BF22D397010A640&selectedIndex=51&itb=0");
                    setCardType(CardType.COURSE.name());
                    setSummary("针对跨境电商平台的运营策略、广告投放等专项训练");
                    setKeyLabel("跨境");
                    setLabels("亚马逊,广告投放,海外市场");
                    setAgentId("agent_global_002");
                    setViewId("interviewer_008");
                    setUserId("user_456");
                }},

                // 直播电商场景题
                new CardInfo() {{
                    setId("card_004");
                    setTitle("直播电商运营高频场景题");
                    setLogo("https://cn.bing.com/images/search?view=detailV2&ccid=HoC3nELc&id=51629C9DA52005FFF24A4536D7CE761A63B59514&thid=OIP.HoC3nELceHwkjhDBzWbLkwHaE2&mediaurl=https%3a%2f%2fts1.tc.mm.bing.net%2fth%2fid%2fR-C.********************************%3frik%3dFJW1Yxp2ztc2RQ%26riu%3dhttp%253a%252f%252ffbimg.fangxinxue.net%252fplan%252f202205%252f05%252f165171585699029.jpg%26ehk%3dJq9k84E%252bajuSyE5XNadgfjJD9o%252bQT1W6OOumh%252fWgfl4%253d%26risl%3d%26pid%3dImgRaw%26r%3d0&exph=459&expw=701&q=%e8%bf%90%e8%90%a5%e8%af%be%e7%a8%8b&simid=608031816772249297&FORM=IRPRST&ck=779BF216F15905D6D1C7779EBB3AAA9D&selectedIndex=75&itb=0");
                    setCardType(CardType.COURSE.name());
                    setSummary("抖音/快手直播间的流量获取、转化率提升等实战案例分析");
                    setKeyLabel("新趋势");
                    setLabels("直播运营,流量转化,ROI优化");
                    setAgentId("agent_live_004");
                    setViewId("interviewer_015");
                    setUserId("user_101");
                }}
        );
    }



}
