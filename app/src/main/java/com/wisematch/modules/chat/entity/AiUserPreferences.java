package com.wisematch.modules.chat.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wisematch.modules.chat.model.PreferenceStatusDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("ai_user_preferences")
@Schema(description = "用户偏好行为")
@NoArgsConstructor
public class AiUserPreferences implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "目标ID")
    private String targetId;

    @Schema(description = "是否查看，0：未查看，1查看")
    private Integer viewStatus = 0;

    @Schema(description = "收藏状态，1：收藏")
    //1：收藏
    private Integer collect = 0;

    @Schema(description = "点赞状态，状态:-1点踩，1点赞")
    //状态:-1点踩，1点赞
    private Integer thumbsUp = 0;

    @Schema(description = "业务场景： WISER_AGENT:WISER用户对agent回复的反馈，MATCHER_AGENT:MATCHER_用户对agent回复的反馈，USER_POSITION," +
            "用户对推荐职位的反馈:USER_INTERVIEW:用户对推荐面试的反馈，COMPANY_USER:企业对推荐用户的反馈")
    //业务场景： WISER_AGENT:WISER用户对agent回复的反馈，MATCHER_AGENT:MATCHER_用户对agent回复的反馈，USER_POSITION,用户对推荐职位的反馈:USER_INTERVIEW:用户对推荐面试的反馈，COMPANY_USER:企业对推荐用户的反馈
    private String bizSence;
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间")
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "更新时间")
    private Date updateTime;

    public AiUserPreferences(String userId, String targetId, String bizSence) {
        this.userId = userId;
        this.targetId = targetId;
        this.bizSence = bizSence;
    }

    public static PreferenceStatusDTO toPreferenceStatusDTO(AiUserPreferences aiUserPreferences){
        PreferenceStatusDTO preferenceStatusDTO = new PreferenceStatusDTO();
        if(null == aiUserPreferences){
            return new PreferenceStatusDTO();
        }
        preferenceStatusDTO.setViewStatus(aiUserPreferences.getViewStatus());
        preferenceStatusDTO.setThumb(aiUserPreferences.getThumbsUp());
        preferenceStatusDTO.setStored(aiUserPreferences.getCollect());
        return preferenceStatusDTO;
    }
}

