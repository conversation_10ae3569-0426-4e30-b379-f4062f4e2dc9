package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class RecommendClickVO  extends Message{

    String chatId;

    @Schema(description = "名字")
    String name;

    @Schema(description = "照片")
    String photo;

    /**
     * 用户提示建议
     */
    @Schema(description = "用户提示建议")
    List<String> tips;

    /**
     * 人才推荐卡片
     */
    @Schema(description = "人才推荐卡片")
    List<CardInfoClick> cardInfos;

    @Schema(description = "时间")
    Date dateTime;

    @Schema(description = "收藏状态")
    private Integer collect;

    @Schema(description = "点赞点踩状态")
    private Integer thumbsUp;

}
