package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiJobViewer;
import com.wisematch.modules.chat.model.AiJobViewerQueryDTO;

import java.util.List;


/**
 * AI智能体服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */

public interface AiJobViewerService extends IService<AiJobViewer> {
    Page<AiJobViewer> pageQuery(AiJobViewerQueryDTO queryDTO);

    void batchLogicDelete(List<String> ids);

    void logicDelete(String ids);

    List<AiJobViewer> listAll(AiJobViewerQueryDTO queryDTO);

    List<String> defaultPhoto();

    void createObj(AiJobViewer aiJobViewer);
}
