package com.wisematch.modules.chat.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 经验要求枚举（原工作经验）
 */
@Getter
public enum ExperienceRequirement {
    // 无经验
    NO_EXPERIENCE(0, "经验不限"),
    // 1年以内
    LESS_THAN_1_YEAR(1, "1年以内"),
    // 1-3年
    ONE_TO_THREE_YEARS(2, "1-3年"),
    // 3-5年
    THREE_TO_FIVE_YEARS(3, "3-5年"),
    // 5-10年
    FIVE_TO_TEN_YEARS(4, "5-10年"),
    // 10年以上
    MORE_THAN_TEN_YEARS(5, "10年以上");

    private final int code;
    private final String name;

    ExperienceRequirement(int code, String name) {
        this.code = code;
        this.name = name;
    }

    // 根据code获取枚举实例
    public static ExperienceRequirement getByCode(int code) {
        for (ExperienceRequirement requirement : values()) {
            if (requirement.code == code) {
                return requirement;
            }
        }
        throw new IllegalArgumentException("无效的经验要求code：" + code);
    }

    // 根据name获取枚举实例（便于查询转换）
    public static ExperienceRequirement getByName(String name) {
        for (ExperienceRequirement type : values()) {
            if (Objects.equals(name, type.getName())) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的经验要求name：" + name);
    }
}
