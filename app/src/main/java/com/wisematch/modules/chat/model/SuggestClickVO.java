package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SuggestClickVO extends Message{

    @Schema(description = "收藏状态")
    private Integer collect;

    @Schema(description = "点赞点踩状态")
    private Integer thumbsUp;

    String agentId;

    String chatId;

    String userId;

    String name;

    Boolean enableThinking;

    String photo;

    /**
     * 用户提示建议
     */
    @Schema(description = "用户提示建议")
    List<String> tips;

    /**
     * 人才推荐卡片
     */
    @Schema(description = "人才推荐卡片")
    List<CardInfoClick> cardInfos;

    Date dateTime;

}
