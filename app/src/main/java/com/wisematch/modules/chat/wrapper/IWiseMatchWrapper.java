package com.wisematch.modules.chat.wrapper;

import com.wisematch.modules.chat.enums.MsgRole;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.utils.JsonExtractor;
import org.apache.commons.lang.StringUtils;
import reactor.core.publisher.Flux;

import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;

public interface IWiseMatchWrapper {

    default String promptWrap(AgentHandlerContext context, String prompt) {
        return prompt;
    }


    default ChatMessage replyWrap(ChatMessage chatMessage){
        return chatMessage;
    }

    default Flux<ChatMessage> format(String chatId, Flux<String> reply) {
        StringBuffer stringBuffer = new StringBuffer();
        AtomicInteger atomicInteger = new AtomicInteger(0);
        AtomicInteger eof = new AtomicInteger(0);
        return reply.map(x -> {
            for (int i = 0; i < x.length(); i++) {
                if (x.charAt(i) == '=') {
                    eof.addAndGet(1);
                }
            }
            if (eof.get() > 0 && x.contains("{")) {
                atomicInteger.addAndGet(1);
            }
            if (atomicInteger.get() > 0) {
                stringBuffer.append(x);
                if (x.contains("}")) {
                    atomicInteger.addAndGet(-1);
                }
                ChatMessage replyViewMessage = new ChatMessage();
                if (x.contains("=")) {
                    replyViewMessage.setMsg(x.substring(0, x.indexOf("=")));
                    replyViewMessage.setRole(MsgRole.assistant.name());
                    replyViewMessage.setChatId(chatId);
                    replyViewMessage.setDateTime(new Date());
                }
                return replyViewMessage;
            } else {
                ChatMessage replyViewMessage = new ChatMessage();
                replyViewMessage.setMsg(x.replaceAll("=", "").replaceAll("\n\n", "\n").replaceAll("\n\n\n", "\n"));
                replyViewMessage.setRole(MsgRole.assistant.name());
                replyViewMessage.setChatId(chatId);
                replyViewMessage.setDateTime(new Date());
                return replyViewMessage;
            }
        }).filter(x -> StringUtils.isNotBlank(x.getMsg())).concatWith(Flux.defer(() -> {
            // If there's any leftover in the buffer, append it as-is
            if (!stringBuffer.isEmpty() && atomicInteger.get() == 0) {
                ChatMessage replyViewMessage = new ChatMessage();
                String jsonText = JsonExtractor.extractJSON(stringBuffer.toString());
                replyViewMessage.setMsg(jsonText);
                replyViewMessage.setRole(MsgRole.assistant.name());
                replyViewMessage.setChatId(chatId);
                replyViewMessage.setDateTime(new Date());
                return Flux.just(this.replyWrap(replyViewMessage));
            }
            return Flux.empty();
        }));
    }
}
