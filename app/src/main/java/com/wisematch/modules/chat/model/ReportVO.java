package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Schema(description = "面试报告")
public class ReportVO {

    private String userId;

    private String roomId;

    @Schema(description = "职位")
    private String position;

    private String videoUrl;

    @Schema(description = "时长")
    private Long duration;

    private Date startTime;

    @Schema(description = "分数")
    private Integer score;
    /**
     * 面试问答
     */
    @Schema(description = "面试问答")
    private String messages;

    /**
     * 针对messages的重点划线文本列表
     */
    @Schema(description = "针对messages的重点划线文本列表")
    private List<String> underlines;

    /**
     * 维度标签
     */
    @Schema(description = "维度标签")
    private List<String> labels;

    /**
     * 考核维度
     */
    @Schema(description = "考核维度")
    private List<CommentMsg> dimensions;

}
