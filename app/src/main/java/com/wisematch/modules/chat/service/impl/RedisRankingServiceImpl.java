package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.entity.AiViewRank;
import com.wisematch.modules.chat.entity.AiViewRecord;
import com.wisematch.modules.chat.mapper.AiViewRankMapper;
import com.wisematch.modules.chat.mapper.AiViewRecordMapper;
import com.wisematch.modules.chat.model.AiViewRankWithRankVO;
import com.wisematch.modules.chat.model.AiViewUserRankVO;
import com.wisematch.modules.chat.model.GetRankListResponse;
import com.wisematch.modules.chat.service.AiViewRankService;
import com.wisematch.modules.chat.service.RedisRankingService;
import com.wisematch.modules.sys.constant.Constants;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.mapper.SysUserMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


@Service
@Slf4j
public class RedisRankingServiceImpl implements RedisRankingService {

    @Resource
    private AiViewRankMapper aiViewRankMapper;

    @Resource
    private AiViewRecordMapper aiViewRecordMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private AiViewRankService aiViewRankService;

    private static final String ANONYMOUS = "平台用户";

    @Override
    public GetRankListResponse getTopN(String currentUserId, String roomId, int page, int size, String position) {

        if (!StringUtils.hasLength(roomId)) {
            AiViewRank aiViewRank = aiViewRankMapper.selectOne(new LambdaQueryWrapper<AiViewRank>()
                    .eq(AiViewRank::getUserId, currentUserId)
                    .eq(AiViewRank::getPosition, position)
                    .orderByDesc(AiViewRank::getUpdateTime)
                    .last("LIMIT 1"));
            roomId = aiViewRank.getRoomId();
        }

        AiViewRecord aiViewRecord = aiViewRecordMapper.selectOne(new QueryWrapper<AiViewRecord>().lambda().eq(AiViewRecord::getRoomId, roomId));
        if (aiViewRecord == null) {
            return null;
        }

        String trainId = aiViewRecord.getTrainId();

        // 1. 获取当前职位下每个用户的最新排名数据
        List<AiViewRank> latestRecordsByUser = getLatestByTrainId(trainId);

        // 2. 计算总记录数（用于分页）
        long totalRecords = latestRecordsByUser.size();

        // 3. 根据分数和时间排序，然后分页
        List<AiViewRank> sortedRecords = latestRecordsByUser.stream()
                .sorted((r1, r2) -> {
                    // 先按分数降序排序
                    int scoreCompare = r2.getScore().compareTo(r1.getScore());
                    if (scoreCompare != 0) {
                        return scoreCompare;
                    }
                    // 分数相同时按时间升序排序（早的排在前面）
                    return r1.getUpdateTime().compareTo(r2.getUpdateTime());
                })
                .toList();

        // 4. 分页处理
        int start = (page - 1) * size;
        int end = Math.min(start + size, sortedRecords.size());
        List<AiViewRank> pageRecords = sortedRecords.subList(start, end);

        List<String> userIds = pageRecords.stream()
                .map(AiViewRank::getUserId)
                .distinct()
                .toList();

        if (CollectionUtils.isEmpty(userIds)) {
            return new GetRankListResponse();
        }

        Map<String, SysUser> userIdToUserMap = sysUserMapper.selectByIds(userIds).stream()
                .collect(Collectors.toMap(SysUser::getUserId, Function.identity()));

        // 5. 批量计算排名并转换为VO
        List<AiViewRankWithRankVO> voList = pageRecords.stream()
                .map(record -> {
                    AiViewRankWithRankVO vo = new AiViewRankWithRankVO();
                    BeanUtils.copyProperties(record, vo);
                    String userId = record.getUserId();

                    // 直接通过在sortedRecords中的索引计算排名
                    long rank = sortedRecords.indexOf(record) + 1;

                    vo.setUserRank(rank);
                    vo.setIsMe(userId.equals(currentUserId));
                    vo.setPhoto(Optional.ofNullable(userIdToUserMap.get(userId))
                            .map(SysUser::getPhoto)
                            .orElse(Constants.DEFAULT_AVATAR));
                    vo.setName(
                            // 1. 从Map中获取SysUser，若为null则触发Optional空分支
                            Optional.ofNullable(userIdToUserMap.get(userId))
                                    // 2. 提取SysUser的name（若User为null，此步不执行）
                                    .map(SysUser::getName)
                                    // 3. 对name执行脱敏：首字符保留，其余替换为*
                                    .map(name -> {
                                        if (userId.equals(currentUserId)) {
                                            return name;
                                        }

                                        // 处理极端情况：name为空字符串（避免空指针）
                                        if (!StringUtils.hasLength(name)) {
                                            return ANONYMOUS;
                                        }
                                        // 首字符 + 其余长度的*（例如："张三" → "张*"，"李思思" → "李**"）
                                        return name.charAt(0) + "*".repeat(name.length() - 1);
                                    })
                                    .orElse(ANONYMOUS)
                    );
                    return vo;
                })
                .toList();

        // 6. 构建响应对象
        GetRankListResponse response = new GetRankListResponse();
        response.setCurrent(page);
        response.setSize(size);
        response.setTotal(totalRecords);
        response.setPages((int) Math.ceil((double) totalRecords / size));
        response.setRecords(voList);

        return response;
    }

    public List<AiViewRank> getLatestByTrainId(String trainId) {
        List<AiViewRank> aiViewRanks = aiViewRankMapper.selectList(new LambdaQueryWrapper<AiViewRank>().eq(AiViewRank::getTrainId, trainId));

        // 按userId分组，每组取updateTime最新的记录
        return aiViewRanks.stream()
                .collect(Collectors.groupingBy(AiViewRank::getUserId))
                .values()
                .stream()
                .map(userRecords -> userRecords.stream()
                        .max(Comparator.comparing(AiViewRank::getUpdateTime))
                        .orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    // ============ 查询用户的排名 ============
    public AiViewUserRankVO getUserRank(String userId, String roomId, String position) {

        if (!StringUtils.hasLength(roomId)) {
            AiViewRank aiViewRank = aiViewRankMapper.selectOne(new LambdaQueryWrapper<AiViewRank>()
                    .eq(AiViewRank::getUserId, userId)
                    .eq(AiViewRank::getPosition, position)
                    .orderByDesc(AiViewRank::getUpdateTime)
                    .last("LIMIT 1"));
            roomId = aiViewRank.getRoomId();
        }

        AiViewRecord aiViewRecord = aiViewRecordMapper.selectOne(new QueryWrapper<AiViewRecord>().lambda().eq(AiViewRecord::getRoomId, roomId));
        if (aiViewRecord == null) {
            return null;
        }
        String trainId = aiViewRecord.getTrainId();

        // 获取当前用户在该房间的排名记录
        AiViewRank aiViewRank = aiViewRankMapper.selectOne(new LambdaQueryWrapper<AiViewRank>()
                .eq(AiViewRank::getUserId, userId)
                .eq(AiViewRank::getRoomId, roomId)
                .orderByDesc(AiViewRank::getUpdateTime)
                .last("LIMIT 1"));
        if (aiViewRank == null) {
            return null;
        }

        SysUser sysUser = sysUserMapper.selectById(userId);

        // 1. 获取当前职位下每个用户的最新排名数据
        List<AiViewRank> latestRecordsByUser = getLatestByTrainId(trainId);

        // 2. 根据分数和时间排序
        List<AiViewRank> sortedRecords = latestRecordsByUser.stream()
                .sorted((r1, r2) -> {
                    // 先按分数降序排序
                    int scoreCompare = r2.getScore().compareTo(r1.getScore());
                    if (scoreCompare != 0) {
                        return scoreCompare;
                    }
                    // 分数相同时按时间升序排序（早的排在前面）
                    return r1.getUpdateTime().compareTo(r2.getUpdateTime());
                })
                .toList();

        // 使用IntStream查找用户在排序列表中的位置
        OptionalInt userIndex = IntStream.range(0, sortedRecords.size())
                .filter(i -> sortedRecords.get(i).getUserId().equals(userId))
                .findFirst();

        long rank = userIndex.isPresent() ? userIndex.getAsInt() + 1 : sortedRecords.size() + 1;

        Long total = (long) latestRecordsByUser.size();

        AiViewRankWithRankVO aiViewRankWithRankVO = buildRankVO(aiViewRank, rank, userId,
                Optional.ofNullable(sysUser)
                        .map(SysUser::getPhoto)
                        .orElse(Constants.DEFAULT_AVATAR));

        AiViewUserRankVO aiViewUserRankVO = new AiViewUserRankVO();
        BeanUtils.copyProperties(aiViewRankWithRankVO, aiViewUserRankVO);
        aiViewUserRankVO.setName(Optional.ofNullable(sysUser).map(SysUser::getName).orElse(ANONYMOUS));
        aiViewUserRankVO.setTotal(total);
        return aiViewUserRankVO;
    }

    private AiViewRankWithRankVO buildRankVO(AiViewRank base, long rank, String userId, String avatar) {
        AiViewRankWithRankVO vo = new AiViewRankWithRankVO();
        vo.setUserId(base.getUserId());
        vo.setPosition(base.getPosition());
        vo.setScore(base.getScore());
        vo.setUpdateTime(base.getUpdateTime());
        vo.setName(base.getName());
        vo.setCity(base.getCity());
        vo.setRoomId(base.getRoomId());
        vo.setPhoto(avatar);
        vo.setUserRank(rank);
        vo.setIsMe(UserInfoUtils.getCurrentUserId().equals(userId));
        return vo;
    }
}