package com.wisematch.modules.chat.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.cache.RedisCache;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.oss.FileAcl;
import com.wisematch.common.oss.OssFacade;
import com.wisematch.common.utils.ThreadPoolUtil;
import com.wisematch.modules.chat.agent.AiReportAgentFacade;
import com.wisematch.modules.chat.convertor.GenerationReportConvertor;
import com.wisematch.modules.chat.entity.*;
import com.wisematch.modules.chat.enums.ApplyType;
import com.wisematch.modules.chat.handler.wiser.ViewRankHandler;
import com.wisematch.modules.chat.mapper.AiViewReportMapper;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.*;
import com.wisematch.modules.chat.utils.MapToListUtils;
import com.wisematch.modules.chat.wrapper.wiser.agents.AgentsReportServiceWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


/**
 * 系统用户
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiViewReportServiceImpl extends ServiceImpl<AiViewReportMapper, AiViewReport> implements AiViewReportService {
    @Autowired
    AiViewRecordService aiViewRecordService;
    @Autowired
    private AiReportAgentFacade aiReportAgentFacade;
    @Autowired
    private AiJobExamineService aiJobExamineService;
    @Autowired
    private AiChatMemoryService aiChatMemoryService;
    @Autowired
    private ViewRankHandler viewRankHandler;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private AiNotifyMessageService aiNotifyMessageService;

    @Resource
    private AiExamineAgentMappingService aiExamineAgentMappingService;

    @Resource
    private AiJobPositionService aiJobPositionService;

    @Resource
    private AiJobTrainService aiJobTrainService;

    @Resource
    private OssFacade ossFacade;

    @Resource
    private AgentsReportServiceWrapper agentsReportServiceWrapper;

    @Override
    public AiViewReport getByRoomId(String roomId) {
        QueryWrapper<AiViewReport> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiViewReport::getRoomId, roomId);
        return this.baseMapper.selectOne(queryWrapper);
    }

    private AiViewReport createIfAbsent(AiViewRecord aiViewRecord){

        AiViewReport aiViewReport = this.getByRoomId(aiViewRecord.getRoomId());
        if (aiViewReport == null) {
            //面试报告初始化
            aiViewReport = new AiViewReport();
            aiViewReport.setName(aiViewRecord.getChatName());
            aiViewReport.setUserId(aiViewRecord.getUserId());
            aiViewReport.setRoomId(aiViewRecord.getRoomId());
            aiViewReport.setDuration(aiViewRecord.getDuration());
            aiViewReport.setMessages(JSONObject.toJSONString(this.aiChatMemoryService.getList(aiViewRecord.getRoomId())));
            aiViewReport.setStatus(0);
            this.baseMapper.insert(aiViewReport);
        }
        return aiViewReport;
    }

    @Override
    public boolean reportSchedule(AiViewRecord aiViewRecord, Integer currentRunCount, Integer maxRunCount) {
        String roomId = aiViewRecord.getRoomId();
        AiViewReport aiViewReport = getByRoomId(roomId);
        if (aiViewReport != null) {
            removeById(aiViewReport.getId());
        }

        //面试报告初始化
        aiViewReport = new AiViewReport();
        aiViewReport.setName(aiViewRecord.getChatName());
        aiViewReport.setUserId(aiViewRecord.getUserId());
        aiViewReport.setRoomId(aiViewRecord.getRoomId());
        aiViewReport.setDuration(aiViewRecord.getDuration());
        aiViewReport.setMessages(JSONObject.toJSONString(this.aiChatMemoryService.getList(aiViewRecord.getRoomId())));
        aiViewReport.setStatus(0);
        aiViewReport.setChatUrl(ossFacade.getHttpUrl("interview/user/" + roomId + ".mp4", FileAcl.PUBLIC));
        this.baseMapper.insert(aiViewReport);

        try {
            String historyChatMessage = JSONObject.toJSONString(this.aiChatMemoryService.getList(roomId));

            if (aiViewReport.getMessages() == null || historyChatMessage.isEmpty()) {
                throw new RRException("记录为空，生成报告异常");
            }
            AiJobExamine aiJobExamine = aiJobExamineService.getExamByInterview(aiViewRecord);

            AiViewRecord record = aiViewRecordService.getByRoomId(roomId);

            String positionContent = "";
            String positionName = "";
            String chatType = aiViewRecord.getChatType();
            if (ApplyType.POSITION.name().equals(chatType)) {
                String positionId = record.getPositionId();
                AiJobPosition position = aiJobPositionService.getById(positionId);
                if (position != null) {
                    positionContent = position.getContent();
                    positionName = position.getPosition();
                }
            }

            if (ApplyType.TRAIN.name().equals(chatType)) {
                String trainId = record.getTrainId();
                AiJobTrain aiJobTrain = aiJobTrainService.getById(trainId);
                if (aiJobTrain != null) {
                    positionContent = aiJobTrain.getContent();
                    positionName = aiJobTrain.getPosition();
                }
            }

            String assessmentStr = aiJobExamine.getContent();
            List<JSONObject> assessmentDimensions = JSONObject.parseArray(assessmentStr, JSONObject.class);

            JSONObject jsonObject = new JSONObject();
            assessmentDimensions.forEach(assessment -> {
                String dimensionName = assessment.getString("dimension");
                JSONArray checkPoint = assessment.getJSONArray("checkPoint");
                double weight = assessment.getDouble("weight");
                // 创建维度对象
                JSONObject dimensionObj = new JSONObject();
                dimensionObj.put("checkPoint", checkPoint);
                dimensionObj.put("weight", weight);
                // 将维度对象放入结果中
                jsonObject.put(dimensionName, dimensionObj);
            });

            List<ShortMessageVO> shortMessageVOS = aiChatMemoryService.getListWithIndexQuestion(roomId);
            List<List<ShortMessageVO>> messages = MapToListUtils.convert(shortMessageVOS);

            AgentsReportReq req = new AgentsReportReq();
            req.setExamineData(jsonObject.toJSONString());
            req.setJobName(positionName == null ? "" : positionName);
            req.setJdData(positionContent == null ? "" : positionContent);
            req.setGroupedHistory(JSON.toJSONString(messages));
            AgentsReportVO report = agentsReportServiceWrapper.getReport(req);
            GenerationReport generationReport = GenerationReportConvertor.convert(report);
            List<CommentMsg> dimensions = generationReport.getDimensions();

            //计算分数
            ReportVO reportVO = new ReportVO();

            Integer score = calculateScore(generationReport, assessmentDimensions);

            if (score == 0 && maxRunCount - 1 > currentRunCount) {
                log.warn("分数为0，生成报告异常:{}", generationReport);
                return false;
            }

            reportVO.setScore(score);

            //构造报告
            BeanUtils.copyProperties(aiViewRecord, reportVO);
            reportVO.setStartTime(aiViewRecord.getStartTime());
            reportVO.setPosition(aiViewRecord.getChatName());
            reportVO.setLabels(this.getLabels(assessmentStr));
            reportVO.setMessages(historyChatMessage);

            List<CommentMsg> adjustedDimensions = adjustDimensions(reportVO, dimensions);
            reportVO.setDimensions(adjustedDimensions);

            reportVO.setVideoUrl(aiViewReport.getChatUrl());
            reportVO.setDuration(aiViewReport.getDuration());
            aiViewReport.setStatus(1);
            aiViewReport.setScore(reportVO.getScore());
            aiViewReport.setReport(JSONObject.toJSONString(reportVO));
            this.updateById(aiViewReport);
            log.info("报告生成完成, roomId: {}", roomId);

            if(ApplyType.TRAIN.name().equals(aiViewRecord.getChatType())){
                viewRankHandler.handleInsert(aiViewReport);
            }

            return true;
        } catch (Exception e) {
            log.info("报告生成异常", e);
            aiViewReport.setStatus(-1);
            this.baseMapper.updateById(aiViewReport);
            return false;
        }



    }

    @Override
    public ReportVO report(AiViewRecord aiViewRecord) {
        String roomId = aiViewRecord.getRoomId();
        log.info("报告开始生成..., roomId: {}", roomId);
        //todo 后面根据岗位或者面试的城市添加
        AiViewReport aiViewReport = this.createIfAbsent(aiViewRecord);

        if (aiViewReport.getStatus() == 1) {
            ReportVO reportVO = JSON.parseObject(aiViewReport.getReport(), ReportVO.class);
            if (reportVO != null) {
                return reportVO;
            }
        } else if (aiViewReport.getStatus() == -1) {
            ReportVO reportVO = JSON.parseObject(aiViewReport.getReport(), ReportVO.class);
            if (reportVO != null) {
                return reportVO;
            }
        }

        try {
            String historyChatMessage = JSONObject.toJSONString(this.aiChatMemoryService.getList(roomId));

            if (aiViewReport.getMessages() == null || historyChatMessage.isEmpty()) {
                throw new RRException("记录为空，生成报告异常");
            }
            AiJobExamine aiJobExamine = aiJobExamineService.getExamByInterview(aiViewRecord);

            AiViewRecord record = aiViewRecordService.getByRoomId(roomId);

            String positionContent = null;
            String positionName = null;
            String chatType = aiViewRecord.getChatType();
            if (ApplyType.POSITION.name().equals(chatType)) {
                String positionId = record.getPositionId();
                AiJobPosition position = aiJobPositionService.getById(positionId);
                if (position != null) {
                    positionContent = position.getContent();
                    positionName = position.getPosition();
                }
            }

            if (ApplyType.TRAIN.name().equals(chatType)) {
                String trainId = record.getTrainId();
                AiJobTrain aiJobTrain = aiJobTrainService.getById(trainId);
                if (aiJobTrain != null) {
                    positionContent = aiJobTrain.getContent();
                    positionName = aiJobTrain.getPosition();
                }
            }

            String assessmentStr = aiJobExamine.getContent();
            List<JSONObject> assessmentDimensions = JSONObject.parseArray(assessmentStr, JSONObject.class);

            JSONObject jsonObject = new JSONObject();
            assessmentDimensions.forEach(assessment -> {
                String dimensionName = assessment.getString("dimension");
                JSONArray checkPoint = assessment.getJSONArray("checkPoint");
                double weight = assessment.getDouble("weight");
                // 创建维度对象
                JSONObject dimensionObj = new JSONObject();
                dimensionObj.put("checkPoint", checkPoint);
                dimensionObj.put("weight", weight);
                // 将维度对象放入结果中
                jsonObject.put(dimensionName, dimensionObj);
            });

            List<ShortMessageVO> shortMessageVOS = aiChatMemoryService.getListWithIndexQuestion(roomId);
            List<List<ShortMessageVO>> messages = MapToListUtils.convert(shortMessageVOS);

            AgentsReportReq req = new AgentsReportReq();
            req.setExamineData(jsonObject.toJSONString());
            req.setJobName(positionName == null ? "" : positionName);
            req.setJdData(positionContent == null ? "" : positionContent);
            req.setGroupedHistory(JSON.toJSONString(messages));
            AgentsReportVO report = agentsReportServiceWrapper.getReport(req);
            log.info("agents report: {}", report);
            GenerationReport generationReport = GenerationReportConvertor.convert(report);
            List<CommentMsg> dimensions = generationReport.getDimensions();

            //计算分数
            ReportVO reportVO = new ReportVO();

            Integer score = calculateScore(generationReport, assessmentDimensions);
            reportVO.setScore(score);

            //构造报告
            BeanUtils.copyProperties(aiViewRecord, reportVO);
            reportVO.setStartTime(aiViewRecord.getStartTime());
            reportVO.setPosition(aiViewRecord.getChatName());
            reportVO.setLabels(this.getLabels(assessmentStr));
            reportVO.setMessages(historyChatMessage);

            List<CommentMsg> adjustedDimensions = adjustDimensions(reportVO, dimensions);
            reportVO.setDimensions(adjustedDimensions);

            reportVO.setVideoUrl(aiViewReport.getChatUrl());
            reportVO.setDuration(aiViewReport.getDuration());
            aiViewReport.setStatus(1);
            aiViewReport.setScore(reportVO.getScore());
            aiViewReport.setReport(JSONObject.toJSONString(reportVO));
            this.updateById(aiViewReport);
            log.info("报告生成完成, roomId: {}", roomId);

            if(ApplyType.TRAIN.name().equals(aiViewRecord.getChatType())){
                ThreadPoolUtil.supplyAsync(() -> viewRankHandler.handleInsert(aiViewReport)).thenAccept(result -> {
                    log.info("更新排名执行成功: {}", result);
                });
            }
//            AiNotifyMessage aiNotifyMessage = NotifyMessageFactories.reportGenerate(aiViewReport.getUserId(), aiViewReport.getId());
//            aiNotifyMessage.setPosition(aiViewReport.getName());
//            ThreadPoolUtil.supplyAsync(() -> aiNotifyMessageService.save(
//                    aiNotifyMessage)).thenAccept(result -> {
//                log.info("发送消息执行成功: {}", result);
//            });
            return reportVO;
        } catch (Exception e) {
            log.info("报告生成异常", e);
            aiViewReport.setStatus(-1);
            this.baseMapper.updateById(aiViewReport);
        }

        return null;
    }


    private static List<CommentMsg> adjustDimensions(ReportVO reportVO, List<CommentMsg> dimensions) {
        JSONArray msgList = JSONObject.parseArray(reportVO.getMessages());
        List<CommentMsg> scoreLabels = new ArrayList<>();
        dimensions.forEach(x -> {
            for (int i = 0; i < msgList.size(); i++) {
                JSONObject jsonObject = msgList.getJSONObject(i);
                if (jsonObject.getString("content").contains(x.getUnderline())) {
                    x.setMsgIndex(i);
                }
            }
            if (x.getScore() > Labels.PERFECT.score) {
                CommentMsg cm = CommentMsg.clone(x);
                cm.setLabels(Labels.PERFECT.label);
                scoreLabels.add(cm);
            } else if (x.getScore() > Labels.GOOD.score) {
                CommentMsg cm = CommentMsg.clone(x);
                cm.setLabels(Labels.GOOD.label);
                scoreLabels.add(cm);
            } else {
                CommentMsg cm = CommentMsg.clone(x);
                cm.setLabels(Labels.BAD.label);
                scoreLabels.add(cm);
            }
        });
        reportVO.setUnderlines(dimensions.stream().map(CommentMsg::getUnderline).toList());
        dimensions = new ArrayList<>(dimensions);
        dimensions.addAll(scoreLabels);
        return dimensions;
    }

    private Integer calculateScore(GenerationReport generationReport, List<JSONObject> assessmentDimensions) {
        // 校验输入参数合法性
        if (generationReport == null || generationReport.getDimensions() == null || assessmentDimensions == null) {
            return 0;
        }

        List<CommentMsg> commentList = generationReport.getDimensions();
        double totalScore = assessmentDimensions.stream()
                .mapToDouble(dimensionJson -> calculateDimensionScore(dimensionJson, commentList))
                .sum();

        // 四舍五入为整数返回（最终得分应为百分制整数）
        return (int) Math.round(totalScore);
    }

    /**
     * 计算单个维度的得分
     */
    private double calculateDimensionScore(JSONObject dimensionJson, List<CommentMsg> commentList) {
        String dimensionName = dimensionJson.getString("dimension");
        double weight = dimensionJson.getDouble("weight");
        double dimensionFullScore = weight * 100;
        JSONArray checkPoints = dimensionJson.getJSONArray("checkPoint");
        int checkPointCount = checkPoints.size();

        // 计算所有考核点的最高得分总和
        double sumCheckPointMax = IntStream.range(0, checkPoints.size())
                .mapToObj(checkPoints::getJSONObject)
                .mapToDouble(checkPointJson -> calculateCheckPointMaxScore(
                        checkPointJson.getString("name"),
                        dimensionName,
                        commentList
                ))
                .sum();

        // 计算该维度的最终得分
        double scoreRatio = sumCheckPointMax / (checkPointCount * 5.0);
        double dimensionScore = scoreRatio * dimensionFullScore;

        // 确保得分在0-维度满分之间（处理极端值）
        return Math.max(0, Math.min(dimensionScore, dimensionFullScore));
    }

    /**
     * 计算单个考核点的最高得分
     */
    private double calculateCheckPointMaxScore(String checkPointName, String dimensionName, List<CommentMsg> commentList) {
        return commentList.stream()
                // 匹配维度和考核点
                .filter(comment -> isDimensionMatch(comment, dimensionName) && isCheckPointMatch(comment, checkPointName))
                // 找到分数最高的评论，score为null的视为0分
                .max(Comparator.comparingInt(comment -> getScoreOrDefault(comment, 0)))
                // 处理无记录的情况，取0分
                .map(comment -> getScoreOrDefault(comment, 0.0))
                .orElse(0.0);
    }

    /**
     * 检查评论是否匹配维度
     */
    private boolean isDimensionMatch(CommentMsg comment, String dimensionName) {
        return comment.getLabels() != null && comment.getLabels().equals(dimensionName);
    }

    /**
     * 检查评论是否匹配考核点
     */
    private boolean isCheckPointMatch(CommentMsg comment, String checkPointName) {
        return comment.getCheckPoint() != null && comment.getCheckPoint().equals(checkPointName);
    }

    /**
     * 获取评论分数，为空时返回默认值
     */
    private int getScoreOrDefault(CommentMsg comment, int defaultValue) {
        Integer score = comment.getScore();
        return score != null ? score : defaultValue;
    }

    /**
     * 获取评论分数，为空时返回默认值
     */
    private double getScoreOrDefault(CommentMsg comment, double defaultValue) {
        Integer score = comment.getScore();
        return score != null ? score : defaultValue;
    }


    private GenerationReport getLlmReportReply(String historyChatMessage, List<JSONObject> assessmentDimensions
            , Map<String, AiExamineAgentMapping> examineNameToMappingMap, String positionContent) {
        // 解析评估维度列表
        List<GenerationReport> allReports = new ArrayList<>();

        // 1. 为每个维度创建异步评估任务（Stream生成Future列表）
        List<CompletableFuture<GenerationReport>> futureList = assessmentDimensions.stream()
                .map(dimension -> CompletableFuture.supplyAsync(() -> {
                    // 构建当前维度的输入参数
                    String dimensionName = dimension.getString("dimension");
                    AiExamineAgentMapping mapping = examineNameToMappingMap.get(dimensionName);

                    if (mapping == null) {
                        log.error("unknown dimensionName: {}", dimensionName);
                        return new GenerationReport();
                    }

                    String poolId = mapping.getPoolId();

                    ReportInput input = new ReportInput();
                    input.setAssessment(dimension.toJSONString());
                    input.setHistory(historyChatMessage);
                    input.setPoolId(poolId);
                    input.setPositionContent(positionContent);
                    // 执行评估并解析结果
                    try {
                        String reply = aiReportAgentFacade.getReport(input);
                        return JSON.parseObject(reply, GenerationReport.class);
                    } catch (Exception e) {
                        // 单个维度异常处理（返回空对象避免NPE）
                        log.error("简历维度评估异常（维度：{}）", dimension.getString("dimension"), e);
                        return new GenerationReport(); // 建议使用无参构造创建空对象，而非解析空JSON
                    }
                }, ThreadPoolUtil.getInstance()))
                .toList();

        // 2. 等待所有任务完成并汇总结果（注意：anyOf改为allOf，确保所有任务完成）
        try {
            // 等待所有任务完成，设置超时时间（100秒）
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]))
                    .get(15, TimeUnit.MINUTES);

            // 收集所有任务结果
            allReports = futureList.stream()
                    .map(future -> {
                        try {
                            return future.get(); // 已完成，get()不会阻塞
                        } catch (Exception e) {
                            log.error("获取维度评估结果异常", e);
                            return new GenerationReport();
                        }
                    })
                    .collect(Collectors.toList());
        } catch (TimeoutException e) {
            log.error("简历维度评估超时（100秒），部分结果可能不完整", e);
            // 超时后尝试收集已完成的结果
            allReports = futureList.stream()
                    .filter(CompletableFuture::isDone)
                    .map(future -> {
                        try {
                            return future.get();
                        } catch (Exception ex) {
                            log.error("超时后获取结果异常", ex);
                            return new GenerationReport();
                        }
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("简历维度评估汇总异常", e);
        }

        List<CommentMsg> commentMsgList = allReports.stream()
                .filter(Objects::nonNull) // 确保report不为null
                .map(GenerationReport::getDimensions) // 先获取dimensions
                .filter(Objects::nonNull) // 确保dimensions不为null
                .flatMap(List::stream) // 再进行flatMap操作
                .toList();
        return new GenerationReport(commentMsgList);
    }

    @Override
    public List<ReportVO> reportList(String userId) {
        return null;
    }

    @Override
    public List<String> getColumnList(String column) {
        QueryWrapper<AiViewReport> wrapper = new QueryWrapper<>();
        wrapper.select("DISTINCT "+column);
        return this.baseMapper.selectObjs(wrapper);
    }

    @Override
    public List<String> getCityList() {
        List<String> cities = redisCache.getCacheObject("rank_cities");
        if(cities != null && !cities.isEmpty()){
            return cities;
        }
        cities = this.getColumnList("city");
        redisCache.put("rank_cities", cities, (long)(60  * 24));
        return cities;
    }

    @Override
    public List<String> getPositionList() {
        List<String> positions = redisCache.getCacheObject("rank_positions");
        if(positions != null && !positions.isEmpty()){
            return positions;
        }
        positions = this.getColumnList("name");
        redisCache.put("rank_positions", positions, (long)(60  * 24));
        return positions;
    }

    /**
     * 提取labels
     *
     * @param examine
     * @return
     */
    private List<String> getLabels(String examine) {
        if (StringUtils.isEmpty(examine)) {
            return new ArrayList<>();
        }
        List<JSONObject> list = JSONObject.parseArray(examine, JSONObject.class);
        List<String> labels = new ArrayList<>(list.stream().map(x -> x.getString("dimension")).toList());
        labels.addAll(List.of(Labels.PERFECT.label, Labels.GOOD.label, Labels.BAD.label));
        return labels;
    }

    /**
     * 分类标签
     */
    public enum Labels {
        PERFECT("回答卓越", 3),

        GOOD("回答一般", 1),

        BAD("回答有误", 0);
        private String label;

        private Integer score;

        Labels(String label, Integer score) {
            this.label = label;
            this.score = score;
        }
    }
}
