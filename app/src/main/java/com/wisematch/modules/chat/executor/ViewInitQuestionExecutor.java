package com.wisematch.modules.chat.executor;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wisematch.common.utils.InterviewThreadPoolUtil;
import com.wisematch.modules.chat.agent.AgentContext;
import com.wisematch.modules.chat.agent.AgentFacade;
import com.wisematch.modules.chat.agent.ResumeParseAgent;
import com.wisematch.modules.chat.entity.*;
import com.wisematch.modules.chat.enums.ApplyType;
import com.wisematch.modules.chat.enums.ViewerAgentConstant;
import com.wisematch.modules.chat.model.GenQuestion;
import com.wisematch.modules.chat.service.*;
import com.wisematch.modules.common.utils.JsonUtils;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.service.SysUserService;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 面试准备异步执行器，准备简历、准备问题清单
 *
 * <AUTHOR>
 * @version AgentExecutor.java, v0.1 2025-07-08 19:12
 */
@Service
@Slf4j
@Data
public class ViewInitQuestionExecutor {


    @Autowired
    public AiJobExamineService aiJobExamineService;

    @Autowired
    public AiJobPositionService aiJobPositionService;

    @Autowired
    public AiJobTrainService aiJobTrainService;

    @Autowired
    public AiViewRecordService aiViewRecordService;


    @Autowired
    public AiUserResumeService aiUserResumeService;

    @Autowired
    public AiViewPrepareService aiViewPrepareService;

    @Autowired
    private AiJobViewerService aiJobViewerService;

    @Resource
    private ResumeParseAgent resumeParseAgent;

    @Autowired
    public AgentFacade agentFacade;

    @Autowired
    SysUserService sysUserService;


    /**
     * 生成个人经历问题
     *
     * @param aiViewPrepare
     * @return
     */
    List<GenQuestion> generateQuesionA(AiJobExamine aiJobExamine, AiViewPrepare aiViewPrepare, AiJobViewer aiJobViewer) {
        try {
            AgentContext agentContext = new AgentContext();

            //个人经历问题生成
            String prompt = agentFacade.getPrompt(ViewerAgentConstant.QUESTION_GENERATE_SZ_P1);
            agentContext.setAgentCode(ViewerAgentConstant.QUESTION_GENERATE_SZ_P1);
            if (JSONUtil.isTypeJSON(aiViewPrepare.getResume())) {
                JSONObject resume = JSONObject.parseObject(aiViewPrepare.getResume());
                //应届生提问策略
                if (!"非应届".equals(resume.getString("graduateStatus"))) {
                    prompt = agentFacade.getPrompt(ViewerAgentConstant.QUESTION_GENERATE_YJ_P1);
                    agentContext.setAgentCode(ViewerAgentConstant.QUESTION_GENERATE_YJ_P1);
                }
            }

            JSONArray jsonArray = JSONObject.parseArray(aiJobExamine.getContent());
            List<JSONObject> examines = new ArrayList<>();
            jsonArray.forEach(x -> {
                JSONObject examine = JSONObject.parseObject(JSONObject.toJSONString(x));
                if (examine.getString("dimension").contains("知识技能") || examine.getString("dimension").equals("岗位胜任力")) {
                    examines.add(examine);
                }
            });
            prompt = prompt.replace("$resume", aiViewPrepare.getResume());
            prompt = prompt.replace("$examine", JSONObject.toJSONString(examines));
            prompt = prompt.replace("$jd", aiJobExamine.getName());
            agentContext.setPrompt(prompt);
            agentContext.setUserMsg("帮我生成问题清单");
            String replyA = agentFacade.supply(agentContext);
            String reply = JsonUtils.extractJSONArray(replyA);
            if (reply.contains("[") && reply.contains("]")) {
                return JSONObject.parseArray(reply, GenQuestion.class);
            } else {
                log.error("view generate questionA error,{}", reply);
            }
        } catch (Exception e) {
            log.error("view generate questionB error", e);
        }
        return new ArrayList<>();
    }

    /**
     * 生成简历风险点问题
     *
     * @param aiViewPrepare
     * @return
     */
    List<GenQuestion> generateQuesionB(AiJobExamine aiJobExamine, AiViewPrepare aiViewPrepare, AiJobViewer aiJobViewer) {
        //生成简历风险点问题
        try {
            AgentContext agentContext = new AgentContext();

            String prompt = agentFacade.getPrompt(ViewerAgentConstant.QUESTION_GENERATE_SZ_P2);
            agentContext.setAgentCode(ViewerAgentConstant.QUESTION_GENERATE_SZ_P2);
            if (JSONUtil.isTypeJSON(aiViewPrepare.getResume())) {
                JSONObject resume = JSONObject.parseObject(aiViewPrepare.getResume());
                //应届生提问策略
                if (!"非应届".equals(resume.getString("graduateStatus"))) {
                    prompt = agentFacade.getPrompt(ViewerAgentConstant.QUESTION_GENERATE_YJ_P2);
                    agentContext.setAgentCode(ViewerAgentConstant.QUESTION_GENERATE_YJ_P2);
                }
            }
            prompt = prompt.replace("$resume", aiViewPrepare.getResume());
            prompt = prompt.replace("$jd", aiJobExamine.getName());
            agentContext.setPrompt(prompt);
            agentContext.setUserMsg("帮我生成问题清单");
            String replyB = agentFacade.supply(agentContext);
            String reply = JsonUtils.extractJSONArray(replyB);
            if (reply.contains("[") && reply.contains("]")) {
                return JSONObject.parseArray(reply, GenQuestion.class);
            } else {
                log.error("view generate questionB error,{}", reply);
            }
        } catch (Exception e) {
            log.error("view generate questionB error", e);
        }
        return new ArrayList<>();
    }

    /**
     * 异步生成问题
     *
     * @param aiViewPrepare
     * @param aiJobViewer
     * @return
     */
    List<GenQuestion> asyncGenerate(AiJobExamine aiJobExamine, AiViewPrepare aiViewPrepare, AiJobViewer aiJobViewer) {


        CompletableFuture<List<GenQuestion>> gA = InterviewThreadPoolUtil.supplyAsync(() -> this.generateQuesionA(aiJobExamine, aiViewPrepare, aiJobViewer));
        CompletableFuture<List<GenQuestion>> gB = InterviewThreadPoolUtil.supplyAsync(() -> this.generateQuesionB(aiJobExamine, aiViewPrepare, aiJobViewer));

        CompletableFuture<List<GenQuestion>> qAB = gA.thenCombine(gB, (List<GenQuestion> q1, List<GenQuestion> q2) -> {
            if (!q2.isEmpty()) {
                q1.addAll(q2);
            }
            return q1;
        });
        try {
            return qAB.get();
        } catch (Exception e) {
            log.error("async generate question error", e);
        }
        return new ArrayList<>();
    }

    /**
     * 同步生成问题清单
     *
     * @param aiViewRecord
     * @return
     */
    public boolean execute(AiViewRecord aiViewRecord) {
        log.info("interview prepare start");
        long start = System.currentTimeMillis();
        if (aiViewRecord == null) {
            return false;
        }
        AiViewPrepare aiViewPrepare = new AiViewPrepare();
        String userId = aiViewRecord.getUserId();
        aiViewPrepare.setUserId(userId);
        aiViewPrepare.setRoomId(aiViewRecord.getRoomId());
        String resume = aiUserResumeService.getCurrentOpened(aiViewRecord.getUserId());
        if (!JSONUtil.isTypeJSON(resume)) {
            resume = resumeParseAgent.resumeJsonExtract(resume);
        }
        JSONObject resumeJsonObject = JSONObject.parseObject(resume);
        SysUser sysUser = sysUserService.getById(userId);
        resumeJsonObject.put("sex", sysUser.getSex());
        aiViewPrepare.setResume(resumeJsonObject.toJSONString());

        AiJobViewer aiJobViewer = aiJobViewerService.getById(aiViewRecord.getViewerId());

        if (ApplyType.POSITION.name().equals(aiViewRecord.getChatType())) {
            AiJobPosition aiJobPosition = aiJobPositionService.getById(aiViewRecord.getPositionId());
            if (aiJobPosition == null) return false;
            AiJobExamine aiJobExamine = aiJobExamineService.getById(aiJobPosition.getJobExamineId());
            aiViewPrepare.setExamineId(aiJobExamine.getId());
            // 模型回复
            String questions = JSONObject.toJSONString(this.asyncGenerate(aiJobExamine, aiViewPrepare, aiJobViewer));
            aiViewPrepare.setQuestions(questions);
        } else {
            AiJobTrain aiJobTrain = aiJobTrainService.getById(aiViewRecord.getTrainId());
            if (aiJobTrain == null) return false;
            AiJobExamine aiJobExamine = aiJobExamineService.getById(aiJobTrain.getJobExamineId());
            aiViewPrepare.setExamineId(aiJobExamine.getId());
            //模拟面试类型:0-模拟，1-专项
            if (aiJobTrain.getType() == 1) {
                //按难度过滤题库
                if (JSONObject.isValidArray(aiJobExamine.getQuestions())) {
                    List<JSONObject> list = new ArrayList<>();
                    JSONObject.parseArray(aiJobExamine.getQuestions(), JSONObject.class).forEach(x -> {
                        if (aiJobTrain.getLevel().equals(x.getString("level"))) {
                            list.add(x);
                        }
                    });
                    aiViewPrepare.setQuestions(JSONObject.toJSONString(list));
                } else {
                    aiViewPrepare.setQuestions(aiJobExamine.getQuestions());
                }
            } else {
                String questions = JSONObject.toJSONString(this.asyncGenerate(aiJobExamine, aiViewPrepare, aiJobViewer));
                aiViewPrepare.setQuestions(questions);
            }
        }
        this.aiViewPrepareService.save(aiViewPrepare);
        log.info("prepare agent end, cost {} ms", (System.currentTimeMillis() - start));
        return true;
    }

    public void asyncExecute(AiViewRecord aiViewRecord) {
        InterviewThreadPoolUtil.supplyAsync(() -> this.execute(aiViewRecord)).thenAccept(result -> {
            log.info("生成问题成功: {}", result);
        });
    }

}
