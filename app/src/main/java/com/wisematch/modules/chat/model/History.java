package com.wisematch.modules.chat.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class History {

    @Schema(description = "角色")
    String role;

    @Schema(description = "聊天内容")
    String message;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date timestamp;

}
