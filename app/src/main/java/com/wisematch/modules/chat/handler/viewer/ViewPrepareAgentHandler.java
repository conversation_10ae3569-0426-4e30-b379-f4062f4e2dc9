package com.wisematch.modules.chat.handler.viewer;

import com.alibaba.fastjson.JSONObject;
import com.wisematch.modules.chat.entity.AiViewPrepare;
import com.wisematch.modules.chat.entity.AiViewRecord;
import com.wisematch.modules.chat.enums.InterviewStatus;
import com.wisematch.modules.chat.enums.MsgRole;
import com.wisematch.modules.chat.model.ChatReplyMsg;
import com.wisematch.modules.chat.model.GenQuestion;
import com.wisematch.modules.chat.model.ShortMessage;
import com.wisematch.modules.chat.service.AiViewPrepareService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;

/**
 * 记忆存储
 * <AUTHOR>
 * @version IAgentHandler.java, v0.1 2025-07-15 16:42
 */
@Component
@Slf4j
public class ViewPrepareAgentHandler implements IViewAgentHandler {

    IViewAgentHandler iViewAgentHandler;

    @Autowired
    private AiViewPrepareService aiViewPrepareService;

    @Override
    public void setNext(IViewAgentHandler iAgentHandler) {
        this.iViewAgentHandler = iAgentHandler;
    }

    @Override
    public Flux<ChatReplyMsg> handle(ViewHandlerContext context) {
        log.info("interview prepare start, chatId: {}", context.getRoomId());
        if (InterviewStatus.CHATED.name().equals(context.getAiViewRecord().getStatus())) {
            return Flux.just(reply(context.getReplyId(), "面试结束，请点击关闭按钮退出"));
        }
        //最近的一次输入和输出
        context.setUserMsg(this.getLastedUserMessage(context.getChatViewMsg().getMessages()));

         //问题清单
        context.setQuestionList(this.getQuestions(context.getAiViewRecord()));
        //当前话题
        if (context.getAiViewRecord().getAskIndex() == -1) {
            context.setQuestion(new GenQuestion(context.getAiViewRecord().getWelcome()));
        } else {
            if (context.getQuestionList().size() > context.getAiViewRecord().getAskIndex()) {
                context.setQuestion(context.getQuestionList().get(context.getAiViewRecord().getAskIndex()));
            }
        }
        log.info("current question: {} ,askIndex: {} ,question size: {}", context.getQuestion(),
                context.getAiViewRecord().getAskIndex(), context.getQuestionList().size());
        context.setAiViewPrepare(aiViewPrepareService.getByRoomId(context.getRoomId()));
        return iViewAgentHandler.handle(context);
    }

    private List<GenQuestion> getQuestions(AiViewRecord interview){
        try {
            AiViewPrepare aiViewPrepare = aiViewPrepareService.getByRoomId(interview.getRoomId());
            if (aiViewPrepare != null) {
                return JSONObject.parseArray(aiViewPrepare.getQuestions(), GenQuestion.class);
            }
        } catch (Exception e) {
            log.info("问题清单解析异常", e);
        }
        return new ArrayList<>();
    }

    private ChatReplyMsg reply(String replyId, String msg){
        ChatReplyMsg replyMsg = new ChatReplyMsg();
        replyMsg.setId(replyId);
        ChatReplyMsg.Choices choices = new ChatReplyMsg.Choices();
        choices.getDelta().put("content", msg);
        replyMsg.setChoices(List.of(choices));
        return replyMsg;
    }

    /**
     * 获取最新的用户消息
     *
     * @param list
     * @return
     */
    private String getLastedUserMessage(List<Message> list) {
        String userMsg = "";

        /**
         * 获取最新的用户消息
         *
         * @param list
         * @return
         */
        if (list != null && !list.isEmpty()
                && (list.get(list.size() - 1).getMessageType().name().equalsIgnoreCase("user"))) {
            int i = list.size() - 1;
            while (list.get(i).getMessageType().name().equalsIgnoreCase("user")) {
                userMsg = list.get(i).getText() + userMsg;
                i = i - 1;
                if (i < 0) break;
            }
        }
        return userMsg;
    }

    /**
     * 获取最新的智能体消息
     *
     * @param list
     * @return
     */
    private String getLastedAgentMessage(List<ShortMessage> list) {
        String agentMsg = "";
        if (list != null && !list.isEmpty()) {
            int i = list.size() - 1;
            while (i > 0) {
                if (list.get(i).getRole().equals(MsgRole.assistant.name()) || list.get(i).getRole().equals("ASSISTANT")) {
                    agentMsg = list.get(i).getContent();
                    break;
                }
                i = i - 1;
            }
        }
        return agentMsg;
    }
}
