package com.wisematch.modules.chat.handler.matcher;

import com.alibaba.fastjson.JSONObject;
import com.wisematch.modules.chat.agent.*;
import com.wisematch.modules.chat.entity.AiAgentPool;
import com.wisematch.modules.chat.entity.AiChatMatcherLog;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.handler.IAgentHandler;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.service.AiAgentPoolService;
import com.wisematch.modules.chat.service.AiChatMatcherLogService;
import com.wisematch.modules.chat.enums.ViewerAgentConstant;
import com.wisematch.modules.chat.wrapper.IWiseMatchWrapper;
import com.wisematch.modules.chat.wrapper.MatcherWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

/**
 * 简历监测
 * <AUTHOR>
 * @version ResumeCheckHandler.java, v0.1 2025-07-15 16:49
 */
@Component
@Slf4j
public class MatcherReplyAgentHandler implements IAgentHandler {

    IAgentHandler nextHandler;

    @Autowired
    AgentFacade agentFacade;

    @Autowired
    MatcherWrapper matcherWrapper;

    @Autowired
    AiChatMatcherLogService aiChatMatcherLogService;

    @Autowired
    private AiAgentPoolService aiAgentPoolService;

    @Override
    public void setNext(IAgentHandler iAgentHandler) {
        this.nextHandler = iAgentHandler;
    }

    @Override
    public Flux<ChatMessage> handle(AgentHandlerContext context) {

        //人才推荐
        if (ViewerAgentConstant.MATCHER_TALENT_RECOMMEDN_CODE.equals(context.getIntentionVO().getAgentCode())) {
            return nextHandler.handle(context);
        }
        AgentContext agentContext = new AgentContext();
        agentContext.setUserMsg(context.getAiChatUserMsg().getMsg());
        agentContext.setAgentCode(context.getIntentionVO().getAgentCode());
        agentContext.setHistory(context.getMessages());
        agentContext.setAiAgentPool(context.getAiAgentPool());
        log.info("matcher agent reply start. chatId:{}, agentCode:{}", context.getAiChatUserMsg().getChatId(), agentContext.getAgentCode());
        String prompt = agentFacade.getPrompt(context.getAiAgentPool().getAgentCode());
//        prompt = prompt.replace("$history", JSONObject.toJSONString(context.getMessages()));
        JSONObject input = new JSONObject();
        input.put("userMsg", context.getAiChatUserMsg().getMsg());
        if (context.getAiChatUserMsg().getCardInfos() != null && !context.getAiChatUserMsg().getCardInfos().isEmpty()) {
            input.put("cardInfo", context.getAiChatUserMsg().getCardInfos());
        }
        prompt = prompt.replace("$userInput", input.toString());
        IWiseMatchWrapper wiseMatchWrapper = matcherWrapper.wrap(agentContext.getAgentCode());
        agentContext.setPrompt(wiseMatchWrapper.promptWrap(context, prompt));
        Flux<String> reply = agentFacade.supplyStream(agentContext).map(x -> {
            x = x.replaceAll("\n\n", "\n").replaceAll("\n\n\n", "\n");
//            if (x.endsWith("\n")) {
//                x = x.replaceAll("\n", "");
//            }
            context.getReply().append(x);
            return x;
        }).doFinally(x -> {
            try {
                AiChatMatcherLog matcherLog = new AiChatMatcherLog();
                matcherLog.setSysMsg(context.getReply().toString());
                matcherLog.setUserMsg(context.getAiChatUserMsg().getMsg());
                matcherLog.setConversationId(context.getAiChatUserMsg().getChatId());
                matcherLog.setAgentId(agentContext.getAiAgentPool().getAgentCode());
                matcherLog.setAgentName(agentContext.getAiAgentPool().getAgentName());
                matcherLog.setPrompt(agentContext.getPrompt());
                matcherLog.setCost(System.currentTimeMillis() - context.getStart());
                if (context.getIntentionVO() != null) {
                    matcherLog.setIntention(context.getIntentionVO().getMsg());
                    matcherLog.setIntentionCode(context.getIntentionVO().getCode());
                    matcherLog.setIntentionName(context.getIntentionVO().getName());
                }
                aiChatMatcherLogService.supplyAsync(matcherLog);
            }catch (Exception e){
                log.error("Matcher日志异常", e);
            }
        });
        return wiseMatchWrapper.format(context.getAiChatUserMsg().getChatId(), reply);
    }
}
