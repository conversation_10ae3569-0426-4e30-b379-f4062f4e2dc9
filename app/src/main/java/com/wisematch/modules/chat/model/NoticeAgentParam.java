package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 通知命令智能体参数
 * <AUTHOR>
 * @version ChatParams.java, v0.1 2025-06-17 18:19
 */
@Data
public class NoticeAgentParam {

    /**
     * roomId
     */
    @Schema(description = "roomId")
    String roomId;

    /**
     * taskId
     */
    @Schema(description = "taskId")
    String taskId = "startAgent";

    /**
     * interrupt：打断智能体。
     * function：传回工具调用信息指令。
     * ExternalTextToSpeech ： 传入文本信息供 TTS 音频播放。使用方法参看自定义语音播放。
     * ExternalPromptsForLLM：传入自定义文本与用户问题拼接后送入 LLM。
     * ExternalTextToLLM：传入外部问题送入 LLM。根据你设定的优先级决定替代用户问题或增加新一轮对话。
     * FinishSpeechRecognition：触发新一轮对话。
     */
    String Command = "ExternalTextToSpeech";

    /**
     * Command 取值为 function、ExternalTextToSpeech、ExternalPromptsForLLM和ExternalTextToLLM时，Message必填。
     * 当 Command 取值为 function时，Message 格式需为 Json 转译字符串，例如：
     * "{\"ToolCallID\":\"call_cx\",\"Content\":\"上海天气是台风\"}"
     * 其他取值时格式为普通字符串，例如你刚才的故事讲的真棒。"
     * 当 Command 取值为 ExternalTextToSpeech时，message 传入内容建议不超过 200 个字符。
     */
    String Message;


}
