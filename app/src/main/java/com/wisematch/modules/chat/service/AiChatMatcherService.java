package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiChatMatcher;
import com.wisematch.modules.chat.model.*;
import reactor.core.publisher.Flux;

import java.util.List;


/**
 * AI面试服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */
public interface AiChatMatcherService extends IService<AiChatMatcher> {

   HomeMatcher index();

   AiChatMatcher init(ChatInitParams chatInitParams);

   Flux<ChatMessage> stream(AiChatUserMsg aiChatUserMsg);

   boolean stopStream(String msgId);

   List<ChatMessage<CardInfoVO>> messages(String chatId);

   List<AiChatRecord> matcherList();

   Page<AiChatRecord> historyPage(HistoryPageDTO historyPageDTO);

}
