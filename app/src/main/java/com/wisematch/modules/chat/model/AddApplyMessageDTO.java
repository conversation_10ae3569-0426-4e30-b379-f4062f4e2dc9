package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AddApplyMessageDTO {

    @Schema(description = "内容", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String content;

    @Schema(description = "接收者id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String receiverId;

    @Schema(description = "来源业务id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sourceId;

}
