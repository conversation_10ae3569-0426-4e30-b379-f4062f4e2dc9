package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.utils.PageUtils;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.entity.AiMatcherSuggest;
import com.wisematch.modules.chat.mapper.AiMatcherSuggestMapper;
import com.wisematch.modules.chat.model.AiMatcherSuggestQueryDTO;
import com.wisematch.modules.chat.service.AiMatcherSuggestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class AiMatcherSuggestServiceImpl extends ServiceImpl<AiMatcherSuggestMapper, AiMatcherSuggest> implements AiMatcherSuggestService {
    @Override
    public Page<AiMatcherSuggest> pageQuery(AiMatcherSuggestQueryDTO aiMatcherSuggestQueryDTO) {
        QueryWrapper<AiMatcherSuggest> wrapper = new QueryWrapper<AiMatcherSuggest>();

        aiMatcherSuggestQueryDTO.setUserId(UserInfoUtils.getCurrentUserId());
        if (ObjectUtils.isNotNull(aiMatcherSuggestQueryDTO.getTrainId())) {
            wrapper.lambda().eq(AiMatcherSuggest::getTrainId, aiMatcherSuggestQueryDTO.getTrainId());
        }
        if (ObjectUtils.isNotNull(aiMatcherSuggestQueryDTO.getPositionId())) {
            wrapper.lambda().eq(AiMatcherSuggest::getPositionId, aiMatcherSuggestQueryDTO.getPositionId());
        }

        return  PageUtils.doPage(this, aiMatcherSuggestQueryDTO, wrapper);
    }

    @Override
    public void batchLogicDelete(List<String> ids) {
        this.baseMapper.batchLogicDelete(ids);
    }

}
