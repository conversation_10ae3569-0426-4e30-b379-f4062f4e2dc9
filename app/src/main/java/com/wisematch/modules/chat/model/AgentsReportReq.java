package com.wisematch.modules.chat.model;

import lombok.Data;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

/**
 * 请求类：Java 驼峰字段 → JSON 下划线字段（序列化）
 */
@Data
// 核心注解：指定序列化/反序列化的命名策略为蛇形（下划线）
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AgentsReportReq {

    // 序列化后 → grouped_history
    private String groupedHistory;

    // 序列化后 → examine_data
    private String examineData;

    // 序列化后 → jd_data
    private String jdData;

    // 序列化后 → job_name
    private String jobName;
}