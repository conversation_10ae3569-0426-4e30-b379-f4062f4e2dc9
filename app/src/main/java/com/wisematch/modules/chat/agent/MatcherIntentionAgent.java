package com.wisematch.modules.chat.agent;

import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatOptions;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.modules.chat.entity.AiAgentPool;
import com.wisematch.modules.chat.entity.AiChatIntention;
import com.wisematch.modules.chat.enums.IntentionBizScene;
import com.wisematch.modules.chat.enums.MatcherAgentConstant;
import com.wisematch.modules.chat.enums.WiserAgentConstant;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.model.IntentionVO;
import com.wisematch.modules.chat.service.AiAgentPoolService;
import com.wisematch.modules.chat.service.AiChatIntentionService;
import com.wisematch.modules.chat.service.AiChatWiserMemoryService;
import com.wisematch.modules.chat.enums.ViewerAgentConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 意图识别agent门面
 * <AUTHOR>
 * @version IntentionAgentFacade.java, v0.1 2025-07-11 11:02
 */
@Component
@Slf4j
public class MatcherIntentionAgent {

    @Autowired
    private AiAgentPoolService aiAgentPoolService;
    @Autowired
    private AiChatIntentionService aiChatIntentionService;
    @Autowired
    private AiChatWiserMemoryService aiChatWiserMemoryService;
    @Autowired
    AgentFacade agentFacade;
    /**
     * 意图识别
     * @param context
     * @param userMsg
     * @return
     */
    public IntentionVO getIntention(AgentHandlerContext context, String userMsg){
        long start = System.currentTimeMillis();
        AiAgentPool intention = aiAgentPoolService.getByCode(MatcherAgentConstant.MATCHER_INTENTION_AGENT_CODE);
        log.info("matcher intention agentName: {}, agentId: {}, model: {}", intention.getAgentName(), intention.getId(), intention.getModel());

        String prompt = intention.getPrompt();
        prompt = prompt.replace("$userInput",userMsg);

        AgentContext agentContext = new AgentContext();
        agentContext.setPrompt(prompt);
        agentContext.setUserMsg(userMsg);
        agentContext.setAgentCode(MatcherAgentConstant.MATCHER_INTENTION_AGENT_CODE);
        agentContext.setHistory(context.getMessages());
        String reply =  agentFacade.supply(agentContext);

        IntentionVO intentionVO = new IntentionVO();
        intentionVO.setAgentCode(MatcherAgentConstant.DEFAULT_MATCHER);
        AiChatIntention aiChatIntention = aiChatIntentionService.getIntention(reply, IntentionBizScene.matcher.name());
        if (aiChatIntention != null) {
            intentionVO.setCode(aiChatIntention.getCode());
            intentionVO.setAgentId(aiChatIntention.getAgentId());
            AiAgentPool aiAgentPool = aiAgentPoolService.getById(aiChatIntention.getAgentId());
            intentionVO.setAgentCode(aiAgentPool.getAgentCode());
            log.info("matcher intention reply:{}, agentCode:{}", reply, intentionVO.getAgentCode());
        }
        log.info("意图识别耗时:" + (System.currentTimeMillis() - start) + "ms");
        return intentionVO;
    }

}
