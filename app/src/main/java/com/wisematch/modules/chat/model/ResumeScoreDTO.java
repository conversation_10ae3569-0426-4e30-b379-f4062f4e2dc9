package com.wisematch.modules.chat.model;

import lombok.Data;

import java.util.List;

@Data
public class ResumeScoreDTO {

    private ScoreDimension base;
    private ScoreDimension education;
    private ScoreDimension work;
    private ScoreDimension skills;
    private ScoreDimension extra;
    private ScoreDimension logic;

    /**
     * 计算最终分数：所有评分项的总和加上100分基础分
     * @return 最终得分
     */
    public int calculateFinalScore() {
        int total = 100; // 基础分数

        // 累加各个维度的分数
        total += sumDimensionScores(base);
        total += sumDimensionScores(education);
        total += sumDimensionScores(work);
        total += sumDimensionScores(skills);
        total += sumDimensionScores(extra);
        total += sumDimensionScores(logic);

        return total;
    }

    /**
     * 计算单个维度的总分
     * @param dimension 评分维度
     * @return 该维度的总分，若维度为null则返回0
     */
    private int sumDimensionScores(ScoreDimension dimension) {
        if (dimension == null || dimension.getDetails() == null) {
            return 0;
        }

        int sum = 0;
        for (ScoreDetail detail : dimension.getDetails()) {
            if (detail != null && detail.getScore() != null) {
                sum += detail.getScore();
            }
        }
        return sum;
    }

    /**
     * 评分维度内部类
     * 每个维度包含一个评分详情列表
     */
    @Data
    public static class ScoreDimension {
        private List<ScoreDetail> details;
    }

    /**
     * 评分详情内部类
     * 包含具体的分数和评分原因
     */
    @Data
    public static class ScoreDetail {
        private Integer score;
        private String reason;
    }
}
