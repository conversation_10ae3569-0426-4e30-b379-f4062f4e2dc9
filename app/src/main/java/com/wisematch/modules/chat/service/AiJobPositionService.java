package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiJobPosition;
import com.wisematch.modules.chat.model.AiJobPositionQueryDTO;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.chat.model.PositionListVO;
import com.wisematch.modules.chat.model.PublishDeactivationDTO;
import org.springframework.ai.chat.messages.Message;

import java.util.List;


/**
 * AI智能体服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */
public interface AiJobPositionService extends IService<AiJobPosition>,PreferenceShow {

    AiJobPosition saveObj(AiJobPosition aiJobPosition);

    int updateObjById(AiJobPosition aiJobPosition);

    List<CardInfo> getList();
    List<CardInfo> getHomeList();

    Page<PositionListVO> pageQueryBack(AiJobPositionQueryDTO aiJobPosition);

    CardInfo getCardInfoById(String id);

    Page<CardInfo> pageQueryFront(AiJobPositionQueryDTO aiJobPosition);

    List<AiJobPosition> selectAll(AiJobPositionQueryDTO aiJobPosition);

    void logicDelete(String ids);

    void batchLogicDelete(List<String> ids);

    Page<CardInfo> getByJobViewId(AiJobPositionQueryDTO aiJobPosition);

    List<CardInfo> positionToCardInfo(List<AiJobPosition> aiJobPositions);

    void publishDeactivation(PublishDeactivationDTO dto);

    List<CardInfo> getRecommend(String chatId, String userMsg, String resume, List<Message> messages);

    AiJobPosition detail(String id);
}
