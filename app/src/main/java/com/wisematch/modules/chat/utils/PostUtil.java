package com.wisematch.modules.chat.utils;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.http.HttpRequest.BodyPublishers;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.StringJoiner;

public class PostUtil {

    public static HttpResponse<String> postUrlencoded(String url, Map<String, Object> params) throws IOException, InterruptedException {

        // 创建 HTTP 客户端
        HttpClient client = HttpClient.newHttpClient();

        StringJoiner form = new StringJoiner("&");
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            form.add(URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8)
                    + "=" +
                    URLEncoder.encode(entry.getValue().toString(), StandardCharsets.UTF_8));
        }

        // 构建 POST 请求
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("Content-Type", "application/x-www-form-urlencoded")
                .POST(BodyPublishers.ofString(form.toString()))
                .build();

//        // 输出响应信息
//        System.out.println("HTTP 状态码: " + response.statusCode());
//        System.out.println("响应内容(JSON): " + response.body());
        return client.send(request, HttpResponse.BodyHandlers.ofString());
    }


}
