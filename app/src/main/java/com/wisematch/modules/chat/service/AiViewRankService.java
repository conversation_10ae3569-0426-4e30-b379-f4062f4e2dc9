package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiViewRank;
import com.wisematch.modules.chat.entity.AiViewReport;
import com.wisematch.modules.chat.model.AiViewRankQueryDTO;



public interface AiViewRankService extends IService<AiViewRank> {

    IPage<AiViewRank> getUserAllRanks(AiViewRankQueryDTO queryDTO);

    AiViewRank saveRank(AiViewReport aiViewReport);
}
