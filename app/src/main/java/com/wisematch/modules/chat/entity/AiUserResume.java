package com.wisematch.modules.chat.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ai_user_resume")
@Schema(description = "用户简历表")
public class AiUserResume {

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "在线简历")
    private String resumeJson;

    @Schema(description = "附件简历")
    private String resumeUrl;

    @Schema(description = "简历文本")
    private String resumeText;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "是否公开，1：公开，0：不公开")
    private Integer isOpen;

    @Schema(description = "是否删除，0:存在,1:不存在")
    private Integer isDel;

    @Schema(description = "简历解析状态")
    private Integer isAnalysis;

    @Schema(description = "简历评分")
    private Integer resumeScore;

    @Schema(description = "完整度评分")
    private Integer completenessScore;

}
