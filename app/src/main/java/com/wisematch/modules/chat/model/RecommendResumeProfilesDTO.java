package com.wisematch.modules.chat.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 推荐场景-简历主档案DTO
 * 对应Milvus集合：geek_recommendation_resume_profiles
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RecommendResumeProfilesDTO {

    /**
     * 用户唯一标识（长度不超过50字符）
     */
    private String userId;

    /**
     * 房间ID
     */
    private String roomId;

    /**
     * 用户删除状态
     * 对应Milvus Int32类型
     */
    private Integer delStatus;

    /**
     * 用户状态
     * 对应Milvus Int32类型
     */
    private Integer status;

    /**
     * 画像状态
     * 对应Milvus Int32类型
     */
    private Integer portraitStatus;

    /**
     * 画像审核状态
     */
    private Integer portraitVerifyStatus;


    /**
     * 岗位胜任力模型关联ID（与userId共同标识人才实体，长度不超过50字符）
     */
    private String examineId;

    /**
     * 用户性别（0=未知，1=男，2=女）
     * 用户性别（0=未知，1=男，2=女）
     */
    private Integer sex;

    /**
     * 最高学历（编码值，需结合业务字典解析）
     */
    private Integer highestEducation;

    /**
     * 期望薪资低点
     */
    private Integer salaryMin;

    /**
     * 期望薪资高点
     */
    private Integer salaryMax;

    /**
     * 工作方式（编码值，需结合业务字典解析，如1=全职，2=兼职）
     */
    private Integer workWay;

    /**
     * 工作经验
     */
    private String workExperience;

    /**
     * 原始文本内容（简历主档案相关文本，长度不超过300字符）
     */
    private String textContent;

    /**
     * 文本类型（标识textContent的内容类型，长度不超过50字符）
     */
    private String textType;

    /**
     * 简历主档案向量（768维浮点向量，用于Milvus推荐场景向量检索）
     */
    private List<Double> profilesVector;
}
