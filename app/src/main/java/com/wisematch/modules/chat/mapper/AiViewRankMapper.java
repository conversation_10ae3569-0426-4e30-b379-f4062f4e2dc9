package com.wisematch.modules.chat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wisematch.modules.chat.entity.AiViewRank;
import com.wisematch.modules.chat.model.AiViewRankQueryDTO;
import com.wisematch.modules.chat.model.AiViewRankWithRankVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AiViewRankMapper extends BaseMapper<AiViewRank> {

    /**
     * 根据 city 和 position 动态条件分页查询整体排名
     */
    List<AiViewRankWithRankVO> selectRankedByCityPosition(Page<AiViewRankWithRankVO> page,
                                                          @Param("dto") AiViewRankQueryDTO dto);

    /**
     * 根据 city, position, user_id 查询当前用户排名及信息
     */
    AiViewRankWithRankVO selectUserRankByCityPositionUserId(
            @Param("dto") AiViewRankQueryDTO dto);

    /**
     * 根据 user_id 查询该用户所有 city 和 position 的排名（分页）
     */
    List<AiViewRankWithRankVO> selectRanksByUserId(Page<AiViewRankWithRankVO> page,
                                                   @Param("dto") AiViewRankQueryDTO dto);

}
