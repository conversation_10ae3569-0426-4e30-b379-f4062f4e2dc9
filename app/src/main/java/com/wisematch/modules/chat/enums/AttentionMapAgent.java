package com.wisematch.modules.chat.enums;

import java.util.HashMap;
import java.util.Map;

public abstract class AttentionMapAgent {
    public static Map<Integer,String> map = new HashMap<>();
    public static String USER = "USER";
    public static String ASSISTANT = "ASSISTANT";

    public static String SMALL_TALK_MODEL = "Small_Talk_Model";
    public static String JOB_RECOMMEND_MODEL = "JOB_RECOMMEND_MODEL";
    public static String INTERVIEW_RECOMMEND_MODEL = "Interview_Recommend_Model";
    public static String NOT_INCLUDE_RESUME = "NOT_INCLUDE_RESUME";

    public static String RESUME_ANALYSIS_MODEL = "RESUME_ANALYSIS_MODEL";
    public static String REPORT = "report";

    public static String PORTRAIT = "portrait";


    static {
        map.put(0,"Small_Talk_Model");
        map.put(1,"Job_Recommend_Model");
        map.put(2,"Interview_Recommend_Model");
        map.put(3,"NOT_INCLUDE_RESUME");
    }

}
