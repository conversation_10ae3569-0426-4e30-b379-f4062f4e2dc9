package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "企业组织信息")
public class CertifiedEnterpriseDTO {
/*

    @Schema(description = "企业徽标", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String organizationLogo;
//
//    @Schema(description = "企业名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
//    private String organizationName;
//
//    @Schema(description = "唯一社会信用代码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
//    private String uniqueSocialCreditCode;

//    @Schema(description = "营业执照", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
//    private String businessLicense;

    @Schema(description = "企业介绍", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String organizationIntroduction;
//
//    @Schema(description = "地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
//    private String businessAddress;

    @Schema(description = "官网链接", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String businessUrl;

    @Schema(description = "是否公开企业信息: 0不公开，1公开", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer isOpen;
*/

    @Schema(description = "唯一社会信用代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String uniqueSocialCreditCode;

    @Schema(description = "地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String businessAddress;

    @Schema(description = "企业名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String organizationName;

    @Schema(description = "法人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String legalPerson;

    @Schema(description = "法人身份证", requiredMode = Schema.RequiredMode.REQUIRED)
    private String legalPersonIdCard;

    @Schema(description = "邀请码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invitationCode;

}
