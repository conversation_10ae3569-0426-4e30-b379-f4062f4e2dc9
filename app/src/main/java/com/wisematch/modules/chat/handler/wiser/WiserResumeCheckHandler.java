package com.wisematch.modules.chat.handler.wiser;

import com.wisematch.modules.chat.enums.CardType;
import com.wisematch.modules.chat.enums.MsgRole;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.handler.IAgentHandler;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.service.AiUserResumeService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.Date;
import java.util.List;

/**
 * 简历监测
 * <AUTHOR>
 * @version ResumeCheckHandler.java, v0.1 2025-07-15 16:49
 */
@Component
public class WiserResumeCheckHandler implements IAgentHandler {

    IAgentHandler nextHandler;

    @Autowired
    private AiUserResumeService aiUserResumeService;

    @Override
    public void setNext(IAgentHandler iAgentHandler) {
        this.nextHandler = iAgentHandler;
    }

    @Override
    public Flux<ChatMessage> handle(AgentHandlerContext context) {
        String resume = aiUserResumeService.getCurrentOpenedOrText(context.getAiChatUserMsg().getUserId());
        if (StringUtils.isBlank(resume)) {
            ChatMessage replyViewMessage = new ChatMessage();
            replyViewMessage.setMsg("目前尚未提供简历内容，因此无法根据您的背景提供更加准确的信息，建议您补充简历信息");
            replyViewMessage.setRole(MsgRole.assistant.name());
            //replyViewMessage输出加
            replyViewMessage.setChatId(context.getAiChatUserMsg().getChatId());
            replyViewMessage.setDateTime(new Date());

            CardInfo cardInfo = new CardInfo();
            cardInfo.setTitle("请补充您的简历信息");
            cardInfo.setCardType(CardType.RESUME_UPLOAD.name());
            replyViewMessage.setCardInfos(List.of(cardInfo));
            return Flux.just(replyViewMessage);
        }
        return nextHandler.handle(context);
    }
}
