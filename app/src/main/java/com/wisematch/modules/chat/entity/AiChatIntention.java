package com.wisematch.modules.chat.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ai_chat_intention")
@Schema(description = "聊天意图管理")
public class AiChatIntention {

    @ExcelProperty("ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    public String id;

    @Schema(description = "意图码")
    public String code;

    @Schema(description = "意图描述")
    public String intention;

    @Schema(description = "回复策略")
    public String strategy;

    @Schema(description = "agentId")
    public String agentId;

    @Schema(description = "业务场景")
    public String bizScene;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date createTime;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date updateTime;

}
