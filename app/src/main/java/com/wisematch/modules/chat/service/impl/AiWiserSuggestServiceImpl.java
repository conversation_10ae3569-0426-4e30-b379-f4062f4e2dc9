package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.modules.chat.entity.AiWiserSuggest;
import com.wisematch.modules.chat.mapper.AiWiserSuggestMapper;
import com.wisematch.modules.chat.model.AiWiserSuggestQueryDTO;
import com.wisematch.modules.chat.service.AiWiserSuggestService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class AiWiserSuggestServiceImpl extends ServiceImpl<AiWiserSuggestMapper, AiWiserSuggest> implements AiWiserSuggestService {


    @Override
    public void batchLogicDelete(List<String> ids) {
        this.baseMapper.batchLogicDelete(ids);
    }
    @Override
    public Page<AiWiserSuggest> pageQuery(AiWiserSuggestQueryDTO dto) {
        Page<AiWiserSuggest> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        QueryWrapper<AiWiserSuggest> wrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(dto.getCompanyId())) {
            wrapper.lambda().eq(AiWiserSuggest::getCompanyId, dto.getCompanyId());
        }
        if (StringUtils.isNotBlank(dto.getCustomerId())) {
            wrapper.lambda().eq(AiWiserSuggest::getCustomerId, dto.getCustomerId());
        }
        if (dto.getThumbsUp() != null) {
            wrapper.lambda().eq(AiWiserSuggest::getThumbsUp, dto.getThumbsUp());
        }
        if (dto.getCollect() != null) {
            wrapper.lambda().eq(AiWiserSuggest::getCollect, dto.getCollect());
        }

        return this.page(page, wrapper);
    }
}

