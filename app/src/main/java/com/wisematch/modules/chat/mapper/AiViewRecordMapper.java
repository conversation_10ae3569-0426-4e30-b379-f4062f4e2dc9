package com.wisematch.modules.chat.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wisematch.modules.chat.entity.AiViewRecord;
import com.wisematch.modules.chat.model.InterviewReport;
import org.apache.ibatis.annotations.Param;

/**
 * AI面试
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年9月18日 上午9:34:11
 */
public interface AiViewRecordMapper extends BaseMapper<AiViewRecord> {

    InterviewReport report(@Param("ew") QueryWrapper<AiViewRecord> entityWrapper);

}
