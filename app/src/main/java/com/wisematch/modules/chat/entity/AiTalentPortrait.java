package com.wisematch.modules.chat.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ai_talent_portrait")
@Schema(description = "人才画像表")
public class AiTalentPortrait {

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "简历内容")
    private String resume;

    @Schema(description = "面试画像数据")
    private String portrait;

    @Schema(description = "面试评分")
    private Integer chatScore;

    @Schema(description = "是否入人才池（0：否，1：是）")
    private Integer status;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Schema(description = "最新一次面试ID")
    private String interviewId;
}
