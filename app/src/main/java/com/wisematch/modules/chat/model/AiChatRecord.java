package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version AiChatMatcherRecord.java, v0.1 2025-06-28 15:12
 */
@Data
public class AiChatRecord {

    @Schema(description = "会话id")
    public String chatId;

    @Schema(description = "聊天记录")
    public String msg;

    @Schema(description = "更新时间")
    public Date  updateTime;
}
