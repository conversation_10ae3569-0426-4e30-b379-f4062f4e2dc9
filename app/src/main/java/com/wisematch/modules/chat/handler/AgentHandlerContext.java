package com.wisematch.modules.chat.handler;

import com.wisematch.modules.chat.entity.AiAgentPool;
import com.wisematch.modules.chat.model.AiChatUserMsg;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.model.IntentionVO;
import lombok.Data;
import org.springframework.ai.chat.messages.Message;

import java.util.List;

/**
 * <AUTHOR>
 * @version AgentContext.java, v0.1 2025-07-15 17:23
 */
@Data
public class AgentHandlerContext {

    long start = System.currentTimeMillis();

    String question;

    AiChatUserMsg aiChatUserMsg;

    IntentionVO intentionVO;

    AiAgentPool aiAgentPool;

    List<ChatMessage> replyMessages;

    String replyId;

    StringBuffer reply = new StringBuffer();

    List<Message> messages;
}
