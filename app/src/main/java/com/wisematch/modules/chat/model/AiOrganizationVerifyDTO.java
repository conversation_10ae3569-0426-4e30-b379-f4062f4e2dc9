package com.wisematch.modules.chat.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.wisematch.modules.common.handler.businessLicenseVerify.BusinessLicenseContext;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AiOrganizationVerifyDTO extends AiOrganizationInsertDTO{

    @Schema(description = "0未审核，1已认证，2驳回")
    private Integer isCertified;

    @TableId(type = IdType.ASSIGN_UUID)
    @Schema(description = "主键ID")
    private String id;

    public static AiOrganizationVerifyDTO generateAiOrganization(BusinessLicenseContext businessLicenseContext){
        AiOrganizationVerifyDTO aiOrganizationInsertDTO = new AiOrganizationVerifyDTO();
        aiOrganizationInsertDTO.setBusinessLicense(businessLicenseContext.getFileSrc());
        aiOrganizationInsertDTO.setOrganizationName(businessLicenseContext.getCompanyName());
        aiOrganizationInsertDTO.setUniqueSocialCreditCode(businessLicenseContext.getCreditCode());
        aiOrganizationInsertDTO.setIsCertified(businessLicenseContext.getIsCertified());
        aiOrganizationInsertDTO.setBusinessAddress(businessLicenseContext.getBusinessAddress());
        return aiOrganizationInsertDTO;
    }
}
