package com.wisematch.modules.chat.model;

import com.wisematch.common.utils.DesensitizationUtils;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.model.PrivacyProtectionChangeDTO;
import lombok.Data;
import opennlp.tools.util.StringUtil;
import org.apache.commons.lang.StringUtils;

import java.util.List;

@Data
public class ResumeDTO {
    private String skills;
    private Brief brief;
    private String workYears;
    private String educationalTitle;
    private List<EduExperience> eduExperience;
    private List<WorkExperience> workExperience;
    private String userName;
    private List<ProjectExperience> projectExperience;
    private List<SchoolExperience> schoolExperience;
    private String extraInfo;
    private Integer sex;

    @Data
    public static class Brief {
        private String comments;
        private String educationalTitle;
        private String salary;
        private String skills;
        private String userName;
        private String workWay;
        private String workYears;
    }

    @Data
    public static class EduExperience {
        private String endDate;
        private String name;
        private String label;
        private String startDate;
        private String content;
    }

    @Data
    public static class WorkExperience {
        private String endDate;
        private String name;
        private String label;
        private String startDate;
        private String content;
    }

    @Data
    public static class ProjectExperience {
        private String endDate;
        private String name;
        private String label;
        private String startDate;
        private String content;
    }

    @Data
    public static class SchoolExperience {
        private String endDate;
        private String name;
        private String label;
        private String startDate;
        private String content;
    }

    public CardInfo addToCard(SysUser sysUser, CardInfo cardInfo){

        PrivacyProtectionChangeDTO protection = PrivacyProtectionChangeDTO.getByString(
                sysUser.getPrivacyProtection());
        cardInfo.setTitle(protection.getNameProtection() == 0 ?
                this.getUserName() : DesensitizationUtils.name(this.getUserName()));
        cardInfo.setLogo(protection.getPhotoProtection() == 0 ?
                sysUser.getPhoto() : DesensitizationUtils.photo(sysUser.getPhoto()));

        // 处理工作年限
        if (this.getWorkYears() != null && !StringUtil.isEmpty(this.getWorkYears())) {
            cardInfo.setKeyLabel(isEmpty(cardInfo.getKeyLabel())
                    ? this.getWorkYears()
                    : cardInfo.getKeyLabel() + " | " + this.getWorkYears());
        }

        // 处理教育程度
        if (this.getEducationalTitle() != null && !StringUtil.isEmpty(this.getEducationalTitle())) {
            cardInfo.setKeyLabel(isEmpty(cardInfo.getKeyLabel())
                    ? this.getEducationalTitle()
                    : cardInfo.getKeyLabel() + " | " + this.getEducationalTitle());
        }

        // 处理薪资（注意薪资在brief对象中）
        if (this.getBrief() != null
                && this.getBrief().getSalary() != null
                && !StringUtil.isEmpty(this.getBrief().getSalary())) {

            cardInfo.setKeyLabel(isEmpty(cardInfo.getKeyLabel())
                    ? this.getBrief().getSalary()
                    : cardInfo.getKeyLabel() + " | " + this.getBrief().getSalary());
        }
        return cardInfo;
    }

    private boolean isEmpty(String str) {
        if (str == null) {
            return true;
        }
        return StringUtils.isEmpty(str);
    }
}

