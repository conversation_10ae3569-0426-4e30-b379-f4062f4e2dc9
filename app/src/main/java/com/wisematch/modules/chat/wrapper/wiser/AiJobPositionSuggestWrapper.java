package com.wisematch.modules.chat.wrapper.wiser;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.wisematch.common.utils.BeanCopyUtils;
import com.wisematch.common.utils.MybatisUtils;
import com.wisematch.modules.chat.entity.AiJobPosition;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.service.AiChatWiserMemoryService;
import com.wisematch.modules.chat.service.AiJobPositionService;
import com.wisematch.modules.chat.service.AiUserResumeService;
import com.wisematch.modules.chat.wrapper.AbstractWiseMatchWrapper;
import com.wisematch.modules.common.utils.JsonUtils;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 岗位推荐
 */
@Component
@Data
@Slf4j
public class AiJobPositionSuggestWrapper extends AbstractWiseMatchWrapper {

    @Autowired
    AiJobPositionService aiJobPositionService;

    @Resource
    private AiUserResumeService aiUserResumeService;

    @Resource
    AiChatWiserMemoryService aiChatMemoryService;

    @Override
    public String promptWrap(AgentHandlerContext context, String prompt) {
        String resume = aiUserResumeService.getCurrentOpenedOrText(context.getAiChatUserMsg().getUserId());
        if (prompt.contains("$jobDocuments")) {
            //知识库向量检索
            // 使用ArrayList确保可修改性，收集用户消息
            List<Message> messages = context.getMessages().stream()
                    .filter(item -> MessageType.USER.equals(item.getMessageType()))
                    .collect(Collectors.toCollection(ArrayList::new));

            // 添加当前用户消息
            String currentMsg = context.getAiChatUserMsg().getMsg();
            messages.add(new UserMessage(currentMsg));

            List<CardInfo> list = aiJobPositionService.getRecommend(context.getAiChatUserMsg().getChatId(), "帮我识别用户需求", resume, messages);
            if (!list.isEmpty()) {
                prompt = prompt.replace("$jobDocuments", JsonUtils.toJson(list));
            } else {
                prompt = prompt.replace("$jobDocuments", "[]");
            }
        }
        if (prompt.contains("$resume")) {
            prompt = prompt.replace("$resume", resume);
        }
        return super.promptWrap(context, prompt);
    }

    @Override
    public ChatMessage replyWrap(ChatMessage chatMessage) {
        if (!JSONUtil.isTypeJSON(chatMessage.getMsg())) {
            return super.replyWrap(chatMessage);
        }
        JSONObject replys = JSONObject.parseObject(chatMessage.getMsg());
        try {
            if (replys.get("recommendJobId") != null) {
                List<String> rcmdId = (List<String>) replys.get("recommendJobId");
                if (null != rcmdId && !rcmdId.isEmpty()) {
                    List<AiJobPosition> aiJobTrains = MybatisUtils.listByIds(this.aiJobPositionService, rcmdId);
                    List<CardInfo> cardInfos = BeanCopyUtils.copyList(aiJobTrains, CardInfo::positionToCardInfo);
                    chatMessage.setCardInfos(cardInfos);
                    return super.replyWrap(chatMessage);
                }
            }
        } catch (Exception e) {
            log.error("岗位推荐异常", e);
        }
        return super.replyWrap(chatMessage);
    }
}
