package com.wisematch.modules.chat.wrapper.wiser;

import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.service.AiUserResumeService;
import com.wisematch.modules.chat.wrapper.AbstractWiseMatchWrapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 简历优化
 */
@Component
@Data
@Slf4j
public class AiResumeOptimizeWrapper extends AbstractWiseMatchWrapper {

    @Autowired
    private AiUserResumeService aiUserResumeService;

    @Override
    public String promptWrap(AgentHandlerContext context, String prompt) {
        if (prompt.contains("$resume")) {
            prompt = prompt.replace("$resume", aiUserResumeService.getCurrentOpenedOrText(context.getAiChatUserMsg().getUserId()));
        }
        return super.promptWrap(context, prompt);
    }
}
