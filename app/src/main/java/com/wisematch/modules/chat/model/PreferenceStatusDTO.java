package com.wisematch.modules.chat.model;

import com.wisematch.modules.chat.entity.AiUserPreferences;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PreferenceStatusDTO {

    private Integer stored;

    private Integer thumb;

    @Schema(description = "是否查看，0：未查看，1查看")
    private Integer viewStatus;

    public PreferenceStatusDTO() {
    }

    public PreferenceStatusDTO(Integer stored) {
        this.stored = stored;
    }

    public PreferenceStatusDTO(Integer stored, Integer thumb) {
        this.stored = stored;
        this.thumb = thumb;
    }

    public static PreferenceStatusDTO preferenceToDTO(AiUserPreferences preferences) {
        PreferenceStatusDTO dto = new PreferenceStatusDTO();
        if(preferences == null){
            return dto;
        }
        dto.setStored(preferences.getCollect());
        dto.setThumb(preferences.getThumbsUp());
        dto.setViewStatus(preferences.getViewStatus());
        return dto;
    }

}
