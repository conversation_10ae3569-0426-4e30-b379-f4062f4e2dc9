package com.wisematch.modules.chat.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.wisematch.common.model.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class NotifyCommunicatListDTO implements PageRequest {

    @Schema(description = "接收者id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String receiverId;

    @Schema(description = "消息类型(加入候选人TALENT、加入人才池PORTRAIT，报告生成REPORT" +
            "，系统消息SYSTEM、其他OTHER)", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("biz_sence")
    private String bizSence;

    @Schema(description = "起始页")
    private Integer pageNum = 1;

    @Schema(description = "页面大小")
    private Integer pageSize = 10;
}
