package com.wisematch.modules.chat.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class IsCommunicateVO implements Serializable {

    private String code;

    private Integer bizStatus;

    private String phone;

    public IsCommunicateVO(String code, String phone) {
        this.code = code;
        this.phone = phone;
    }

    public IsCommunicateVO() {
    }

    public IsCommunicateVO(String code) {
        this.code = code;
    }

    public IsCommunicateVO(String code, Integer bizStatus, String phone) {
        this.code = code;
        this.bizStatus = bizStatus;
        this.phone = phone;
    }
}
