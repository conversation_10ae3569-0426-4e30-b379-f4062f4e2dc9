package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.modules.chat.entity.AiViewPrepare;
import com.wisematch.modules.chat.mapper.AiViewPrepareMapper;
import com.wisematch.modules.chat.service.AiViewPrepareService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class AiViewPrepareServiceImpl extends ServiceImpl<AiViewPrepareMapper, AiViewPrepare> implements AiViewPrepareService {

    @Override
    public AiViewPrepare getByRoomId(String roomId) {
        QueryWrapper<AiViewPrepare> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiViewPrepare::getRoomId, roomId);
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<AiViewPrepare> getByRoomId(List<String> roomIds) {
        if (CollectionUtils.isEmpty(roomIds)) {
            return new ArrayList<>();
        }
        QueryWrapper<AiViewPrepare> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(AiViewPrepare::getRoomId, roomIds);
        return this.baseMapper.selectList(queryWrapper);
    }
}

