package com.wisematch.modules.chat.handler.wiser;

import com.alibaba.fastjson.JSONObject;
import com.wisematch.modules.chat.agent.AgentContext;
import com.wisematch.modules.chat.agent.AgentFacade;
import com.wisematch.modules.chat.entity.AiAgentPool;
import com.wisematch.modules.chat.entity.AiChatWiserLog;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.handler.IAgentHandler;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.service.AiAgentPoolService;
import com.wisematch.modules.chat.service.AiChatWiserLogService;
import com.wisematch.modules.chat.service.AiChatWiserMemoryService;
import com.wisematch.modules.chat.wrapper.IWiseMatchWrapper;
import com.wisematch.modules.chat.wrapper.WiserWrapperEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

/**
 * 简历监测
 *
 * <AUTHOR>
 * @version ResumeCheckHandler.java, v0.1 2025-07-15 16:49
 */
@Component
@Slf4j
public class WiserReplyAgentHandler implements IAgentHandler {

    IAgentHandler nextHandler;

    @Autowired
    private AiAgentPoolService aiAgentPoolService;

    @Autowired
    private AgentFacade agentFacade;

    @Autowired
    AiChatWiserMemoryService aiChatMemoryService;
    @Autowired
    private AiChatWiserLogService aiChatWiserLogService;

    @Override
    public void setNext(IAgentHandler iAgentHandler) {
        this.nextHandler = iAgentHandler;
    }

    @Override
    public Flux<ChatMessage> handle(AgentHandlerContext context) {
        log.info("wiser agent reply start. chatId:{}", context.getAiChatUserMsg().getChatId());

        AiAgentPool aiAgentPool;
        if (context.getIntentionVO().getAgentId() != null) {
            aiAgentPool = this.aiAgentPoolService.getById(context.getIntentionVO().getAgentId());
        } else {
            aiAgentPool = this.aiAgentPoolService.getByCode(context.getIntentionVO().getAgentCode());
        }
        log.info("wiser reply agentName: {}, agentId: {}, model: {}", aiAgentPool.getAgentName(), aiAgentPool.getId(), aiAgentPool.getModel());

        String prompt = aiAgentPool.getPrompt();
//        prompt = prompt.replace("$history", JSONObject.toJSONString(context.getMessages()));
        JSONObject input = new JSONObject();
        input.put("userMsg", context.getAiChatUserMsg().getMsg());
        if (context.getAiChatUserMsg().getCardInfos() != null && !context.getAiChatUserMsg().getCardInfos().isEmpty()) {
            input.put("cardInfo", context.getAiChatUserMsg().getCardInfos());
        }
        prompt = prompt.replace("$userInput", input.toString());
        IWiseMatchWrapper wrapper = WiserWrapperEnum.getWrapperByCode(aiAgentPool.getAgentCode());
        prompt = wrapper.promptWrap(context, prompt);

        AgentContext agentContext = new AgentContext();
        agentContext.setAgentCode(aiAgentPool.getAgentCode());
        agentContext.setUserMsg(context.getAiChatUserMsg().getMsg());
        agentContext.setAiAgentPool(aiAgentPool);
        agentContext.setPrompt(prompt);
        agentContext.setHistory(context.getMessages());
        Flux<String> reply = agentFacade.supplyStream(agentContext).map(x -> {
            x = x.replaceAll("\n\n", "\n").replaceAll("\n\n\n", "\n");
//            if (x.endsWith("\n")) {
//                x = x.replaceAll("\n", "");
//            }
            context.getReply().append(x);
            return x;
        }).doFinally(x -> {
            try {
                AiChatWiserLog wiserLog = new AiChatWiserLog();
                wiserLog.setSysMsg(context.getReply().toString());
                wiserLog.setUserMsg(context.getAiChatUserMsg().getMsg());
                wiserLog.setConversationId(context.getAiChatUserMsg().getChatId());
                wiserLog.setAgentId(agentContext.getAiAgentPool().getAgentCode());
                wiserLog.setAgentName(agentContext.getAiAgentPool().getAgentName());
                wiserLog.setPrompt(agentContext.getPrompt());
                wiserLog.setCost(System.currentTimeMillis() - context.getStart());
                if (context.getIntentionVO() != null) {
                    wiserLog.setIntention(context.getIntentionVO().getMsg());
                    wiserLog.setIntentionCode(context.getIntentionVO().getCode());
                    wiserLog.setIntentionName(context.getIntentionVO().getName());
                }
                aiChatWiserLogService.supplyAsync(wiserLog);
            }catch (Exception e){
                log.error("Wiser日志异常", e);
            }
        });
        return wrapper.format(context.getAiChatUserMsg().getChatId(), reply);
    }

}
