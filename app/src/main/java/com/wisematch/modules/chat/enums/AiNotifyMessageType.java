package com.wisematch.modules.chat.enums;

import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.List;

@Getter
public enum AiNotifyMessageType {
    TALENT("https://wise-match.oss-cn-hangzhou.aliyuncs.com/user/resume/2394cd1ed4da20b5752ca094af6cae3c/9b2e9928d7984a6eae1ef8a01c6c8346.png",
            "", "/ai/portrait/detail", "加入候选人"),
    PORTRAIT("https://wise-match.oss-cn-hangzhou.aliyuncs.com/user/resume/2394cd1ed4da20b5752ca094af6cae3c/fd885e2f6e74448ab1a57cf383dfa3ef.png",
            "", "/ai/portrait/detail", "加入人才池"),
    REPORT("https://wise-match.oss-cn-hangzhou.aliyuncs.com/user/resume/2394cd1ed4da20b5752ca094af6cae3c/1eb996d0a895429b81d1827ab9738795.png",
            "", "/ai/reporter/report", "报告生成"),
    OTHER("", "", "", "其他"),
    APPLY_VIDEO("https://wise-match.oss-cn-hangzhou.aliyuncs.com/user/resume/2394cd1ed4da20b5752ca094af6cae3c/265d2fcd2eac41c8adf59f0ded6c1a6f.png",
            "", "/ai/portrait/detail", "申请查看视频"),
    VX_EXCHANGE("https://wise-match.oss-cn-hangzhou.aliyuncs.com/user/resume/2394cd1ed4da20b5752ca094af6cae3c/a846454c0c114324ab68975fa32e82ab.png",
            "", "", "申请交换微信"),
    PHONE_EXCHANGE("https://wise-match.oss-cn-hangzhou.aliyuncs.com/user/resume/2394cd1ed4da20b5752ca094af6cae3c/382a8f9de9094e9f8e702b5541156e07.png",
            "", "", "申请交换电话号"),
    SYSTEM("", "", "", "系统消息"),
    ;

    private final String typeIcon;

    private final String type;

    private final String redirectUrl;

    private final String title;

    AiNotifyMessageType(String typeIcon, String type, String redirectUrl, String title) {
        this.typeIcon = typeIcon;
        this.type = type;
        this.redirectUrl = redirectUrl;
        this.title = title;
    }

    public static String paramList(){
        return String.join("，", listAllStr());
    }


    public static List<String> listAllStr(){
        return Arrays.stream(AiNotifyMessageType.values())
                .map(Enum::name)
                .toList();
    }

    public static void containsExam(String bizSence){
        if(StringUtils.isBlank(bizSence)){
            return;
        }
        if(!AiNotifyMessageType.listAllStr()
                .contains(bizSence)){
            throw new RRException(RRExceptionEnum.ILLEGAL_ARGUMENT);
        }
    }
}
