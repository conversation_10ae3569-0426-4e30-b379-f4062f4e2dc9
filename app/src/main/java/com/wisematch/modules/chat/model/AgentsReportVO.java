package com.wisematch.modules.chat.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.util.List;

/**
 * 响应类：JSON 下划线字段 → Java 驼峰字段（反序列化）
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class) // 核心：下划线转驼峰
public class AgentsReportVO {

    // 对应 JSON 顶层的 "dimensions" 数组
    private List<Dimension> dimensions;

    /**
     * 嵌套类：对应 dimensions 数组中的单个对象结构
     */
    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class) // 内部类也需单独加注解
    public static class Dimension {

        // JSON → labels → Java：labels（字段名一致，无需转换）
        private String labels;

        @JsonProperty("checkPoint")
        private String checkPoint;

        // JSON → underline → Java：underline（字段名一致）
        private String underline;

        // JSON → underline_detail → Java：underlineDetail（下划线转驼峰）
        private List<String> underlineDetail;

        // JSON → summary → Java：summary（字段名一致）
        private String summary;

        // JSON → comments → Java：comments（字段名一致）
        private String comments;

        // JSON → score → Java：score（字段名一致）
        private Integer score;
    }
}