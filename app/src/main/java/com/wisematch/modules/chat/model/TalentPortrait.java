package com.wisematch.modules.chat.model;

import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 人才画像
 * <AUTHOR>
 * @version TalentPortrait.java, v0.1 2025-06-22 14:53
 */
@Data
public class TalentPortrait {

    @Schema(description = "人才卡片")
    CardInfo cardInfo;
    /**
     * 人才简介
     */
    @Schema(description = "人才简介")
    PortraitBrief brief;

    /**
     * 面试记录+面试反馈
     */
    @Schema(description = "面试报告")
    JSONObject report;

    /**
     * 用户简历
     */
    @Schema(description = "用户简历")
    JSONObject resume;

    /**
     * 人才画像
     */
    @Schema(description = "人才画像")
    JSONObject portrait;

    /**
     * mbti
     */
    @Schema(description = "mbti")
    MbtiInfo mbtiInfo;

    public static PortraitBrief getBriefIfAbsent(TalentPortrait portrait){
        PortraitBrief brief = portrait.getBrief();
        if(null == brief){
            brief = new PortraitBrief();
            brief.setExtra(new JSONObject());
            return brief;
        }
        return brief;
    }

}