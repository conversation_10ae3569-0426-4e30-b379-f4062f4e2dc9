package com.wisematch.modules.chat.handler.wiser;

import com.wisematch.modules.chat.agent.AiWiserIntentionAgent;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.handler.IAgentHandler;
import com.wisematch.modules.chat.model.IntentionVO;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.service.AiChatWiserMemoryService;
import com.wisematch.modules.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * Wiser意图识别
 * <AUTHOR>
 * @version ResumeCheckHandler.java, v0.1 2025-07-15 16:49
 */
@Component
@Slf4j
public class WiserIntentionHandler implements IAgentHandler {

    IAgentHandler nextHandler;

    @Autowired
    private AiWiserIntentionAgent intentionAgentFacade;

    @Autowired
    private AiChatWiserMemoryService aiChatWiserMemoryService;

    @Override
    public void setNext(IAgentHandler iAgentHandler) {
        this.nextHandler = iAgentHandler;
    }

    @Override
    public Flux<ChatMessage> handle(AgentHandlerContext context) {
        log.info("wiser intention start, chatId: {}", context.getAiChatUserMsg().getChatId());
        IntentionVO intention = intentionAgentFacade.streamIntention(context, context.getAiChatUserMsg().getMsg());
        log.info("得到的意图为：" + JsonUtils.toJson(intention));
        context.setIntentionVO(intention);
        return nextHandler.handle(context);
    }
}
