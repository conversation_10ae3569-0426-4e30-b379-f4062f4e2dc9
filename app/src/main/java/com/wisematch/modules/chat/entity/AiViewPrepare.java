package com.wisematch.modules.chat.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wisematch.modules.chat.model.GenQuestion;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("ai_view_prepare")
@Schema(description = "面试准备")
public class AiViewPrepare {

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "面试候选人")
    private String userId;

    @Schema(description = "面试房间Id")
    private String roomId;

    @Schema(description = "考核维度Id")
    private String examineId;

    @Schema(description = "简历信息")
    private String resume;

    @Schema(description = "问题清单")
    private String questions;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
