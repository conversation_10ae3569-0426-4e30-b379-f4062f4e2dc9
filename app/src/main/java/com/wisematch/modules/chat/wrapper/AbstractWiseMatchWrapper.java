package com.wisematch.modules.chat.wrapper;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.service.AiUserResumeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public abstract class AbstractWiseMatchWrapper implements IWiseMatchWrapper {

    @Autowired
    AiUserResumeService userResumeService;

    private IWiseMatchWrapper nextWraper;

    @Override
    public String promptWrap(AgentHandlerContext context, String prompt) {
        if (prompt.contains("$resume")) {
            String resume = userResumeService.getCurrentOpenedOrText(context.getAiChatUserMsg().getUserId());
            prompt = prompt.replace("$resume", StringUtils.isBlank(resume) ? "" : resume);
        }
        return prompt;
    }

    public IWiseMatchWrapper setNext(IWiseMatchWrapper IWiseMatchWrapper) {
        return this.nextWraper = IWiseMatchWrapper;
    }

    @Override
    public ChatMessage replyWrap(ChatMessage chatMessage) {
        if (chatMessage.getCardInfos() != null && !chatMessage.getCardInfos().isEmpty()) {
            chatMessage.setMsg("");
        }
        String reply = chatMessage.getMsg();
        if (!reply.contains("tips") || !JSONUtil.isTypeJSON(reply)) {
            log.info("reply wrap:{}", reply);
            return chatMessage;
        }
        try {
            JSONObject replys = JSONObject.parseObject(reply);
            chatMessage.setTips((List<String>) replys.get("tips"));
            chatMessage.setMsg("");
        } catch (Exception e) {
            log.error("Tips提取异常", e);
        }
        return chatMessage;
    }
}
