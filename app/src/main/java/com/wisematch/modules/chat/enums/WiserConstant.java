package com.wisematch.modules.chat.enums;

import java.util.Arrays;
import java.util.List;

public class WiserConstant {

//    public static String APPLY_VIDEO = "https://wise-match.oss-cn-hangzhou.aliyuncs.com/user/resume/2394cd1ed4da20b5752ca094af6cae3c/265d2fcd2eac41c8adf59f0ded6c1a6f.png";
//    public static String VX_EXCHANGE = "https://wise-match.oss-cn-hangzhou.aliyuncs.com/user/resume/2394cd1ed4da20b5752ca094af6cae3c/a846454c0c114324ab68975fa32e82ab.png";
//    public static String PHONE_EXCHANGE = "https://wise-match.oss-cn-hangzhou.aliyuncs.com/user/resume/2394cd1ed4da20b5752ca094af6cae3c/382a8f9de9094e9f8e702b5541156e07.png";

    public static Integer PRIVATE_PROTECT = 1;
    public static Integer PRIVATE_NOT_PROTECT = 0;

    public static Integer OK = 1;
    public static Integer SUCCESS = 0;

    public static Integer ERROR = -1;

    public static String OK_STRING = "1";
    public static String SUCCESS_STRING = "0";

    public static String ERROR_STRING = "-1";

    public static Integer NOT_VERIFY = 0;
    public static Integer VERIFIED = 1;


    public static Integer NOT_DISPLAY_HOME = 0;
    public static Integer DISPLAY_HOME = 1;

    public static Integer VERIFY_REJECT = 2;

    //系统中的用户
    public static String USER = "USER";
    public static String COMPANY = "COMPANY";
    public static String SYSTEM = "SYSTEM";

    //来源业务类型
    public static String STORED = "STORED";
    public static String REPORT = "REPORT";
    public static String PORTRAIT = "PORTRAIT";
    public static String DEFAULT = "DEFAULT";

    //消息类型
    public static String JOIN_CANDIDATE = "JOIN_CANDIDATE";
    public static String JOIN_TALENT_POOL = "JOIN_TALENT_POOL";
    public static String REPORT_GENERATE = "REPORT_GENERATE";
    public static String SYSTEM_MESSAGES = "SYSTEM_MESSAGES";

    public static Integer DELETED = 1;

    public static Integer NOT_DELETE = 0;
    public static Integer NOT_CERTIFY = 0;
    public static Integer CERTIFIED = 1;//
    public static Integer CERTIFY_FAIL = 2;

    public static Integer NOT_CERTIFICATED = 0;
    public static Integer CERTIFICATED_SUCCESS = 1;
    public static Integer CERTIFICATED_FAIL = 2;

    public static Integer IN_USE = 1;
    public static Integer DISABLE = 0;

    public static Integer OPEN = 1;
    public static Integer NOT_OPEN = 0;

    //岗位和面试的三个状态
    public static Integer UNPUBLISHED = 0;//未发布
    public static Integer PUBLISHED = 1;//已发布
    public static Integer DEACTIVATE = 2;//停用

    public static Integer COLLECT = 1;//收藏
    public static Integer NOT_COLLECT = 0;

    public static String CHAT_ROOM_ID_PRE = "Chat_";

    public static String TEMP_FOLDER = "C:\\Windows\\Temp\\";

    public static Integer INCLUDE_RESUME = 1;
    public static Integer NOT_INCLUDE_RESUME = 0;

    public static Integer CHAT_MAX_MEMORY = 5;

    public static Integer ANALYSIS = 0;
    public static Integer NOT_ANALYSIS = 1;

    public static Integer RESUME_ANALYSIS_BATCH_SIZE = 2;


    public static List<Integer> RESUME_ANALYSIS_STATUS = Arrays.asList(/*UserResumeType.ANALYZE_FAIL.name() ,*/
            UserResumeType.NOT_ANALYSIS, UserResumeType.ANALYZE_FAIL);
}
