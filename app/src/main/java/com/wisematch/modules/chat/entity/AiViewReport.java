package com.wisematch.modules.chat.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version AiChatInterview.java, v0.1 2025-06-20 20:12
 */
@Data
@TableName("ai_view_report")
@Schema(description = "AI面试报告")
public class AiViewReport implements Serializable {

    public static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    public String id;
    
    @Schema(description = "面试ID")
    public String roomId;

    @Schema(description = "用户ID")
    public String userId;

    @Schema(description = "报告名称")
    public String name;

    @Schema(description = "所在城市")
    public String city;

    @Schema(description = "面试视频")
    public String chatUrl;

    @Schema(description = "面试时长")
    public Long duration;

    @Schema(description = "报告状态")
    public Integer status;

    @Schema(description = "面试消息")
    public String messages;

    @Schema(description = "面试评分")
    public Integer score;

    @Schema(description = "面试报告")
    public String report;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    public Date createTime;

}
