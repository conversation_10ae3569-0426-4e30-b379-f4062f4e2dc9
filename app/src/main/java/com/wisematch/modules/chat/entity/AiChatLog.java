package com.wisematch.modules.chat.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ai_chat_log")
@Schema(description = "面试日志记录")
public class AiChatLog {

    @ExcelProperty("ID")
    @TableId(value = "id", type = IdType.AUTO)
    public Long id;

    @Schema(description = "对话id")
    public String conversationId;

    @Schema(description = "智能体id")
    public String agentId;

    @Schema(description = "智能体名称")
    public String agentName;

    @Schema(description = "当前问题")
    public String question;

    @Schema(description = "意图识别")
    public String intention;

    @Schema(description = "意图识别码")
    public String intentionCode;

    @Schema(description = "意图识别名")
    public String intentionName;

    @Schema(description = "追问次数")
    public Integer followCount;

    @Schema(description = "提示词")
    public String prompt;

    @Schema(description = "系统回答")
    public String sysMsg;

    @Schema(description = "用户回答")
    public String userMsg;

    @Schema(description = "回答耗时")
    public Long cost;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date timestamp;

}
