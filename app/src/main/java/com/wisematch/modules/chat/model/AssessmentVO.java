package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class AssessmentVO {


    @Schema(description = "维度")
    private String dimension;

    @Schema(description = "权重")
    private Double weight;
    @Schema(description = "详情")
    private String detail;
    @Schema(description = "评估方法")
    private List<ScoreMethodVO> score_method;


}
