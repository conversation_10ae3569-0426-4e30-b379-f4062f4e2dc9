package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiViewRecord;
import com.wisematch.modules.chat.entity.AiViewReport;
import com.wisematch.modules.chat.model.ReportVO;

import java.util.List;


/**
 * AI面试服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */
public interface AiViewReportService extends IService<AiViewReport> {

    ReportVO report(AiViewRecord chatRoomId);

    AiViewReport getByRoomId(String roomId);

    List<ReportVO> reportList(String userId);

    List<String> getColumnList(String column);

    List<String> getCityList();

    List<String> getPositionList();

    boolean reportSchedule(AiViewRecord aiViewRecord, Integer currentRunCount, Integer maxRunCount);

}
