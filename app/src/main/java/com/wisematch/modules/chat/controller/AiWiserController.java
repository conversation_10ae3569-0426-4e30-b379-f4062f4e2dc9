package com.wisematch.modules.chat.controller;

import com.wisematch.common.annotation.SysLog;
import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.AiChatWiserService;
import com.wisematch.modules.sys.annotation.Anonymous;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.Arrays;

/**
 * Wiser智能体服务
 * <AUTHOR>
 * @version AiWiserController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/wiser")
@Tag(name = "Wiser首页", description = "AI Wiser 首页聊天智能体")

public class AiWiserController {

    @Autowired
    private AiChatWiserService aiChatWiserService;

    @Operation(summary = "Wiser初始化")
    @GetMapping("/init")
    @SysLog("wiser初始化")
    public R init(ChatInitParams chatInitParams) {
        return R.ok().setData(aiChatWiserService.initChat(chatInitParams));
    }

    @Operation(summary = "Wiser首页")
    @Anonymous
    @GetMapping("/index")
    @SysLog("Wiser首页")
    public R index() {
        return R.ok().setData(aiChatWiserService.index(""));
    }

    @Operation(summary = "聊天流式消息")
    @Anonymous
    @PostMapping(path = "/stream",consumes = MediaType.ALL_VALUE, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatMessage> stream(@RequestBody AiChatUserMsg userMsg) throws Exception {
        return aiChatWiserService.stream(userMsg);
    }

    @Operation(summary = "停止流式输出")
    @PostMapping("/stop")
    public R stop(@RequestParam("msgId") String msgId) throws Exception {
        if (null == msgId) {
            throw new Exception("msgId不能为空");
        }
        return R.ok().setData(aiChatWiserService.stopStream(msgId));
    }

    @Operation(summary = "历史聊天记录")
    @GetMapping("/messages")
    public R messages(@RequestParam("chatId") String chatId) throws Exception {
        if (null == chatId) {
            throw new Exception("chatId不能为空");
        }
        return R.ok().setData(aiChatWiserService.messages(chatId));
    }

    @Operation(summary = "历史会话记录")
    @GetMapping("/history")
    @Deprecated
    public R history() {
        return R.ok().setData(aiChatWiserService.chatRecords(UserInfoUtils.getCurrentUserId()));
    }

    @Operation(summary = "历史会话记录（分页）")
    @GetMapping("/historyPage")
    public R historyPage(HistoryPageDTO historyPageDTO) {
        historyPageDTO.setUserId(UserInfoUtils.getCurrentUserId());
        return R.ok().setData(aiChatWiserService.historyPage(historyPageDTO));
    }

    @Operation(summary = "删除会话记录")
    @PostMapping("/deleteSession")
    @SysLog("删除会话记录")
    public R deleteHistory(@RequestBody ChatIdRequest chatId) {
        aiChatWiserService.deleteChatRecords(chatId.getChatId());
        return R.ok("删除成功");
    }

    /**
     * 删除
     */
    @PostMapping(value = "/delete")
    @NotDoubleSubmit
    public R delete(@RequestBody String[] ids) {
        aiChatWiserService.batchLogicDelete(Arrays.asList(ids));
        return R.ok();
    }

}
