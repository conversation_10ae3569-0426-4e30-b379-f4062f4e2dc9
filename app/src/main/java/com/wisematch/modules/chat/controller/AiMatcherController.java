package com.wisematch.modules.chat.controller;

import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.modules.chat.model.AiChatUserMsg;
import com.wisematch.modules.chat.model.ChatInitParams;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.model.HistoryPageDTO;
import com.wisematch.modules.chat.service.AiChatMatcherService;
import com.wisematch.modules.sys.annotation.Anonymous;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @version AiMatcherController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/matcher")
@Tag(name = "Matcher首页", description = "AI Matcher 智能体")
public class AiMatcherController {
    @Autowired
    private AiChatMatcherService matcherService;

    @Operation(summary = "Matcher首页")
    @GetMapping("/index")
    @Anonymous
    public R index() {
        return R.ok().setData(matcherService.index());
    }


    @Operation(summary = "初始化")
    @GetMapping("/init")
    public R init(ChatInitParams chatInitParams) {
        return R.ok().setData(matcherService.init(chatInitParams));
    }

    @Operation(summary = "聊天历史记录")
    @GetMapping("/messages")
    public R messages(String chatId) {
        return R.ok().setData(matcherService.messages(chatId));
    }


    @Operation(summary = "聊天流式消息")
    @Anonymous
    @PostMapping(path = "/stream",consumes = MediaType.ALL_VALUE, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatMessage> stream(@RequestBody AiChatUserMsg userMsg) throws Exception {
        return matcherService.stream(userMsg);
    }

    @Operation(summary = "停止流式输出")
    @PostMapping("/stop")
    public R stop(@RequestParam("msgId") String msgId) throws Exception {
        if (null == msgId) {
            throw new Exception("msgId不能为空");
        }
        return R.ok().setData(matcherService.stopStream(msgId));
    }

    @Operation(summary = "历史会话记录")
    @GetMapping("/history")
    public R history() {
        return R.ok().setData(matcherService.matcherList());
    }


    @Operation(summary = "历史会话记录（分页）")
    @GetMapping("/historyPage")
    @NotDoubleSubmit
    public R historyPage(HistoryPageDTO historyPageDTO) {
        return R.ok().setData(matcherService.historyPage(historyPageDTO));
    }



}
