package com.wisematch.modules.chat.service;

import com.wisematch.common.utils.AgentsResult;
import com.wisematch.modules.chat.model.AgentsReportReq;
import com.wisematch.modules.chat.model.AgentsReportVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PostExchange;

public interface AgentsReportService {

    @PostExchange(value = "/analyze")
    AgentsResult<AgentsReportVO> getReport(@RequestBody AgentsReportReq req);
}
