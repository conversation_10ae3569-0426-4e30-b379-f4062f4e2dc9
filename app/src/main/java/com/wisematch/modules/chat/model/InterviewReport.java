package com.wisematch.modules.chat.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 面试报告
 * <AUTHOR>
 * @version InterviewReport.java, v0.1 2025-06-22 14:53
 */
@Data
public class InterviewReport {

    String userId;

    String roomId;

    String position;

    String resume;

    String chatType;

    String chatName;

    String positionId;

    String chatReport;

    String trainId;

    String interviewId;

    /**
     * 面试岗位
     */
    String chatId;

    /**
     * 面试视频
     */
    String videoUrl;

    /**
     * 开始时间
     */
    Date startTime;

    /**
     * 面试时长
     */
    Integer duration;

    /**
     * 面试评分
     */
    Integer score;

    /**
     * 面试问答
     */
    List<ChatInterviewViewMessage> messages;

    /**
     * 针对messages的重点划线文本列表
     */
    List<String> underlines;

    /**
     * 维度标签
     */
    List<String> labels;

    /**
     * 考核维度
     */
    List<CommentMsg> dimensions;

    List<LabelStatsVO> skillDirectVOS;

}
