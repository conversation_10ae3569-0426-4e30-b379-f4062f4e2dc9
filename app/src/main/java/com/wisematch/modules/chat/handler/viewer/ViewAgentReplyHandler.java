package com.wisematch.modules.chat.handler.viewer;

import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatOptions;
import com.alibaba.fastjson.JSONObject;
import com.wisematch.common.cache.GuavaCache;
import com.wisematch.modules.chat.entity.AiJobExamine;
import com.wisematch.modules.chat.entity.AiJobPosition;
import com.wisematch.modules.chat.entity.AiJobTrain;
import com.wisematch.modules.chat.enums.ApplyType;
import com.wisematch.modules.chat.executor.ChatViewLogExecutor;
import com.wisematch.modules.chat.model.ChatReplyMsg;
import com.wisematch.modules.chat.service.*;
import com.wisematch.modules.chat.enums.ViewerAgentConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * 记忆存储
 * <AUTHOR>
 * @version IAgentHandler.java, v0.1 2025-07-15 16:42
 */
@Component
@Slf4j
public class ViewAgentReplyHandler implements IViewAgentHandler {

    IViewAgentHandler iViewAgentHandler;
    @Autowired
    AiJobPositionService aiJobPositionService;
    @Autowired
    AiJobExamineService aiJobExamineService;
    @Autowired
    AiJobTrainService aiJobTrainService;
    @Autowired
    AiAgentPoolService aiAgentPoolService;
    @Autowired
    AiViewRecordService aiViewRecordService;

    @Autowired
    GuavaCache guavaCache;
    @Autowired
    private AiChatMemoryService aiChatMemoryService;
    @Autowired
    ChatViewLogExecutor chatViewLogExecutor;

    @Override
    public void setNext(IViewAgentHandler iAgentHandler) {
        this.iViewAgentHandler = iAgentHandler;
    }

    private final ChatClient chatClient;

    public ViewAgentReplyHandler(ChatClient.Builder builder) {
        this.chatClient = builder.build();
    }

    @Override
    public Flux<ChatReplyMsg> handle(ViewHandlerContext context) {
        long start = System.currentTimeMillis();
        context.setStartTime(start);
//        //默认面试agent
//        if (context.getIntention() == null || StringUtils.isEmpty(context.getIntention().getAgentId())) {
//            AiAgentPool aiAgentPool = this.aiAgentPoolService.getById(ViewerAgentConstant.INTERVIEW_AGENT_ID);
//            context.getAiViewRecord().setAgentId(aiAgentPool.getId());
//            context.setAiAgentPool(aiAgentPool);
//        } else {
//            context.getAiViewRecord().setAgentId(context.getIntention().getAgentId());
//            AiAgentPool aiAgentPool = this.aiAgentPoolService.getById(context.getIntention().getAgentId());
//            context.setAiAgentPool(aiAgentPool);
//        }

        //追问+1
        if (ViewerAgentConstant.FlOW_UP_SET.contains(context.getAiAgentPool().getAgentCode())) {
            context.getAiViewRecord().setFollowTime(context.getAiViewRecord().getFollowTime() + 1);
        }

        log.info("interview reply, chatId: {}, agentCode: {}, followCount: {}", context.getRoomId(), context.getAiAgentPool().getAgentCode(), context.getAiViewRecord().getFollowTime());

        String prompt = this.initPrompt(context);
        context.setPrompt(prompt);
        DashScopeChatOptions customOptions = DashScopeChatOptions.builder()
                .withTemperature(context.getAiAgentPool().getTemperature())
                .withModel(context.getAiAgentPool().getModel())
                .withEnableThinking(false)
                .build();

//        return chatClient.prompt(new Prompt(prompt, customOptions))
//                .user(context.getUserMsg())
//                .stream()
//                .content().map(x -> reply(context.getReplyId(),x));
        String reply = chatClient.prompt(new Prompt(prompt, customOptions))
                .messages(context.getChatViewMsg().getMessages())
                .user(context.getUserMsg()).call().content();
        //增加记忆
        aiChatMemoryService.addAssistantAsync(context.getRoomId(),reply,context.getAiViewRecord().getAskIndex());

        context.setLastedAgentMsg(reply);
        //意图识别完记一次log
        chatViewLogExecutor.executeAsync(context);

        log.info("agent reply cost: {} ms",(System.currentTimeMillis()-start));
        return Flux.just(reply(context.getReplyId(), reply));
    }

    /**
     * 提示词初始化，包含记忆、槽位替换
     * @param context
     * @return
     */
    private String initPrompt(ViewHandlerContext context){
        Long start = System.currentTimeMillis();
        log.info("init prompt,roomId:{}", context.getRoomId());
        String prompt = context.getAiAgentPool().getPrompt();
        AiJobExamine aiJobExamine = null;
        if (ApplyType.POSITION.name().equals(context.getAiViewRecord().getChatType())) {
            AiJobPosition jobPosition = this.aiJobPositionService.getById(context.getAiViewRecord().getPositionId());
            if (jobPosition != null) {
                prompt = prompt.replace("$jd", jobPosition.getPosition());
                aiJobExamine = this.aiJobExamineService.getById(jobPosition.getJobExamineId());
            }
        } else {
            AiJobTrain aiJobTrain = this.aiJobTrainService.getById(context.getAiViewRecord().getTrainId());
            if (aiJobTrain != null) {
                prompt = prompt.replace("$jd", aiJobTrain.getPosition());
                aiJobExamine = this.aiJobExamineService.getById(aiJobTrain.getJobExamineId());
            }
        }
        if (context.getAiViewPrepare() != null) {
            prompt = prompt.replace("$resume", context.getAiViewPrepare().getResume());
        } else {
            prompt = prompt.replace("$resume", "暂无");
        }
        if (aiJobExamine != null) {
            prompt = prompt.replace("$examine", aiJobExamine.getContent());
        }
        if (context.getIntention() != null && StringUtils.isNotBlank(context.getIntention().getCode())) {
            prompt = prompt.replace("$code", context.getIntention().getCode());
        }
//        prompt = prompt.replaceAll("$history", JSONObject.toJSONString(context.getChatViewMsg().getMessages()));
        prompt = prompt.replace("$question", JSONObject.toJSONString(context.getQuestion()));

        log.info("init prompt cost:{} ms", (System.currentTimeMillis() - start));
        return prompt;
    }

    private ChatReplyMsg reply(String replyId, String msg){
        ChatReplyMsg replyMsg = new ChatReplyMsg();
        replyMsg.setId(replyId);
        ChatReplyMsg.Choices choices = new ChatReplyMsg.Choices();
        choices.getDelta().put("content", msg);
        replyMsg.setChoices(List.of(choices));
        return replyMsg;
    }
}
