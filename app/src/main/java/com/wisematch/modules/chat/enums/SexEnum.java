package com.wisematch.modules.chat.enums;

import lombok.Getter;

public enum SexEnum {

    UNKNOWN(null, 0),  // dto中的未知对应null和0
    MALE(1, 1),        // 男：resumeDTO是1，dto是1
    FEMALE(0, 2);      // 女：resumeDTO是0，dto是2

    private final Integer resumeValue;  // resumeDTO中的值
    @Getter
    private final Integer dtoValue;     // dto中的值

    SexEnum(Integer resumeValue, Integer dtoValue) {
        this.resumeValue = resumeValue;
        this.dtoValue = dtoValue;
    }

    // 根据resumeDTO的值获取对应的枚举
    public static SexEnum getByResumeValue(Integer resumeValue) {
        for (SexEnum sex : values()) {
            if (sex.resumeValue != null && sex.resumeValue.equals(resumeValue)) {
                return sex;
            }
        }
        return UNKNOWN;  // 默认未知
    }


    // 根据resumeDTO的值获取对应的枚举
    public static SexEnum getByDtoValue(Integer dtoValue) {
        for (SexEnum sex : values()) {
            if (sex.dtoValue != null && sex.dtoValue.equals(dtoValue)) {
                return sex;
            }
        }
        return UNKNOWN;  // 默认未知
    }

}
