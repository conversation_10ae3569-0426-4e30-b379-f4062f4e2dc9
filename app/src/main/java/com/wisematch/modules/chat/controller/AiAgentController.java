package com.wisematch.modules.chat.controller;

import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.modules.chat.service.AiAgentVoiceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version AiChatController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/agent")
@Tag(name = "智能体声音", description = "AI面试官")
public class AiAgentController {

    @Autowired
    private AiAgentVoiceService aiAgentVoiceService;

    @Operation(summary = "智能体声音列表")
    @GetMapping("/voice/list")
    @NotDoubleSubmit
    public R getAudioList() {
        return R.ok().setData(aiAgentVoiceService.getList());
    }

}
