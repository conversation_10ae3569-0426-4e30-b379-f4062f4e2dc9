package com.wisematch.modules.chat.model;

import com.wisematch.modules.chat.entity.AiAgentVoice;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 火山引擎音色声音列表
 * <AUTHOR>
 * @version AiAgentVoice.java, v0.1 2025-06-20 20:12
 */
@Data
public class AiAgentVoiceVO {

    @Schema(description = "名称")
     public String name;

     public String code;

     public String url;

     public static AiAgentVoiceVO toVO(AiAgentVoice agentVoice){
         if (agentVoice != null) {
             AiAgentVoiceVO agentVoiceVO = new AiAgentVoiceVO();
             agentVoiceVO.setCode(agentVoice.getVoiceCode());
             agentVoiceVO.setUrl(agentVoice.getVoiceUrl());
             agentVoiceVO.setName(agentVoice.getVoiceName());
             return agentVoiceVO;
         }
         return null;
     }

    public static List<AiAgentVoiceVO> toVOs(List<AiAgentVoice> agentVoices) {
        List<AiAgentVoiceVO> voList = new ArrayList<>();
        for (int i = 0; i < agentVoices.size(); i++) {
            voList.add(AiAgentVoiceVO.toVO(agentVoices.get(i)));
        }
        return voList;
    }
}
