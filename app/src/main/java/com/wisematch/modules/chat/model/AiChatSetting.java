package com.wisematch.modules.chat.model;

import com.wisematch.modules.chat.config.AiChatConfig;
import com.wisematch.modules.chat.entity.AiViewRecord;
import lombok.Data;

/**
 * AI面试Agent配置参数
 * <AUTHOR>
 * @version AiChatSetting.java, v0.1 2025-06-20 14:35
 */
@Data
public class AiChatSetting {

    private String userId;

    private String appId;

    private String appKey;

    private String token;

    private String roomId;

    private String position;

    private String viewerName;

    private String viewerPhoto;

    private String speakPhoto;

    private String listenPhoto;

    private Integer width = 1920;

    private Integer height = 1080;


    public AiChatSetting(AiChatConfig aiChatConfig) {
        this.appId = aiChatConfig.getAppId();
        this.appKey = aiChatConfig.getAppKey();
//        this.asrAppId = aiChatConfig.getAsrAppId();
//        this.asrToken = aiChatConfig.getAsrToken();
//        this.ttsAppId = aiChatConfig.getTtsAppId();
//        this.ttsToken = aiChatConfig.getTtsToken();
    }

    public AiChatSetting(AiViewRecord aiViewRecord) {
        this.userId = aiViewRecord.getUserId();
        this.roomId = aiViewRecord.getRoomId();
        this.token = aiViewRecord.getRoomToken();
        this.viewerName = aiViewRecord.getViewerName();
        this.viewerPhoto = aiViewRecord.getViewerPhoto();
    }
}
