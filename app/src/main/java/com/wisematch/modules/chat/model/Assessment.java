package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public
class Assessment{

    @Schema(description = "岗位描述")
    String jd;
    @Schema(description = "评估维度")
    String dimension;
    @Schema(description = "详情")
    String detail;
    @Schema(description = "维度")
    Double weight;
    @Schema(description = "评估方法")
    List<SoreMethod> score_method;
}
