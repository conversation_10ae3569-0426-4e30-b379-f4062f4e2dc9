package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 问题
 * <AUTHOR>
 * @version QuestionsVO.java, v0.1 2025-07-15 20:16
 */
@Data
public class QuestionVO {

    @Schema(description = "问题")
    String question;

    @Schema(description = "审核点")
    String checkpoint;

    @Schema(description = "权重")
    Integer weight;

    public QuestionVO(String question){
        this.question = question;
    }
}
