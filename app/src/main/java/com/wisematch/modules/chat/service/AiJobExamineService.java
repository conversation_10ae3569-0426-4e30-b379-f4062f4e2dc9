package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiViewRecord;
import com.wisematch.modules.chat.entity.AiJobExamine;
import com.wisematch.modules.chat.model.AiJobExamineDTO;
import com.wisematch.modules.chat.model.AiJobExamineQueryDTO;

import java.util.List;


/**
 * AI智能体服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */
public interface AiJobExamineService extends IService<AiJobExamine> {

    void deleteOne(String id);

    void batchLogicDelete(List<String> ids);

    Page<AiJobExamine> pageQuery(AiJobExamineDTO aiJobExamineDTO);

    Page<AiJobExamine> selectAll(AiJobExamineDTO aiJobExamineDTO);

    AiJobExamine getExamByInterview(AiViewRecord aiViewRecord);

    List<AiJobExamine> listAll(AiJobExamineQueryDTO queryDTO);
}
