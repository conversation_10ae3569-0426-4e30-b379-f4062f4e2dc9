package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiMatcherSuggest;
import com.wisematch.modules.chat.model.AiMatcherSuggestQueryDTO;

import java.util.List;

public interface AiMatcherSuggestService extends IService<AiMatcherSuggest> {
    Page<AiMatcherSuggest> pageQuery(AiMatcherSuggestQueryDTO aiMatcherSuggestQueryDTO);

    void batchLogicDelete(List<String> list);
}

