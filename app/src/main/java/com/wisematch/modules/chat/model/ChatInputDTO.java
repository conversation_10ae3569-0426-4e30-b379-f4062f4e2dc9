package com.wisematch.modules.chat.model;

import com.wisematch.modules.chat.entity.AiJobPosition;
import com.wisematch.modules.chat.entity.AiJobTrain;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ChatInputDTO {

    @Schema(description = "消息")
    private String messages;

    @Schema(description = "工作列表")
    private List<AiJobPosition> aiJobPositionList;

    @Schema(description = "面试列表")
    private List<AiJobTrain> aiJobTrains;

}
