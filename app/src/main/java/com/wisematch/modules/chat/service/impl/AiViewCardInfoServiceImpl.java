package com.wisematch.modules.chat.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.format.FastDateFormat;
import com.alibaba.fastjson.JSONObject;
import com.wisematch.modules.chat.entity.AiJobPosition;
import com.wisematch.modules.chat.entity.AiJobTrain;
import com.wisematch.modules.chat.entity.AiViewReport;
import com.wisematch.modules.chat.enums.ApplyType;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.chat.service.AiJobPositionService;
import com.wisematch.modules.chat.service.AiJobTrainService;
import com.wisematch.modules.chat.service.AiViewRecordService;
import com.wisematch.modules.chat.service.AiViewReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 系统用户
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiViewCardInfoServiceImpl  {

    @Autowired
    private AiViewRecordService aiViewRecordService;
    @Autowired
    private AiJobPositionService aiJobPositionService;
    @Autowired
    private AiJobTrainService aiJobTrainService;
    @Autowired
    private AiViewReportService aiViewReportService;

    public List<CardInfo> getViewRecord(String userId, String type) {
        return this.aiViewRecordService.getViewRecord(userId, type).stream().map(x -> {
            CardInfo cardInfo = new CardInfo();
            cardInfo.setTitle(x.getChatName());
            cardInfo.setCardType(x.getChatType());
            cardInfo.setId(x.getRoomId());
            JSONObject extra = new JSONObject();
            extra.put("positionId", x.getPositionId());
            extra.put("trainId", x.getTrainId());
            cardInfo.setExtra(extra);
            if (ApplyType.POSITION.name().equals(cardInfo.getCardType())) {
                AiJobPosition aiJobPosition = this.aiJobPositionService.getById(x.getPositionId());
                if (aiJobPosition != null) {
                    cardInfo.setTitle(aiJobPosition.getPosition());
                    cardInfo.setSummary(aiJobPosition.getSummary());
                }
            } else {
                AiJobTrain aiJobTrain = this.aiJobTrainService.getById(x.getTrainId());
                if (aiJobTrain != null) {
                    cardInfo.setTitle(aiJobTrain.getPosition());
                    cardInfo.setSummary(aiJobTrain.getSummary());
                }
            }
            cardInfo.setKeyLabel(this.format(x.getDuration()));
            cardInfo.setLabels(DateUtil.format(x.getCreateTime(), FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss")));
            AiViewReport aiViewReport = this.aiViewReportService.getByRoomId(x.getRoomId());
            if (x.getStartTime() != null && x.getEndTime() != null) {
                x.setDuration((x.getStartTime().getTime() - x.getEndTime().getTime()) / 1000);
            }
//            if (x.getDuration() < 300) {
//                cardInfo.setStatus("-1");
//            } else {
                cardInfo.setStatus(aiViewReport == null ? "0" : aiViewReport.getStatus().toString());
//                cardInfo.setStatus(Objects.requireNonNull(ReportStatus.getStatus(aiViewReport.getStatus())).getDesc());
//            }
            return cardInfo;
        }).toList();
    }

    private String format(Long duration) {
        if (duration == null) {
            return "";
        }
        if (duration < 60) {
            return duration + "秒";
        } else if (duration % 60 == 0) {
            return duration / 60 + "分";
        } else {
            return duration / 60 + "分" + duration % 60 + "秒";
        }
    }

}
