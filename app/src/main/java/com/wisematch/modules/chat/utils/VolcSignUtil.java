package com.wisematch.modules.chat.utils;

import okhttp3.*;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URL;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version Sign.java, v0.1 2025-06-25 15:04
 */
public class VolcSignUtil {

    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .build();
    private static final BitSet URLENCODER = new BitSet(256);

    private static final String CONST_ENCODE = "0123456789ABCDEF";
    public static final Charset UTF_8 = StandardCharsets.UTF_8;

    private final String region;
    private final String service;
    private final String schema;
    private final String host;
    private final String path;
    private final String ak;
    private final String sk;

    static {
        int i;
        for (i = 97; i <= 122; ++i) {
            URLENCODER.set(i);
        }

        for (i = 65; i <= 90; ++i) {
            URLENCODER.set(i);
        }

        for (i = 48; i <= 57; ++i) {
            URLENCODER.set(i);
        }
        URLENCODER.set('-');
        URLENCODER.set('_');
        URLENCODER.set('.');
        URLENCODER.set('~');
    }

    public VolcSignUtil(String region, String service, String schema, String host, String path, String ak, String sk) {
        this.region = region;
        this.service = service;
        this.host = host;
        this.schema = schema;
        this.path = path;
        this.ak = ak;
        this.sk = sk;
    }

    public static void main(String[] args) throws Exception {
        String action = "StartVoiceChat";
        String version = "2024-12-01";
        String ak = "AKLTNGUwMzYyODI0ZGQ3NDYzYmEzYmRjMzI5NGYwZmI1OTM";
        String sk = "WkRnNE4yVXlaalUzT1dFMU5ESXhabUk1TXpNME1XUmxaVEk1WkROaE9USQ==";
        String region = "cn-north-1";
        String service = "rtc";
        // 请求地址
        String endpoint = "rtc.volcengineapi.com";
        String path = "/"; // 路径，不包含 Query// 请求接口信息

//        String region = "cn-beijing";
        String schema = "https";
        VolcSignUtil sign = new VolcSignUtil(region, service, schema, endpoint, path, ak, sk);

        String body = "{\"AppId\":\"683927008ac8a9018c2f0a34\",\"RoomId\":\"ChatRoom01\",\"TaskId\":\"ChatTask02\",\"Config\":{\"ASRConfig\":{\"Provider\":\"volcano\",\"ProviderParams\":{\"Mode\":\"bigmodel\",\"AppId\":\"**********\",\"AccessToken\":\"M_DO1jpElFo63yeP6roC0vc18_J6lYxq\",\"ApiResourceId\":\"volc.bigasr.sauc.duration\",\"StreamMode\":0}},\"TTSConfig\":{\"Provider\":\"volcano_bidirection\",\"ProviderParams\":{\"app\":{\"appid\":\"**********\",\"token\":\"M_DO1jpElFo63yeP6roC0vc18_J6lYxq\"},\"audio\":{\"voice_type\":\"zh_female_qingxinnvsheng_mars_bigtts\",\"speech_rate\":0,\"pitch_rate\":0}}},\"LLMConfig\":{\"VisionConfig\":{\"Enable\":false},\"Mode\":\"ArkV3\",\"EndPointId\":\"ep-20250604115214-lqnfs\",\"BotId\":\"ChatBot01\"}},\"AgentConfig\":{\"TargetUserId\":[\"Huoshan01\"],\"WelcomeMessage\":\"你好\",\"UserId\":\"HR\"}}";

        Date date = new Date();
        HashMap<String, String> queryMap = new HashMap<>() ;


        sign.doRequest("POST", queryMap, body.getBytes(), date, action, version);
    }

    public String doRequest(String method, Map<String, String> queryList, byte[] body,
                          Date date, String action, String version) throws Exception {
        if (body == null) {
            body = new byte[0];
        }
        String xContentSha256 = hashSHA256(body);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        String xDate = sdf.format(date);
        String shortXDate = xDate.substring(0, 8);
        String contentType = "application/json";

        String signHeader = "host;x-date;x-content-sha256;content-type";


        SortedMap<String, String> realQueryList = new TreeMap<>(queryList);
        realQueryList.put("Action", action);
        realQueryList.put("Version", version);
        StringBuilder querySB = new StringBuilder();
        for (String key : realQueryList.keySet()) {
            querySB.append(signStringEncoder(key)).append("=").append(signStringEncoder(realQueryList.get(key))).append("&");
        }
        querySB.deleteCharAt(querySB.length() - 1);

        String canonicalStringBuilder = method + "\n" + path + "\n" + querySB + "\n" +
                "host:" + host + "\n" +
                "x-date:" + xDate + "\n" +
                "x-content-sha256:" + xContentSha256 + "\n" +
                "content-type:" + contentType + "\n" +
                "\n" +
                signHeader + "\n" +
                xContentSha256;


        String hashcanonicalString = hashSHA256(canonicalStringBuilder.getBytes());
        String credentialScope = shortXDate + "/" + region + "/" + service + "/request";
        String signString = "HMAC-SHA256" + "\n" + xDate + "\n" + credentialScope + "\n" + hashcanonicalString;

        byte[] signKey = genSigningSecretKeyV4(sk, shortXDate, region, service);
        String signature = HexFormat.of().formatHex(hmacSHA256(signKey, signString));


        URL url = new URL(schema + "://" + host + path + "?" + querySB);
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("X-Date", xDate);
        headers.put("X-Content-Sha256", xContentSha256);
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "HMAC-SHA256" +
                " Credential=" + ak + "/" + credentialScope +
                ", SignedHeaders=" + signHeader +
                ", Signature=" + signature);

        // 3 构建Request对象
        Headers.Builder builder = new Headers.Builder();
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            builder.add(entry.getKey(), entry.getValue());
        }

        Request request = new Request.Builder()
                .url(url)
                .headers(builder.build())
                .post(RequestBody.create(MediaType.parse("application/json"),body))
                .build();

        // 4 发起请求获取响应值
        Response response = client.newCall(request).execute();

//        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
//        conn.setRequestMethod(method);
//        conn.setRequestProperty("Host", host);
//        conn.setRequestProperty("X-Date", xDate);
//        conn.setRequestProperty("X-Content-Sha256", xContentSha256);
//        conn.setRequestProperty("Content-Type", contentType);
//        conn.setRequestProperty("Authorization", "HMAC-SHA256" +
//                " Credential=" + ak + "/" + credentialScope +
//                ", SignedHeaders=" + signHeader +
//                ", Signature=" + signature);
//        if (!Objects.equals(conn.getRequestMethod(), "GET")) {
//            conn.setDoOutput(true);
//            OutputStream os = conn.getOutputStream();
//            os.write(body);
//            os.flush();
//            os.close();
//        }
//        conn.connect();
//
//        int responseCode = conn.getResponseCode();
//
//        InputStream is;
//        if (responseCode == 200) {
//            is = conn.getInputStream();
//        } else {
//            is = conn.getErrorStream();
//        }
//        String responseBody = new String(is.readAllBytes());
//        is.close();
        // 5 根据响应结果判断
        if (response.isSuccessful()) {
            return response.body().string();
        } else {
            throw new RuntimeException("请求异常,错误码为: " +  response.body().string());
        }
    }

    private String signStringEncoder(String source) {
        if (source == null) {
            return null;
        }
        StringBuilder buf = new StringBuilder(source.length());
        ByteBuffer bb = UTF_8.encode(source);
        while (bb.hasRemaining()) {
            int b = bb.get() & 255;
            if (URLENCODER.get(b)) {
                buf.append((char) b);
            } else if (b == 32) {
                buf.append("%20");
            } else {
                buf.append("%");
                char hex1 = CONST_ENCODE.charAt(b >> 4);
                char hex2 = CONST_ENCODE.charAt(b & 15);
                buf.append(hex1);
                buf.append(hex2);
            }
        }

        return buf.toString();
    }

    public static String hashSHA256(byte[] content) throws Exception {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");

            return HexFormat.of().formatHex(md.digest(content));
        } catch (Exception e) {
            throw new Exception(
                    "Unable to compute hash while signing request: "
                            + e.getMessage(), e);
        }
    }

    public static byte[] hmacSHA256(byte[] key, String content) throws Exception {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(key, "HmacSHA256"));
            return mac.doFinal(content.getBytes());
        } catch (Exception e) {
            throw new Exception(
                    "Unable to calculate a request signature: "
                            + e.getMessage(), e);
        }
    }

    private byte[] genSigningSecretKeyV4(String secretKey, String date, String region, String service) throws Exception {
        byte[] kDate = hmacSHA256((secretKey).getBytes(), date);
        byte[] kRegion = hmacSHA256(kDate, region);
        byte[] kService = hmacSHA256(kRegion, service);
        return hmacSHA256(kService, "request");
    }

}
