package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.entity.AiViewRank;
import com.wisematch.modules.chat.entity.AiViewRecord;
import com.wisematch.modules.chat.entity.AiViewReport;
import com.wisematch.modules.chat.mapper.AiViewRankMapper;
import com.wisematch.modules.chat.mapper.AiViewRecordMapper;
import com.wisematch.modules.chat.model.AiViewRankQueryDTO;
import com.wisematch.modules.chat.service.AiViewRankService;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.service.SysUserService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class AiViewRankServiceImpl extends ServiceImpl<AiViewRankMapper, AiViewRank> implements AiViewRankService {

    @Autowired
    private AiViewRankMapper mapper;

    @Resource
    private AiViewRecordMapper aiViewRecordMapper;

    public IPage<AiViewRank> getUserAllRanks(AiViewRankQueryDTO queryDTO) {

        Page<AiViewRank> page = new Page<>(queryDTO.getPage(), queryDTO.getSize());
        queryDTO.setUserId(UserInfoUtils.getCurrentUserId());
        LambdaQueryWrapper<AiViewRank> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiViewRank::getUserId, UserInfoUtils.getCurrentUserId());
        if(StringUtils.isNotBlank(queryDTO.getPosition())){
            queryWrapper.eq(AiViewRank::getPosition, queryDTO.getPosition());
        }
        if(StringUtils.isNotBlank(queryDTO.getCity())){
            queryWrapper.eq(AiViewRank::getCity, queryDTO.getCity());
        }
        return this.page(page, queryWrapper);
    }

    @Autowired
    SysUserService sysUserService;

    @Override
    public AiViewRank saveRank(AiViewReport aiViewReport) {
        String userId = aiViewReport.getUserId();
        SysUser sysUser = sysUserService.getById(userId);
        String name = sysUser.getUsername();

        String roomId = aiViewReport.getRoomId();
        AiViewRecord record = aiViewRecordMapper.selectOne(new QueryWrapper<AiViewRecord>().lambda().eq(AiViewRecord::getRoomId, roomId));
        AiViewRank aiViewRank = new AiViewRank();
        aiViewRank.setName(name);
        aiViewRank.setPosition(aiViewReport.getName());
        aiViewRank.setCity(aiViewReport.getCity());
        aiViewRank.setRoomId(roomId);
        aiViewRank.setScore(aiViewReport.getScore());
        aiViewRank.setUserId(userId);
        aiViewRank.setTrainId(record.getTrainId());
        this.save(aiViewRank);

        return aiViewRank;
    }
}
