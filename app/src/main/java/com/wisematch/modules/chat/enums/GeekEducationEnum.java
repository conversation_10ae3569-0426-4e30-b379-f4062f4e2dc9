package com.wisematch.modules.chat.enums;

import lombok.Getter;

@Getter
public enum GeekEducationEnum {

    DOCTOR("博士", 7),
    MASTER("硕士", 6),
    BACHELOR("本科", 5),
    COLLEGE("大专", 4),
    SENIOR_HIGH("高中", 3),
    TECHNICAL_SECONDARY("中专", 2),
    JUNIOR_HIGH("初中", 1),
    UNKNOWN("未知", 0);

    private final String name;
    private final int code;

    GeekEducationEnum(String name, int code) {
        this.name = name;
        this.code = code;
    }

    // 根据代码获取对应的枚举
    public static GeekEducationEnum getByCode(int code) {
        for (GeekEducationEnum education : values()) {
            if (education.code == code) {
                return education;
            }
        }
        return UNKNOWN;
    }

    // 根据描述获取对应的枚举
    public static GeekEducationEnum getByName(String name) {
        for (GeekEducationEnum education : values()) {
            if (education.name.equals(name)) {
                return education;
            }
        }
        return UNKNOWN;
    }
}
