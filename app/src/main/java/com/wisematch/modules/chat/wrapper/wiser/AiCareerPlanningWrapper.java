package com.wisematch.modules.chat.wrapper.wiser;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.wisematch.common.utils.BeanCopyUtils;
import com.wisematch.modules.chat.entity.AiJobTrain;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.service.AiJobPositionService;
import com.wisematch.modules.chat.service.AiJobTrainService;
import com.wisematch.modules.chat.service.AiUserResumeService;
import com.wisematch.modules.chat.wrapper.AbstractWiseMatchWrapper;
import com.wisematch.modules.common.utils.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 职业规划
 */
@Component
@Data
@Slf4j
public class AiCareerPlanningWrapper extends AbstractWiseMatchWrapper {

    @Autowired
    AiJobPositionService aiJobPositionService;
    @Autowired
    private AiJobTrainService aiJobTrainService;
    @Autowired
    private AiUserResumeService aiUserResumeService;

    @Override
    public String promptWrap(AgentHandlerContext context, String prompt) {
        if (prompt.contains("$jobDocuments")) {
            prompt = prompt.replace("$jobDocuments", JsonUtils.toJson(this.aiJobPositionService.getList().subList(0, 8)));
        }
        if (prompt.contains("$resume")) {
            prompt = prompt.replace("$resume", aiUserResumeService.getCurrentOpenedOrText(context.getAiChatUserMsg().getUserId()));
        }
        return super.promptWrap(context, prompt);
    }

    @Override
    public ChatMessage replyWrap(ChatMessage chatMessage) {
        if (!JSONUtil.isTypeJSON(chatMessage.getMsg())) {
            log.info("reply wrap:{}", chatMessage.getMsg());
            return super.replyWrap(chatMessage);
        }
        JSONObject replys = JSONObject.parseObject(chatMessage.getMsg());
        if (!chatMessage.getMsg().contains("recommendJobId")) {
            return super.replyWrap(chatMessage);
        }
        try {
            List<String> rcmdId = (List<String>) replys.get("recommendJobId");
            if (null != rcmdId && !rcmdId.isEmpty()) {
                List<AiJobTrain> aiJobTrains = this.aiJobTrainService.listByIds(rcmdId);
                List<CardInfo> cardInfos = BeanCopyUtils.copyList(aiJobTrains, CardInfo::jobTrainToCard);

                chatMessage.setCardInfos(cardInfos);
                return super.replyWrap(chatMessage);
            }
        } catch (Exception e) {
            log.error("职业规划异常", e);
        }
        //TODO 测试待删除
        chatMessage.setCardInfos(this.aiJobTrainService.getList().subList(0, 3));
        return super.replyWrap(chatMessage);
    }
}
