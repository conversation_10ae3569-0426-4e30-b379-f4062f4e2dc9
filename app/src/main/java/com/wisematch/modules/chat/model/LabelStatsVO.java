package com.wisematch.modules.chat.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LabelStatsVO {

    private String label;
    private int totalItems;       // 该label总条目数
    private long underlineCount;  // 带underline的条目数
    private double averageScore;  // 平均分

    public LabelStatsVO(int totalItems, long underlineCount, double averageScore) {
        this.totalItems = totalItems;
        this.underlineCount = underlineCount;
        this.averageScore = averageScore;
    }
}
