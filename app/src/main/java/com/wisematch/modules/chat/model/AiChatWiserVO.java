package com.wisematch.modules.chat.model;

import com.wisematch.modules.chat.entity.AiChatWiser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version AiChatMatcher.java, v0.1 2025-06-20 20:12
 */
@Data
public class AiChatWiserVO {

    @Schema(description = "用户id")
    public String chatId;

    @Schema(description = "用户id")
    public String userId;

    @Schema(description = "wiserId")
    public String wiserId;

    @Schema(description = "消息")
    public String message;

    @Schema(description = "建议输入")
    List<String> tips;

    public static AiChatWiserVO toVO(AiChatWiser aiChatWiser){
        if (aiChatWiser != null) {
            AiChatWiserVO aiChatWiserVO = new AiChatWiserVO();
            aiChatWiserVO.setChatId(aiChatWiser.getChatId());
            aiChatWiserVO.setWiserId(aiChatWiser.getWiserId());
            aiChatWiserVO.setUserId(aiChatWiser.getUserId());
            return aiChatWiserVO;
        }
        return null;
    }
}
