package com.wisematch.modules.chat.utils;

import com.wisematch.modules.chat.model.ShortMessageVO;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class MapToListUtils {
    public static List<List<ShortMessageVO>> convert(List<ShortMessageVO> shortMessageVOS) {
        // 处理空列表情况
        if (shortMessageVOS == null || shortMessageVOS.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用Stream进行分组、排序操作
        return shortMessageVOS.stream()
                // 过滤掉questionIndex为null的元素
                .filter(message -> message.getQuestionIndex() != null)
                // 按questionIndex分组
                .collect(Collectors.groupingBy(ShortMessageVO::getQuestionIndex))
                // 转换为entry集合流
                .entrySet().stream()
                // 按key（questionIndex）排序
                .sorted(Comparator.comparing(Map.Entry::getKey))
                // 提取value（分组后的列表）
                .map(Map.Entry::getValue)
                // 收集为最终的List
                .collect(Collectors.toList());
    }
}
