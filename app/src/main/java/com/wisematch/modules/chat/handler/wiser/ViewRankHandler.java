package com.wisematch.modules.chat.handler.wiser;

import com.wisematch.common.cache.RedisCache;
import com.wisematch.modules.chat.entity.AiViewRank;
import com.wisematch.modules.chat.entity.AiViewReport;
import com.wisematch.modules.chat.service.AiViewRankService;
import com.wisematch.modules.chat.service.RedisRankingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ViewRankHandler {


    @Autowired
    AiViewRankService aiViewRankService;
    @Autowired
    RedisCache redisCache;

    @Autowired
    RedisRankingService rankingService;


    public AiViewRank handleInsert(AiViewReport aiViewReport) {

        return aiViewRankService.saveRank(aiViewReport);
    }

}
