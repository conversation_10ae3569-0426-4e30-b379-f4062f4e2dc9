package com.wisematch.modules.chat.agent;

import com.alibaba.fastjson.JSONObject;
import com.wisematch.modules.chat.entity.AiAgentPool;
import com.wisematch.modules.chat.handler.viewer.ViewHandlerContext;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.messages.Message;

import java.util.List;

/**
 * 通用agent上下文
 * <AUTHOR>
 * @version AgentContext.java, v0.1 2025-07-15 17:23
 */
@Data
public class AgentContext {

    String userId;

    String replyId;

    String agentCode;

    AiAgentPool aiAgentPool;

    boolean enableThinking = false;

    String prompt;

    /**
     * 用户最后一次输入
     */
    String userMsg;

    /**
     * 历史消息
     */
    List<Message> history;


    public String initPrompt(String prompt, ViewHandlerContext context) {
        if(StringUtils.isEmpty(prompt)){
            return "";
        }
        prompt = prompt.replaceAll("\\$history", JSONObject.toJSONString(context.getChatViewMsg().getMessages())).replace("\\$userMsg", context.getUserMsg());
        prompt = prompt.replaceAll("\\$question", JSONObject.toJSONString(context.getQuestion()));
        return prompt;
    }
}
