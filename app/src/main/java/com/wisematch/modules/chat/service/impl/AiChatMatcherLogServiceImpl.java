package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.utils.ThreadPoolUtil;
import com.wisematch.modules.chat.entity.AiChatMatcherLog;
import com.wisematch.modules.chat.mapper.AiChatMatcherLogMapper;
import com.wisematch.modules.chat.service.AiChatMatcherLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * Matcher Log日志服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiChatMatcherLogServiceImpl extends ServiceImpl<AiChatMatcherLogMapper, AiChatMatcherLog> implements AiChatMatcherLogService {

    @Override
    public void supplyAsync(AiChatMatcherLog aiChatMatcherLog) {
        ThreadPoolUtil.supplyAsync(() -> this.baseMapper.insert(aiChatMatcherLog));
    }
}
