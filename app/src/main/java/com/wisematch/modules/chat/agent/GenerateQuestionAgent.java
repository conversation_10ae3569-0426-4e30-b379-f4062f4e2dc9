package com.wisematch.modules.chat.agent;

import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatOptions;
import com.alibaba.fastjson.JSONObject;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.modules.chat.entity.AiAgentPool;
import com.wisematch.modules.chat.model.GenerateQsAgentDTO;
import com.wisematch.modules.chat.service.AiAgentPoolService;
import com.wisematch.modules.chat.enums.ViewerAgentConstant;
import com.wisematch.modules.chat.utils.JsonExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 意图识别agent门面
 * <AUTHOR>
 * @version IntentionAgentFacade.java, v0.1 2025-07-11 11:02
 */
@Component
@Slf4j
@Deprecated
public class GenerateQuestionAgent {

    @Autowired
    private AiAgentPoolService aiAgentPoolService;
    private final ChatClient chatClient;

    public GenerateQuestionAgent(ChatClient.Builder builder) {
        this.chatClient = builder.build();
    }

    /**
     * 意图识别
     * @param generateQsAgentDTO
     * @return
     */
    public List<String> getQuestions(GenerateQsAgentDTO generateQsAgentDTO){
        long start = System.currentTimeMillis();
        AiAgentPool aiAgentPool = aiAgentPoolService.getById(ViewerAgentConstant.QUESTION);

        String prompt = aiAgentPool.getPrompt();
        prompt = prompt.replace("$resume",JSONObject.toJSONString(generateQsAgentDTO.getResume()));
        prompt = prompt.replace("$jd",generateQsAgentDTO.getJd());
        prompt = prompt.replace("$examine",generateQsAgentDTO.getExamine());

        DashScopeChatOptions customOptions = DashScopeChatOptions.builder()
                .withTemperature(aiAgentPool.getTemperature())
                .withModel(aiAgentPool.getModel())
                .build();

        String reply =  this.chatClient.prompt(new Prompt(prompt, customOptions))
                .call().content();
        List<String> qs = new ArrayList<>();
        try {
            if (reply.contains("{") && reply.contains("}")) {
                reply = JsonExtractor.extractJSON(reply);
                JSONObject replys = JSONObject.parseObject(reply);
                qs = (List<String>) replys.get("question");
            }
            log.info("questions generate:{}", qs);
        }catch (Exception e){
            log.error("意图识别异常,问题={}", e.getMessage());
            throw new RRException(RRExceptionEnum.AGENT_ERROR);
        }
        log.info("意图识别耗时:" + (System.currentTimeMillis() - start) + "ms");
        return qs;
    }


}
