package com.wisematch.modules.chat.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ai_chat_wiser_memory")
@Schema(description = "wiser聊天记录")
public class AiChatWiserMemory {

    @ExcelProperty("ID")
    @TableId(value = "id", type = IdType.AUTO)
    public Long id;

    @ExcelProperty("会话ID")
    @Schema(description = "对话id")
    public String conversationId;

    @ExcelProperty("聊天内容")
    @Schema(description = "聊天内容")
    public String content;

    @ExcelProperty("原始信息")
    @Schema(description = "原始信息")
    public String msg;

    @ExcelProperty("角色")
    @Schema(description = "角色")
    public String type;

    @Schema(description = "收藏状态，0:默认，1-收藏")
    private Integer collect;

    @Schema(description = "点赞点踩状态，-1:踩，1:赞，0:默认")
    private Integer thumbsUp;

    @Schema(description = "额外信息")
    private String extra;

    @ExcelProperty("创建时间")
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date timestamp;



}
