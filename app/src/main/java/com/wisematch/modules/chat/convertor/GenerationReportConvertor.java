package com.wisematch.modules.chat.convertor;

import com.wisematch.modules.chat.model.AgentsReportVO;
import com.wisematch.modules.chat.model.GenerationReport;
import com.wisematch.modules.chat.model.CommentMsg;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class GenerationReportConvertor {

    /**
     * 使用Stream API将AgentsReportVO转换为GenerationReport
     * @param report 源对象
     * @return 转换后的目标对象
     */
    public static GenerationReport convert(AgentsReportVO report) {
        // 处理源对象为null的情况
        GenerationReport targetReport = new GenerationReport();
        if (report == null) {
            targetReport.setDimensions(Collections.emptyList());
            return targetReport;
        }

        // 使用Stream进行列表转换
        List<CommentMsg> commentMsgs = report.getDimensions() == null
                ? Collections.emptyList()
                : report.getDimensions().stream()
                .map(GenerationReportConvertor::mapToCommentMsg)
                .collect(Collectors.toList());

        targetReport.setDimensions(commentMsgs);
        return targetReport;
    }

    /**
     * 将Dimension对象映射为CommentMsg对象
     * @param dimension 源维度对象
     * @return 转换后的评论消息对象
     */
    private static CommentMsg mapToCommentMsg(AgentsReportVO.Dimension dimension) {
        // 处理单个维度对象为null的情况
        if (dimension == null) {
            return new CommentMsg();
        }

        return new CommentMsg() {{
            setLabels(dimension.getLabels());
            setCheckPoint(dimension.getCheckPoint());
            setUnderline(dimension.getUnderline());
            setUnderlineDetail(dimension.getUnderlineDetail());
            setSummary(dimension.getSummary());
            setComments(dimension.getComments());
            setScore(dimension.getScore());
            setMsgIndex(null); // 源对象中无此属性，设为null
        }};
    }
}
