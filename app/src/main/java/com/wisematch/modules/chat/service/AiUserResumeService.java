package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiUserResume;
import com.wisematch.modules.chat.model.AiUserResumeDTO;
import com.wisematch.modules.chat.model.AiUserResumeUpdateDTO;
import com.wisematch.modules.chat.model.ChangeResumeStatusDTO;
import com.wisematch.modules.chat.model.UserResumeOnline;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface AiUserResumeService extends IService<AiUserResume> {

    void updateResume(AiUserResumeUpdateDTO aiUserResumeUpdateDTO);

    Page<AiUserResume> pageQuery(AiUserResumeDTO aiUserResume);
    List<AiUserResume> attachmentList(String userId);

    List<AiUserResume> online(String userId);

    UserResumeOnline onlineDetail(String id);

    boolean onlineSave(UserResumeOnline online);

    String getCurrentOpened(String userId);

    @Deprecated
    String getOwnLasted(String userId);

    AiUserResume uploadResume(MultipartFile file, String userId);

    AiUserResume uploadAndSaveResume(MultipartFile file, String userId);

    void batchLogicDelete(List<String> ids);

    List<String> getBatchIdByStatus(List<Integer> isAnalysis);


    void resumeExtractJsonAsync(String resumeId);

    void resumeUpdate(String resumeId, String docResume);


    void changeStatus(ChangeResumeStatusDTO dto);

    void examResume(String userId);

    String getCurrentOpenedOrText(String userId);
}
