package com.wisematch.modules.chat.model;

import com.wisematch.modules.chat.enums.ApplyPositionType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version ApplyStopVO.java, v0.1 2025-07-09 11:10
 */
@Data
public class ApplyStopVO {

    /**
     * 提示语
     */
    @Schema(description = "提示文案")
    String tips = "";

    /**
     * 是否允许提交表单
     */
    @Schema(description = "是否允许提交表单:true-跳转表单页/false-关闭返回")
    boolean allowSubmit = false;

    /**
     * 兼职PART/全职FULL
     */
    @Schema(description = "兼职PART/全职FULL")
    String formType = ApplyPositionType.FULL.name();
}
