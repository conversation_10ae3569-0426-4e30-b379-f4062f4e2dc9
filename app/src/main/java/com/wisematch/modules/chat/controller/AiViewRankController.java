package com.wisematch.modules.chat.controller;

import com.wisematch.common.utils.R;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.model.AiViewRankQueryDTO;
import com.wisematch.modules.chat.service.AiViewRankService;
import com.wisematch.modules.chat.service.RedisRankingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version AiChatController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/view/rank")
@Tag(name = "面试排名", description = "面试排名")
public class AiViewRankController {

    @Autowired
    AiViewRankService aiViewRankService;

    @Autowired
    RedisRankingService redisRankingService;

    @Operation(summary = "整体排行列表")
    @GetMapping("/list")
    public R list(AiViewRankQueryDTO queryDTO) {
        return R.ok().setData(redisRankingService.getTopN(UserInfoUtils.getCurrentUserId(), queryDTO.getChatId() ,queryDTO.getPage(), queryDTO.getSize(), queryDTO.getPosition()));
    }

    @Operation(summary = "用户所属排名")
    @GetMapping("/getUserRank")
    public R getUserRank(AiViewRankQueryDTO queryDTO) {
        return R.ok().setData(redisRankingService.getUserRank(UserInfoUtils.getCurrentUserId(), queryDTO.getChatId(), queryDTO.getPosition()));
    }

    @Operation(summary = "用户模拟面试列表")
    @GetMapping("/getUserRankList")
    public R getUserRankList(AiViewRankQueryDTO queryDTO) {
        return R.ok().setData(aiViewRankService.getUserAllRanks(queryDTO));
    }

}
