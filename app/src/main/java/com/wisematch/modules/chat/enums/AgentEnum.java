package com.wisematch.modules.chat.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;



public enum AgentEnum {

    // 枚举实例定义
    SMALL_TALK_MODEL(0, "SMALL_TALK_MODEL"),//wiser聊天框聊天
    JOB_RECOMMEND_MODEL(1, "JOB_RECOMMEND_MODEL"),//wiser聊天框推荐工作
    INTERVIEW_RECOMMEND_MODEL(2, "INTERVIEW_RECOMMEND_MODEL"),//wiser聊天框推荐面试
    NOT_INCLUDE_RESUME(3, "NOT_INCLUDE_RESUME"),//wiser聊天框请求上传简历
    AGENT_INTENT_DETECTION_MODEL(5, "AGENT_INTENT_DETECTION_MODEL"),//wiser聊天框请求上传简历
    REPORT(10, "REPORT"),//面试报告生成agent

    RESUME_ANALYSIS_MODEL(4, "RESUME_ANALYSIS_MODEL");//wiser简历解析mode
    private final Integer code;
    private final String model;

    AgentEnum(Integer code, String model) {
        this.code = code;
        this.model = model;
    }

    public Integer getCode() {
        return code;
    }

    public String getModel() {
        return model;
    }
    // 根据code获取枚举实例
    public static AgentEnum getByCode(Integer code) {
        return KEY_MAP.get(code);
    }

    // 预加载code-model映射
    private static final Map<Integer, AgentEnum> KEY_MAP =
            Arrays.stream(AgentEnum.values())
                    .collect(Collectors.toMap(
                            AgentEnum::getCode,
                            e -> e
                    ));

    // 可选：校验code是否存在
    public static boolean containsCode(Integer code) {
        return KEY_MAP.containsKey(code);
    }
}
