package com.wisematch.modules.chat.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.utils.GuavaCacheUtil;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.agent.GenerateQuestionAgent;
import com.wisematch.modules.chat.entity.*;
import com.wisematch.modules.chat.enums.ApplyType;
import com.wisematch.modules.chat.enums.InterviewStatus;
import com.wisematch.modules.chat.mapper.AiViewRecordMapper;
import com.wisematch.modules.chat.model.GenerateQsAgentDTO;
import com.wisematch.modules.chat.model.GenerateQuestionDTO;
import com.wisematch.modules.chat.model.InterviewInitParams;
import com.wisematch.modules.chat.model.PreApplyVO;
import com.wisematch.modules.chat.service.*;
import com.wisematch.modules.form.service.AiUserFormService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 系统用户
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiViewRecordServiceImpl extends ServiceImpl<AiViewRecordMapper, AiViewRecord> implements AiViewRecordService {

    @Autowired
    private AiAgentPoolService aiAgentPoolService;
    @Autowired
    private AiJobViewerService aiJobViewerService;
    @Autowired
    private AiJobPositionService aiJobPositionService;
    @Autowired
    private AiJobTrainService aiJobTrainService;
    @Autowired
    private AiUserResumeService aiUserResumeService;
    @Autowired
    private AiJobExamineService aiJobExamineService;
    @Autowired
    private GuavaCacheUtil guavaCache;
    @Autowired
    GenerateQuestionAgent generateQuestionAgent;
    @Autowired
    AiUserFormService aiUserFormService;

    @Override
    public Boolean applySubmit(JSONObject applyInfos) {
        String roomId = applyInfos.getString("roomId");
        AiViewRecord aiViewRecord = this.getByRoomId(roomId);
        aiViewRecord.setEndTime(new Date());
        aiViewRecord.setSubmitForm(applyInfos.toJSONString());
        aiViewRecord.setStatus(InterviewStatus.SUBMITED.name());
        this.updateById(aiViewRecord);

        aiUserFormService.insertFromInterview(aiViewRecord.getChatName(), applyInfos);
        return true;
    }


    /**
     * 初始化面试记录
     * @param initParams
     * @return
     */
    @Override
    public AiViewRecord initViewRecord(InterviewInitParams initParams){
        log.info("init interview.");
        AiViewRecord aiViewRecord = new AiViewRecord();
        aiViewRecord.setApplyName(initParams.getApplyName());
        aiViewRecord.setApplyPhone(initParams.getApplyPhone());
        aiViewRecord.setApplyEmail(initParams.getApplyEmail());
        aiViewRecord.setChatType(initParams.getApplyType());
        aiViewRecord.setChatName(initParams.getApplyPosition());

//        AiAgentPool aiAgentPool = this.aiAgentPoolService.getById(initParams.getAgentId());
//        if (aiAgentPool != null) {
//            aiViewRecord.setAgentName(aiAgentPool.getAgentName());
//            aiViewRecord.setAgentId(aiAgentPool.getId());
//        }
        aiViewRecord.setUserId(initParams.getUserId());
        aiViewRecord.setPositionId(initParams.getPositionId());
        aiViewRecord.setTrainId(initParams.getTrainId());
        aiViewRecord.setApplyInfos(initParams.getApplyInfos());
        aiViewRecord.setPlatform(initParams.getPlatform());

        //设置默认面试官
        AiJobViewer aiJobViewer = this.aiJobViewerService.getById(StringUtils.isEmpty(initParams.getViewId()) ? "1" : initParams.getViewId());
        if (aiJobViewer != null) {
            aiViewRecord.setViewerId(aiJobViewer.getId());
            aiViewRecord.setViewerName(aiJobViewer.getName());
            aiViewRecord.setViewerVoice(aiJobViewer.getVoice());
            aiViewRecord.setViewerPhoto(aiJobViewer.getPhoto());
            aiViewRecord.setWelcome(aiJobViewer.getWelcome());
        }
        if (!StringUtils.isEmpty(initParams.getWelcome())) {
            aiViewRecord.setWelcome(initParams.getWelcome());
        }
        aiViewRecord.setId(IdUtil.fastSimpleUUID());
        aiViewRecord.setStatus(InterviewStatus.INIT.name());
        aiViewRecord.setCreateTime(new Date());
        aiViewRecord.setUpdateTime(new Date());
        return aiViewRecord;
    }

    @Override
    public List<AiViewRecord> getChattingRoom() {
        QueryWrapper<AiViewRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiViewRecord::getStatus, InterviewStatus.CHATTING)
                .or(x -> x.eq(AiViewRecord::getStatus, InterviewStatus.CHATED))
                .orderByAsc(AiViewRecord::getCreateTime);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<AiViewRecord> getList() {
        QueryWrapper<AiViewRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByAsc(AiViewRecord::getCreateTime);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<String> getByUserId(String userId) {
        LambdaQueryWrapper<AiViewRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(AiViewRecord::getRoomId);
        queryWrapper.eq(AiViewRecord::getUserId, userId);
        return this.baseMapper.selectList(queryWrapper).stream().map(AiViewRecord::getRoomId).collect(Collectors.toList());
    }

    /**
     * 获取面试记录
     *
     * @param roomId
     * @return
     */
    @Override
    public AiViewRecord getByRoomId(String roomId) {
        QueryWrapper<AiViewRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiViewRecord::getRoomId, roomId);
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<AiViewRecord> getViewRecord(String userId, String type) {
        QueryWrapper<AiViewRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(AiViewRecord::getUserId, userId)
                .eq(AiViewRecord::getChatType, type)
                .ne(AiViewRecord::getStatus, InterviewStatus.STOP)
                .ne(AiViewRecord::getStatus, InterviewStatus.INIT)
                .ne(AiViewRecord::getStatus, InterviewStatus.CHATTING);
        queryWrapper.lambda().orderByDesc(AiViewRecord::getCreateTime);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<String> generateQuestion(GenerateQuestionDTO generateQuestionDTO) {

        AiViewRecord aiViewRecord = new AiViewRecord();
        BeanUtils.copyProperties(generateQuestionDTO, aiViewRecord);
        AiJobExamine examineObj = aiJobExamineService.getExamByInterview(aiViewRecord);
        String examine = examineObj.getContent();

        String jd = "";
        if (ApplyType.POSITION.name().equals(aiViewRecord.getChatType())) {
            String positionId = aiViewRecord.getPositionId();
            AiJobPosition aiJobPosition = aiJobPositionService.getById(positionId);
            jd = aiJobPosition.getContent();
        } else if (ApplyType.TRAIN.name().equals(aiViewRecord.getChatType())) {
            String trainId = aiViewRecord.getTrainId();
            AiJobTrain aiJobTrain = aiJobTrainService.getById(trainId);
            jd = aiJobTrain.getContent();
        }
        String resume = aiUserResumeService.getCurrentOpened(UserInfoUtils.getCurrentUserId());

        GenerateQsAgentDTO generateQsAgentDTO = new GenerateQsAgentDTO();
        generateQsAgentDTO.setExamine(examine);
        generateQsAgentDTO.setJd(jd);
        generateQsAgentDTO.setResume(resume);

        return generateQuestionAgent.getQuestions(generateQsAgentDTO);
    }

    @Override
    public PreApplyVO preApply(String userId) {

        AiViewRecord one = this.getOne(new LambdaQueryWrapper<AiViewRecord>()
                .eq(AiViewRecord::getUserId, userId)
                .orderByDesc(AiViewRecord::getCreateTime)
                .last("limit 1"));
        if(null != one){
            return one.wrapToPreApplyVO();
        }else {
            return UserInfoUtils.getLoginUser().wrapToPreApplyVO();
        }

    }

}
