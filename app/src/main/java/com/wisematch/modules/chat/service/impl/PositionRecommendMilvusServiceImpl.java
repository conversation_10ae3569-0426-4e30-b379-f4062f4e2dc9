package com.wisematch.modules.chat.service.impl;

import com.wisematch.modules.chat.config.MilvusTemplate;
import com.wisematch.modules.chat.enums.ExperienceRequirement;
import com.wisematch.modules.chat.enums.MinimumEducation;
import com.wisematch.modules.chat.enums.RecruitmentType;
import com.wisematch.modules.chat.enums.WiserConstant;
import com.wisematch.modules.chat.model.JobIntent;
import com.wisematch.modules.chat.service.PositionRecommendMilvusService;
import com.wisematch.modules.chat.utils.EmbeddingUtils;
import io.milvus.v2.common.IndexParam;
import io.milvus.v2.service.vector.response.QueryResp;
import io.milvus.v2.service.vector.response.SearchResp;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
@Slf4j
public class PositionRecommendMilvusServiceImpl implements PositionRecommendMilvusService {

    @Resource
    private MilvusTemplate milvusTemplate;

    @Resource
    private EmbeddingUtils embeddingUtils;

    // 配置常量
    private static final String POSITION_COLLECTION = "position_recommendation"; // 职位向量集合名
    private static final String VECTOR_FIELD = "embedding"; // 向量字段名
    private static final int SEARCH_LIMIT = 5; // 搜索数量限制

    @Override
    public List<String> chatRecommend(JobIntent jobIntent) {
        // 构建搜索条件
        String scalarFilter = buildScalarFilter(jobIntent);
        log.info("构建的标量过滤条件: {}", scalarFilter);

        // 执行向量搜索
        List<String> recommendations = performVectorSearch(jobIntent, scalarFilter);
        log.info("职位推荐完成，返回结果数量: {}", recommendations.size());
        return recommendations;
    }

    /**
     * 构建标量过滤条件
     * 不同字段之间是且的关系，字段内部数组是或的关系
     */
    private String buildScalarFilter(JobIntent jobIntent) {
        return Stream.of(
                        buildEmploymentCondition(jobIntent),
                        buildSalaryCondition(jobIntent),
                        buildExperienceCondition(jobIntent),
                        buildEducationCondition(jobIntent),
                        buildPositionStatusCondition(),
                        buildIsDeleteCondition()
                )
                .filter(Objects::nonNull)
                .collect(Collectors.joining(" AND "));
    }

    /**
     * 新增：职位状态条件（在用）
     */
    private String buildPositionStatusCondition() {
        return "position_status == " + WiserConstant.IN_USE;
    }

    /**
     * 新增：未删除条件
     */
    private String buildIsDeleteCondition() {
        return "del_status == " + WiserConstant.NOT_DELETE;
    }

    /**
     * 新增：已认证条件
     */
    private String buildIsVerifyCondition() {
        return "audit_status == " + WiserConstant.VERIFIED;
    }

    /**
     * 构建招聘类型条件
     */
    private String buildEmploymentCondition(JobIntent jobIntent) {
        return Optional.ofNullable(jobIntent.getEmploymentType())
                .filter(list -> !list.isEmpty())
                .map(list -> list.stream()
                        .map(typeStr -> {
                            try {
                                return RecruitmentType.getByName(typeStr);
                            } catch (IllegalArgumentException e) {
                                log.warn("未知的招聘类型: {}", typeStr);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .map(RecruitmentType::getCode)
                        .collect(Collectors.toList()))
                .filter(codes -> !codes.isEmpty())
                .map(codes -> "recruitment_type in [" +
                        codes.stream()
                                .map(String::valueOf)
                                .collect(Collectors.joining(",")) + "]")
                .orElse(null);
    }

    /**
     * 构建薪资条件（判断范围是否有交集）
     */
    private String buildSalaryCondition(JobIntent jobIntent) {
        return Optional.ofNullable(jobIntent.getSalary())
                .filter(list -> !list.isEmpty())
                .map(salaries -> salaries.stream()
                        .map(salary -> {
                            // 提取有效的最小和最大薪资（过滤空值和空字符串）
                            Optional<String> minOpt = Optional.ofNullable(salary.getMin())
                                    .filter(StringUtils::isNotEmpty);
                            Optional<String> maxOpt = Optional.ofNullable(salary.getMax())
                                    .filter(StringUtils::isNotEmpty);

                            // 生成单个薪资范围的条件，并包裹括号明确分组
                            return minOpt.flatMap(min ->
                                    maxOpt.map(max ->
                                            // 既有min又有max：(salary_min <= max AND salary_max >= min)
                                            String.format("(salary_min <= %s AND salary_max >= %s)", max, min)
                                    )
                            ).orElseGet(() ->
                                    minOpt.map(min ->
                                            // 只有min：(salary_max >= min)
                                            String.format("(salary_max >= %s)", min)
                                    ).orElseGet(() ->
                                            maxOpt.map(max ->
                                                    // 只有max：(salary_min <= max)
                                                    String.format("(salary_min <= %s)", max)
                                            ).orElse(null)
                                    )
                            );
                        })
                        .filter(Objects::nonNull) // 过滤无效条件
                        .collect(Collectors.toList()))
                .filter(conditions -> !conditions.isEmpty())
                // 多个薪资范围用OR连接，整体再包裹一层括号
                .map(conditions -> "(" + String.join(" OR ", conditions) + ")")
                .orElse(null);
    }


    /**
     * 构建经验要求条件（包含所有不高于目标级别的经验要求）
     */
    private String buildExperienceCondition(JobIntent jobIntent) {
        return Optional.ofNullable(jobIntent.getExperienceRequirement())
                .filter(list -> !list.isEmpty())
                .map(list -> list.stream()
                        // 转换为枚举实例
                        .map(expStr -> {
                            try {
                                return ExperienceRequirement.getByName(expStr);
                            } catch (IllegalArgumentException e) {
                                log.warn("未知的经验要求: {}", expStr);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        // 关键修改：获取当前级别及所有更低级别的code
                        .flatMap(req -> getAllLowerOrEqualCodes(req).stream())
                        // 去重避免重复条件
                        .distinct()
                        .collect(Collectors.toList()))
                .filter(codes -> !codes.isEmpty())
                .map(codes -> "work_experience in [" +
                        codes.stream()
                                .map(String::valueOf)
                                .collect(Collectors.joining(",")) + "]")
                .orElse(null);
    }

    /**
     * 获取当前经验要求及所有更低级别的code
     * 例如：1-3年(2) 会返回 [0,1,2]
     */
    private List<Integer> getAllLowerOrEqualCodes(ExperienceRequirement requirement) {
        int targetCode = requirement.getCode();
        // 遍历所有枚举值，筛选出code小于等于目标code的
        return Arrays.stream(ExperienceRequirement.values())
                .map(ExperienceRequirement::getCode)
                .filter(code -> code <= targetCode)
                .collect(Collectors.toList());
    }

    /**
     * 构建学历要求条件，若用户需求学历为某层级，推送该层级及更低层级（含不限）的职位
     */
    private String buildEducationCondition(JobIntent jobIntent) {
        return Optional.ofNullable(jobIntent.getEducationRequirement())
                .filter(list -> !list.isEmpty())
                .map(list -> list.stream()
                        // 将学历名称转换为枚举实例
                        .map(eduStr -> {
                            try {
                                return MinimumEducation.getByName(eduStr);
                            } catch (IllegalArgumentException e) {
                                log.warn("未知的学历要求: {}", eduStr);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        // 关键：获取当前学历及更低层级的所有学历 code
                        .flatMap(eduEnum -> getLowerAndEqualEducationCodes(eduEnum).stream())
                        // 去重，避免同一 code 重复出现
                        .distinct()
                        .collect(Collectors.toList()))
                .filter(codes -> !codes.isEmpty())
                .map(codes -> "education in [" +
                        codes.stream()
                                .map(String::valueOf)
                                .collect(Collectors.joining(",")) + "]")
                .orElse(null);
    }

    /**
     * 获取目标学历及更低层级（含不限）的学历 code 列表
     * 例如：目标为本科（code=5），则返回 [0,1,2,3,4,5]
     *
     * @param targetEducation 目标学历枚举实例
     * @return 目标学历及更低层级的学历 code 集合
     */
    private List<Integer> getLowerAndEqualEducationCodes(MinimumEducation targetEducation) {
        List<Integer> result = new ArrayList<>();
        // 遍历所有学历枚举，筛选出 code 小于等于目标学历 code 的
        for (MinimumEducation education : MinimumEducation.values()) {
            if (education.getCode() <= targetEducation.getCode()) {
                result.add(education.getCode());
            }
        }
        return result;
    }

    /**
     * 降级：仅执行标量查询
     */
    private List<String> performScalarQuery(String scalarFilter) {
        try {
            log.info("执行标量查询，过滤条件: {}", scalarFilter);

            QueryResp queryResp = milvusTemplate.query(
                    POSITION_COLLECTION,
                    scalarFilter,
                    List.of("position_id"),
                    SEARCH_LIMIT,
                    0
            );

            return Optional.ofNullable(queryResp)
                    .map(QueryResp::getQueryResults)
                    .filter(results -> !results.isEmpty())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(result -> {
                        try {
                            return result.getEntity().get("position_id").toString();
                        } catch (Exception e) {
                            log.warn("构建CardInfo失败", e);
                            return null;
                        }
                    })
                    .distinct()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("标量查询失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 执行向量搜索
     */
    private List<String> performVectorSearch(JobIntent jobIntent, String scalarFilter) {
        try {
            // 1. 准备向量化文本
            List<String> vectorTexts = prepareVectorTexts(jobIntent);
            log.info("准备向量化文本，文本数量: {}", vectorTexts);
            if (vectorTexts.isEmpty()) {
                log.info("没有需要向量化的文本，执行标量查询");
                return performScalarQuery(scalarFilter);
            }

            // 2. 文本向量化
            List<List<Double>> embeddings = embeddingUtils.embedTexts(vectorTexts, 768);
            log.info("向量化完成，向量数量: {}", embeddings.size());

            // 3. 转换为Float格式（Milvus要求）
            List<List<Float>> floatEmbeddings = embeddings.stream()
                    .map(embedding -> embedding.stream()
                            .map(Double::floatValue)
                            .collect(Collectors.toList()))
                    .collect(Collectors.toList());

            // 4. 执行向量搜索
            SearchResp searchResp = milvusTemplate.search(
                    POSITION_COLLECTION,
                    floatEmbeddings,
                    SEARCH_LIMIT,
                    List.of("position_id"),
                    scalarFilter,
                    VECTOR_FIELD,
                    IndexParam.MetricType.COSINE,
                    buildSearchParams(),
                    "position_id",
                    1
            );

            // 5. 处理搜索结果
            return processSearchResults(searchResp);

        } catch (Exception e) {
            log.error("向量搜索执行失败", e);
            // 降级到标量查询
            return performScalarQuery(scalarFilter);
        }
    }

    /**
     * 构建搜索参数
     */
    private Map<String, Object> buildSearchParams() {
        Map<String, Object> searchParams = new HashMap<>();
        searchParams.put("nprobe", 16); // IVF索引探测参数
        return searchParams;
    }

    /**
     * 准备向量化文本
     */
    private List<String> prepareVectorTexts(JobIntent jobIntent) {
        return Stream.of(
                        Optional.ofNullable(jobIntent.getWorkLocation()).orElse(Collections.emptyList()).stream(),
                        Optional.ofNullable(jobIntent.getBenefits()).orElse(Collections.emptyList()).stream(),
                        Optional.ofNullable(jobIntent.getSkills()).orElse(Collections.emptyList()).stream(),
                        Optional.ofNullable(jobIntent.getJobDescription()).orElse(Collections.emptyList()).stream()
                )
                .flatMap(Function.identity())
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 处理搜索结果
     */
    private List<String> processSearchResults(SearchResp searchResp) {
        return Optional.ofNullable(searchResp)
                .map(SearchResp::getSearchResults)
                .filter(results -> !results.isEmpty())
                .orElse(Collections.emptyList())
                .stream()
                .flatMap(Collection::stream) // 展平第一层列表
                .map(result -> {
                    try {
                        return result.getEntity().get("position_id").toString();
                    } catch (Exception e) {
                        log.warn("构建processSearchResults失败，结果ID: {}", result.getId(), e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

}
