package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version Message.java, v0.1 2025-06-20 17:02
 */
@Data
public class MatcherReplyViewMessage extends ViewMessage {

    /**
     * 用户提示建议
     */
    @Schema(description = "用户提示建议")
    List<String> tips;

    /**
     * 人才推荐卡片
     */
    @Schema(description = "人才推荐卡片")
    List<PortraitBrief> portraitBriefs;

}
