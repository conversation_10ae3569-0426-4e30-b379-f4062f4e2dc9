package com.wisematch.modules.chat.model;

import com.wisematch.common.model.PageRequest;
import com.wisematch.modules.chat.entity.AiOrganization;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AiOrganizationQueryDTO extends AiOrganization implements PageRequest {

    @Schema(description = "企业徽标")
    private String organizationLogo;

    @Schema(description = "企业名称")
    private String organizationName;

    @Schema(description = "唯一社会信用代码")
    private String uniqueSocialCreditCode;

    @Schema(description = "营业执照")
    private String businessLicense;

    @Schema(description = "企业介绍")
    private String organizationIntroduction;

    @Schema(description = "是否认证（0未审核，1已认证，2驳回）")
    private Integer isCertified;

    @Schema(description = "是否删除（0不删除，1删除）")
    private Integer isDel;

    @Schema(description = "是否注销账号（0未注销，1已注销）")
    private Integer isCancelAccount;

    @Schema(description = "是否公开（0不公开，1公开）")
    private Integer isOpen;

    @Schema(description = "页码")
    private Integer pageNum = 1;

    @Schema(description = "每页数量")
    private Integer pageSize = 10;
}

