package com.wisematch.modules.chat.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ai_chat_memory")
@Schema(description = "聊天记录")
public class AiChatMemory {

    @TableId(value = "id", type = IdType.AUTO)
    public Long id;

    @Schema(description = "对话id")
    public String conversationId;

    @Schema(description = "聊天内容")
    public String content;

    @Schema(description = "角色")
    public String type;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date timestamp;

    @Schema(description = "面试第几个问题")
    public Integer questionIndex;

}
