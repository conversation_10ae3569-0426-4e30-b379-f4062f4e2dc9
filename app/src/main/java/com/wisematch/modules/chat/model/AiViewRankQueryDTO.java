package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class AiViewRankQueryDTO {

    @NotBlank(message = "岗位不能为空")
    @Schema(description = "岗位", requiredMode = Schema.RequiredMode.REQUIRED)
    String position;

    @Schema(description = "城市", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    String city;

    @Schema(description = "分页页码，暂时不传")
    Integer page = 1;

    @Schema(description = "分页页大小，暂时不传")
    Integer size = 1000;

    String userId;

    private String chatId;
}
