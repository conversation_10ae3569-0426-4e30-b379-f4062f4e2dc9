package com.wisematch.modules.chat.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version AiChatASRConfig.java, v0.1 2025-06-24 22:55
 */
@Data
public class AiChatASRConfig {

    @JSONField(name = "Provider")
    String Provider = "volcano";

    @JSONField(name = "ProviderParams")
    JSONObject ProviderParams;

    @JSONField(name = "VADConfig")
    JSONObject VADConfig;

    @JSONField(name = "VolumeGain")
    Float VolumeGain = 0.3f;

    @JSONField(name = "InterruptConfig")
    JSONObject InterruptConfig;

    /**
     * 0：（默认值）：禁用 AI 降噪。
     * 1：轻度降噪。适用于抑制微弱、平稳的背景噪声。
     * 2：中度降噪。适用于抑制中度平稳噪声，如空调声、风扇声。
     * 3：强度降噪。适用于抑制嘈杂、非平稳的动态噪音，如键盘敲击声、物体碰撞声、动物叫声等
     */
    @JSONField(name = "AnsMode")
    Integer AnsMode = 3;

    {
        ProviderParams = new JSONObject();
        ProviderParams.put("Mode", "bigmodel");
        ProviderParams.put("AppId", "**********");
        ProviderParams.put("AccessToken", "M_DO1jpElFo63yeP6roC0vc18_J6lYxq");
        ProviderParams.put("ApiResourceId", "volc.bigasr.sauc.duration");
        ProviderParams.put("StreamMode", 0);


        VADConfig = new JSONObject();
        //判停时间。房间内真人用户停顿时间若高于该值设定时间，则认为一句话结束。ms
        VADConfig.put("SilenceTime", 1500);
        //TODO 限时公测阶段
        VADConfig.put("AIVAD",true);

        InterruptConfig = new JSONObject();
        //自动打断触发阈值。房间内真人用户持续说话时间达到该参数设定值后，智能体自动停止输出。ms
        InterruptConfig.put("InterruptSpeechDuration", 0);
        InterruptConfig.put("InterruptKeywords", List.of("暂停", "暂停一下", "打断", "停止", "停下", "停一下"));
    }
}
