package com.wisematch.modules.chat.controller;

import com.wisematch.common.utils.R;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.model.CardInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户消息服务
 * <AUTHOR>
 * @version AiChatController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/user/message")
@Tag(name = "消息通知", description = "AI面试用户消息")
@Slf4j
public class AiUserMessageController {

    @Operation(description = "消息列表")
    @GetMapping("/list")
    public R list() {
        String userId = UserInfoUtils.getCurrentUserId();
        List<CardInfo> list = new ArrayList<>();
        CardInfo cardInfo = new CardInfo();
        cardInfo.setTitle("加入候选人");
        cardInfo.setSummary("恭喜你，你投递的岗位被普特教育集团加入候选人");
        cardInfo.setLabels("2025-06-02 12:00:01");
        list.add(cardInfo);
        CardInfo cardInfo2 = new CardInfo();
        cardInfo2.setTitle("加入候选人");
        cardInfo2.setSummary("恭喜你，你投递的岗位被阿里巴巴集团加入候选人");
        cardInfo2.setLabels("2025-06-02 12:00:01");
        list.add(cardInfo2);
        CardInfo cardInfo3 = new CardInfo();
        cardInfo3.setTitle("加入人才池");
        cardInfo3.setSummary("恭喜你，你投递的岗位被加入人才池");
        cardInfo3.setLabels("2025-06-02 12:00:01");
        list.add(cardInfo3);
        return R.ok().setData(list);
    }

}
