package com.wisematch.modules.chat.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class CandidateReport {

    /** 评估维度列表（核心评估内容） */
    private List<Dimension> dimensions;

    @Data
    public static class Dimension {
        /** 评估检查点（维度具体名称，如"平台规则认知"） */
        @SerializedName("checkPoint")
        private String checkPoint;

        /** 评估评语（对候选人表现的详细分析） */
        //需要RAG
        private String comments;

        /** 维度标签（如"知识技能"、"语言表达能力"） */
        private String labels;

        /** 维度评分（1-5分） */
        private Integer score;

        /** 维度总结（候选人表现核心概括） */
        //需要RAG
        private String summary;
    }
}
