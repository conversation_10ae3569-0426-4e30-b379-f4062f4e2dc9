package com.wisematch.modules.chat.agent;

import com.wisematch.modules.chat.entity.AiAgentPool;
import com.wisematch.modules.chat.model.ReportInput;
import com.wisematch.modules.chat.service.AiAgentPoolService;
import com.wisematch.modules.chat.utils.JsonExtractor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Wiser Agent 聊天门面
 * <AUTHOR>
 * @version IntentionAgentFacade.java, v0.1 2025-07-11 11:02
 */
@Component
@Slf4j
public class AiReportAgentFacade {

    @Resource
    AgentFacade agentFacade;

    @Resource
    private AiAgentPoolService aiAgentPoolService;


    public String getReport(ReportInput input){

        long start = System.currentTimeMillis();

        String poolId = input.getPoolId();

        AiAgentPool aiAgentPool = aiAgentPoolService.getById(poolId);
        String agentCode = aiAgentPool.getAgentCode();
        String prompt = agentFacade.getPrompt(agentCode);

        prompt = prompt.replace("$history",input.getHistory());
        prompt = prompt.replace("$examine",input.getAssessment());
        prompt = prompt.replace("$jd", input.getPositionContent() == null ? "" : input.getPositionContent());

        AgentContext agentContext = new AgentContext();
        agentContext.setAgentCode(agentCode);
        agentContext.setPrompt(prompt);
        agentContext.setUserMsg("生成报告");
        String reply =  agentFacade.supply(agentContext);
        reply = JsonExtractor.extractJSON(reply);
        log.info("report success, cost: {} ms", (System.currentTimeMillis() - start));
        return reply;
    }

}
