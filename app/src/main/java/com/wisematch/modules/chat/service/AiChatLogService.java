package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiChatLog;
import com.wisematch.modules.chat.entity.AiChatMemory;
import com.wisematch.modules.chat.model.AiChatLogQueryDTO;
import com.wisematch.modules.chat.model.AiChatMemoryQueryDTO;
import com.wisematch.modules.chat.model.ShortMessage;

import java.util.List;


/**
 * AI智能体服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */
public interface AiChatLogService extends IService<AiChatLog> {

    List<AiChatLog> queryMemory(AiChatLogQueryDTO aiChatLogQueryDTO);
}
