package com.wisematch.modules.chat.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiUserPreferences;
import com.wisematch.modules.chat.model.*;

import java.util.List;

public interface AiUserPreferencesService extends IService<AiUserPreferences> {

    void logicDelete(String ids);

    Page<AiUserPreferences> pageQuery(AiUserPreferencesQueryDTO queryDTO);

    void batchLogicDelete(List<String> list);

    void saveOrUpdate(UserPreferenceVO userPreferenceVO);

    AiUserPreferences getOne(String userId, String bizSence, String targetId);

    Page<String> candidateUserId(CandidatesDTO candidatesDTO, String companyUserId);

    List<String> candidateUserId(String companyUserId);

    ChatMessage wrapWiserChatMessage(ChatMessage chatMessage);

    ChatMessage<CardInfoVO> wrapMatcherChatMessage(ChatMessage<CardInfoVO> chatMessage);

    List<CardInfo> cardInfoAddPreference(List<CardInfo> cardInfos);


    List<CardInfo> cardInfoAddPreference(String userId, List<CardInfo> cardInfos);

    public void notifyUser(AiUserPreferences aiUserPreferences, String targetId);

    AiUserPreferences viewObj(String userId, String targetId, String bizSence);

    List<AiUserPreferences> getPreferenceList(List<String> targetIds, String userId, String bizSence);
}

