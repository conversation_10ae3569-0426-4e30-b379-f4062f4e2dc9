package com.wisematch.modules.chat.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文本类型枚举（对应职位字段的类型标识）
 */
@Getter
@AllArgsConstructor
public enum PositionTextTypeEnum {
    SUMMARY("summary", "职位概述文本"),
    CONTENT("content", "职位详情拆分文本"),
    TITLE("title", "职位名称文本"),
    COMPANY("company", "公司名称文本"),
    BENEFITS("benefits", "职位福利文本"),
    SKILLS("skills", "职位技能要求文本"),
    LOCATION("location", "工作地点文本");

    /**
     * 文本类型标识（存储到Milvus的text_type字段）
     */
    private final String type;

    /**
     * 类型描述
     */
    private final String description;
}