package com.wisematch.modules.chat.agent;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wisematch.modules.chat.enums.ViewerAgentConstant;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class AiPositionFacade {

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private AgentFacade agentFacade;

    private static final TypeReference<List<String>> STRING_LIST_TYPE = new TypeReference<>() {};

    public List<String> split(String content) {
        String prompt = agentFacade.getPrompt(ViewerAgentConstant.POSITION_PARSE_LIST);
        prompt = prompt.replace("$content", content);

        AgentContext agentContext = new AgentContext();
        agentContext.setAgentCode(ViewerAgentConstant.POSITION_PARSE_LIST);
        agentContext.setPrompt(prompt);
        agentContext.setUserMsg("按照句意拆分句子");
        String reply =  agentFacade.supply(agentContext);

        // 移除可能存在的JSON代码块标记
        String cleanedReply = reply.replaceAll("^```json\\s*", "").replaceAll("\\s*```$", "");

        try {
            return objectMapper.readValue(cleanedReply, STRING_LIST_TYPE);
        } catch (Exception e) {
            log.error("AiContentSplitFacade#get", e);
            return new ArrayList<>();
        }
    }
}
