package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.cache.GuavaCache;
import com.wisematch.modules.chat.entity.AiChatIntention;
import com.wisematch.modules.chat.mapper.AiChatIntentionMapper;
import com.wisematch.modules.chat.service.AiChatIntentionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 意图识别管理
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiChatIntentionServiceImpl extends ServiceImpl<AiChatIntentionMapper, AiChatIntention> implements AiChatIntentionService {

    @Autowired
    public GuavaCache guavaCache;

    @Override
    public AiChatIntention getIntention(String code, String bizScene) {
        String key = bizScene + "_" + code;
//        if (guavaCache.getObj(key) != null) {
//            return (AiChatIntention) guavaCache.getObj(key);
//        }
        // 构造查询条件
        QueryWrapper<AiChatIntention> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiChatIntention::getCode, code).eq(AiChatIntention::getBizScene, bizScene);
        AiChatIntention aiChatIntention = this.baseMapper.selectOne(queryWrapper);
        if (aiChatIntention != null) {
            guavaCache.putObj(key, aiChatIntention);
        }
        return aiChatIntention;
    }
}
