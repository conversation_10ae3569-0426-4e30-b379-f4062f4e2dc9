package com.wisematch.modules.chat.controller;

import com.wisematch.common.annotation.SysLog;
import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.entity.AiJobPosition;
import com.wisematch.modules.chat.model.AiJobPositionQueryDTO;
import com.wisematch.modules.chat.model.PublishDeactivationDTO;
import com.wisematch.modules.chat.service.AiJobPositionService;
import com.wisematch.modules.chat.service.AiOrganizationService;
import com.wisematch.modules.chat.service.CardInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/positions")
@Tag(name = "岗位管理", description = "工作管理")
public class AiJobPositionController {

    @Autowired
    AiOrganizationService aiOrganizationService;
    @Autowired
    private AiJobPositionService aiJobPositionService;
    @Autowired
    private CardInfoService cardInfoService;

    @PostMapping
    @Operation(summary = "新增")
    @SysLog("新增岗位")
    @NotDoubleSubmit
    public R create(@RequestBody AiJobPosition dto) {
        aiOrganizationService.examCertification(UserInfoUtils.getCurrentUserId());
        aiJobPositionService.saveObj(dto);
        return R.ok().setData(dto);
    }

    @PostMapping("/update")
    @Operation(summary = "修改")
    @SysLog("修改岗位")
    @NotDoubleSubmit
    public R update(@RequestBody AiJobPosition dto) {
        aiOrganizationService.examCertification(UserInfoUtils.getCurrentUserId());
        aiJobPositionService.updateObjById(dto);
        return R.ok().setData(dto);
    }

    @PostMapping("/publishDeactivation")
    @Operation(summary = "发布停用")
    @NotDoubleSubmit
    @SysLog("发布停用")//Publish Deactivation
    public R publishDeactivation(@RequestBody PublishDeactivationDTO dto) {
        aiOrganizationService.examCertification(UserInfoUtils.getCurrentUserId());
        aiJobPositionService.publishDeactivation(dto);
        return R.ok().setData(dto);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取单条（根据id）")
    public R getById(@PathVariable String id) {
        return R.ok().setData(aiJobPositionService.detail(id));
    }

    @PostMapping("/pageQueryBack")
    @Operation(summary = "后台查询列表")
    @NotDoubleSubmit
    public R pageQueryBack(
            @RequestBody AiJobPositionQueryDTO aiJobPosition) {
        aiJobPosition.setUserId(UserInfoUtils.getCurrentUserId());
        return R.ok().setData(aiJobPositionService.pageQueryBack(aiJobPosition));
    }

    @PostMapping("/pageQueryFront")
    @Operation(summary = "Wiser端岗位列表(前台)")
    @NotDoubleSubmit
    public R pageQueryFront(
            @RequestBody AiJobPositionQueryDTO aiJobPosition) {
        return R.ok().setData(cardInfoService.positionCardInfo(aiJobPosition));
    }

    @PostMapping("/getByJobViewId")
    @Operation(summary = "获取JobViewId获取列表")
    @NotDoubleSubmit
    @Deprecated
    public R getByJobViewId(
            @RequestBody AiJobPositionQueryDTO aiJobPosition) {
        return R.ok().setData(aiJobPositionService.getByJobViewId(aiJobPosition));
    }

    @PostMapping("/selectAll")
    @Operation(summary = "查询所有工作（下拉列表）")
    @NotDoubleSubmit
    public R selectAll(@RequestBody AiJobPositionQueryDTO queryDTO) {
        return R.ok().setData(aiJobPositionService.selectAll(queryDTO));
    }

}
