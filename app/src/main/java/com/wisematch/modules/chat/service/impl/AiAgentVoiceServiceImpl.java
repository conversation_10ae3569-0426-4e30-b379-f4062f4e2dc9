package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.utils.BeanCopyUtils;
import com.wisematch.modules.chat.entity.AiAgentVoice;
import com.wisematch.modules.chat.mapper.AiAgentVoiceMapper;
import com.wisematch.modules.chat.model.AiAgentVoiceVO;
import com.wisematch.modules.chat.service.AiAgentVoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * agent 音色列表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiAgentVoiceServiceImpl extends ServiceImpl<AiAgentVoiceMapper, AiAgentVoice> implements AiAgentVoiceService {

    @Override
    public List<AiAgentVoiceVO> getList() {

        QueryWrapper<AiAgentVoice> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByDesc(AiAgentVoice::getId);

        return BeanCopyUtils.copyList(this.baseMapper.selectList(queryWrapper), AiAgentVoiceVO::toVO);
    }

}
