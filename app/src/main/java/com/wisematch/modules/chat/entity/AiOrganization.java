package com.wisematch.modules.chat.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wisematch.modules.common.handler.businessLicenseVerify.BusinessLicenseContext;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("ai_organization")
@Schema(description = "企业组织信息")
public class AiOrganization implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "地址")
    private String businessAddress;

    @Schema(description = "法人")
    @Deprecated
    private String legalRepresentative;

    @Schema(description = "法人")
    private String legalPerson;

    @Schema(description = "法人身份证")
    private String legalPersonIdCard;

    @Schema(description = "官网链接")
    private String businessUrl;

    @TableId(type = IdType.ASSIGN_UUID)
    @Schema(description = "主键ID")
    private String id;

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间")
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "企业徽标")
    private String organizationLogo;

    @Schema(description = "企业名称")
    private String organizationName;

    @Schema(description = "唯一社会信用代码")
    private String uniqueSocialCreditCode;

    @Schema(description = "营业执照")
    private String businessLicense;

    @Schema(description = "企业介绍")
    private String organizationIntroduction;

    @Schema(description = "是否认证（0未审核，1已认证，2驳回）")
    private Integer isCertified;

    @Schema(description = "是否删除（0不删除，1删除）")
    private Integer isDel;

    @Schema(description = "是否注销账号（0未注销，1已注销）")
    private Integer isCancelAccount;

    @Schema(description = "是否公开（0不公开，1公开）")
    private Integer isOpen;

    @Schema(description = "userId")
    private String userId;

    public static AiOrganization generateAiOrganization(BusinessLicenseContext businessLicenseContext){
        AiOrganization organization = new AiOrganization();
        organization.setBusinessLicense(businessLicenseContext.getFileSrc());
        organization.setOrganizationName(businessLicenseContext.getCompanyName());
        organization.setUniqueSocialCreditCode(businessLicenseContext.getCreditCode());
        organization.setIsCertified(businessLicenseContext.getIsCertified());
        organization.setBusinessAddress(businessLicenseContext.getBusinessAddress());
        organization.setLegalPerson(businessLicenseContext.getLegalPerson());
        return organization;
    }
}

