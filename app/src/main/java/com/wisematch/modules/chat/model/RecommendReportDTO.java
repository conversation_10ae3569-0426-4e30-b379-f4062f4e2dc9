package com.wisematch.modules.chat.model;

import lombok.Data;

import java.util.List;

@Data
public class RecommendReportDTO {


    /**
     * 用户唯一标识
     */
    private String userId;

    /**
     * 房间ID
     */
    private String roomId;

    /**
     * 删除状态
     */
    private Integer delStatus;

    /**
     * 用户状态
     */
    private Integer status;

    /**
     * 画像状态
     * 对应Milvus Int32类型
     */
    private Integer portraitStatus;

    /**
     * 画像审核状态
     */
    private Integer portraitVerifyStatus;


    /**
     * 岗位胜任力模型关联ID
     */
    private String examineId;

    /**
     * 考核维度
     */
    private String dimensions;

    /**
     * 考核点
     */
    private String checkpoints;

    /**
     * 分数
     */
    private Integer checkpointScore;

    /**
     * 文本类型：评价/总结
     */
    private String textType;

    /**
     * 原始文本内容
     */
    private String textContent;

    /**
     * 面试报告向量（可选，根据传输需求决定是否包含）
     */
    private List<Double> reportVector;
}
