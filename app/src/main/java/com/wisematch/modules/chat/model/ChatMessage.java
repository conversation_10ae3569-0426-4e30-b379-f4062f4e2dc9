package com.wisematch.modules.chat.model;

import com.alibaba.fastjson.JSONObject;
import com.wisematch.modules.chat.entity.AiChatMemory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

import static com.wisematch.modules.chat.enums.AttentionMapAgent.ASSISTANT;

/**
 * <AUTHOR>
 * @version Message.java, v0.1 2025-06-20 17:02
 */
@Data
@NoArgsConstructor
public class ChatMessage<T extends CardInfo> extends Message {

    public ChatMessage(String msg) {
        this.msg = msg;
    }

    String chatId;

    String agentId;

    @Deprecated
    String positionId;

    @Deprecated
    String trainId;

    String userId;

    boolean enableThinking;
    /**
     * 用户提示建议
     */
    @Schema(description = "用户提示建议")
    List<String> tips;

    /**
     * 人才推荐卡片
     */
    @Schema(description = "人才推荐卡片")
    List<T> cardInfos;

    Date dateTime;

    @Schema(description = "额外信息")
    JSONObject extra;

    public static ChatMessage aiChatMemoryToChatWiserVO(AiChatMemory aiChatMemory){
        if (aiChatMemory != null) {
            ChatMessage wiserReplyMessage = new ChatMessage();
            wiserReplyMessage.setMsg(aiChatMemory.getContent());
            wiserReplyMessage.setRole(ASSISTANT);
            wiserReplyMessage.setDateTime(aiChatMemory.getTimestamp());
            wiserReplyMessage.setChatId(aiChatMemory.getConversationId());
            return wiserReplyMessage;
        }
        return null;
    }
}
