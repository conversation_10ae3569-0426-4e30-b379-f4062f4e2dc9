package com.wisematch.modules.chat.model;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wisematch.common.utils.EnumUtil;
import com.wisematch.modules.chat.entity.AiNotifyMessage;
import com.wisematch.modules.chat.enums.AiNotifyMessageType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class AiNotifyMessageVO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "职位")
    private String position;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "消息类型(加入候选人CANDIDATE，" +
            "加入人才池TALENT_POOL，" +
            "报告生成REPORT，" +
            "系统消息SYSTEM)")
    @TableField("biz_sence")
    private String bizSence;

    @Schema(description = "接收者id")
    private String receiverId;

    @Schema(description = "发送者类型")
    private String senderType;

    @Schema(description = "发送者id")
    private String senderId;

    @Schema(description = "来源业务id")
    private String sourceId;

    @Schema(description = "状态：0 未读，1 已读，2 撤回")
    private Integer msgStatus;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "logo")
    private String logo;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "业务状态")
    private Integer bizStatus;

    @Schema(description = "回执内容")
    private String replyMsg;

    @Schema(description = "发送者头像")
    private String senderPhoto;

    @Schema(description = "接收者头像")
    private String receiverPhoto;

    @Schema(description = "消息类型")
    private String msgType;

    @Schema(description = "消息类型")
    private String typeIcon;

    @Schema(description = "跳转url")
    private String redirectUrl;

    @Schema(description = "发送者姓名")
    private String senderName;

    @Schema(description = "接收者姓名")
    private String receiverName;

    public static AiNotifyMessageVO toVO(AiNotifyMessage entity) {
        if (entity == null) {
            return null;
        }
        AiNotifyMessageVO vo = new AiNotifyMessageVO();
        BeanUtils.copyProperties(entity, vo);
        setEnumAttribute(vo);
        return vo;
    }

    public static void setEnumAttribute(AiNotifyMessageVO vo){
        AiNotifyMessageType type = EnumUtil.getEnumByName(AiNotifyMessageType.class, vo.getBizSence());
        vo.setTitle(vo.getPosition());
        vo.setTypeIcon(type.getTypeIcon());
        vo.setRedirectUrl(type.getRedirectUrl());
    }

    public static List<AiNotifyMessageVO> toVOList(List<AiNotifyMessage> entityList) {
        return entityList.stream()
                .map(AiNotifyMessageVO::toVO)
                .collect(Collectors.toList());
    }


}
