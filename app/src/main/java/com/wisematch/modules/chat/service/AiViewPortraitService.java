package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiJobPosition;
import com.wisematch.modules.chat.entity.AiViewPortrait;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.sys.model.PrivacyProtectionChangeDTO;
import org.springframework.ai.chat.messages.Message;

import java.util.List;


/**
 * AI人才画像服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */
public interface AiViewPortraitService extends IService<AiViewPortrait> {

    List<CardInfoVO> convert(List<CardInfo> cardInfos);

    List<AiViewPortrait> getByUserId(String userId);

    AiViewPortrait getByRoomId(String roomId);

    TalentPortrait generatePortrait(String chatRoomId);

    AiJobPosition getPositionByPortrait(String portraitId);

    TalentPortrait getPortraitById(String id);

    CardInfo getPortraitCardById(String id);

    List<CardInfo> talentCenter(String position);

    Page<CardInfoVO> talentCenterPage(TalentCenterPageDTO dto);

    Page<CardInfoVO> orgCandidateCard(CandidatesDTO candidate);

    List<TalentPortrait> orgCandidate(String companyUserId);

    AiViewPortrait getRecommend(String userId, String positionId);

    CardInfo portraitCardInfo(AiViewPortrait aiViewPortrait);

    void privacyProtectionChange(PrivacyProtectionChangeDTO params);

    CardInfo communicateCard(CommunicateCardDTO dto);

    List<PortraitRecommendDTO> getChatRecommend(List<Message> messages, String jobExamineId, String userMsg);

    List<AiViewPortrait> getByRoomId(List<String> roomIds);

    boolean generatePortraitSchedule(String roomId);

}
