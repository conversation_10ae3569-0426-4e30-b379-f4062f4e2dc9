package com.wisematch.modules.chat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AiCollegeSelectAllDTO {

    @Schema(description = "学校名称")
    private String collegeName;

    @Schema(description = "所属部门")
    private String collegeDepartment;

    @Schema(description = "所在地区")
    private String collegeLocation;

    @Schema(description = "学校层次")
    private String collegeLevel;

    @Schema(description = "学校类型")
    private String collegeTag;
}
