package com.wisematch.modules.chat.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 划线重点评语
 * <AUTHOR>
 * @version CommentMsg.java, v0.1 2025-06-22 15:06
 */
@Data
public class CommentMsg {

    /**
     * 简评
     */
    @Schema(description = "简评")
    private String summary;

    /**
     * 划线重点
     */
    @Schema(description = "划线重点")
    private String underline;

    @Schema(description = "划线细节")
    @JSONField(name = "underline_detail")
    private List<String> underlineDetail;

    /**
     * 标签
     */
    @Schema(description = "维度标签")
    private String labels;

    /**
     * 考核点
     */
    @Schema(description = "考核点")
    private String checkPoint;

    /**
     * 评语
     */
    @Schema(description = "评语")
    private String comments;

    /**
     * message index
     */
    @Schema(description = "message index")
    private Integer msgIndex;

    /**
     * 评分
     */
    @Schema(description = "评分")
    private Integer score;

    public static CommentMsg clone(CommentMsg commentMsg){
        CommentMsg newCM = new CommentMsg();
        newCM.setScore(commentMsg.getScore());
        newCM.setLabels(commentMsg.getLabels());
        newCM.setMsgIndex(commentMsg.getMsgIndex());
        newCM.setSummary(commentMsg.getSummary());
        newCM.setUnderline(commentMsg.getUnderline());
        newCM.setComments(commentMsg.getComments());
        return newCM;
    }
}
