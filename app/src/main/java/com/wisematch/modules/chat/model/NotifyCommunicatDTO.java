package com.wisematch.modules.chat.model;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class NotifyCommunicatDTO {

    @Schema(description = "职位", requiredMode = Schema.RequiredMode.REQUIRED)
    private String position;

    @Schema(description = "接收者头像", requiredMode = Schema.RequiredMode.REQUIRED)
    private String receiverPhoto;

    @Schema(description = "接收者id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String receiverId;

    @Schema(description = "消息类型(加入候选人TALENT、加入人才池PORTRAIT，报告生成REPORT" +
            "，系统消息SYSTEM、申请查看视频APPLY_VIDEO，交换微信VX_EXCHANGE，交换电话号PHONE_EXCHANGE)", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("biz_sence")
    private String bizSence;

    @Schema(description = "来源业务id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sourceId;

    @Schema(description = "发送者类型")
    private String senderType;

}
