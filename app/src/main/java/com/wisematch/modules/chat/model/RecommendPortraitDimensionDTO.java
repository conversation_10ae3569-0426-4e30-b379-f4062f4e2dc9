package com.wisematch.modules.chat.model;

import lombok.Data;

import java.util.List;

@Data
public class RecommendPortraitDimensionDTO {

    /**
     * 用户唯一标识
     */
    private String userId;

    /**
     * 房间ID
     */
    private String roomId;

    /**
     * 删除状态
     */
    private Integer delStatus;

    /**
     * 用户状态
     */
    private Integer status;

    /**
     * 画像状态
     * 对应Milvus Int32类型
     */
    private Integer portraitStatus;

    /**
     * 画像审核状态
     */
    private Integer portraitVerifyStatus;


    /**
     * 岗位胜任力模型关联ID
     */
    private String examineId;

    /**
     * 考核维度
     */
    private String dimensions;

    /**
     * 维度分数
     */
    private Integer dimensionScore;

    /**
     * 文本类型（如：优点/缺点）
     */
    private String textType;

    /**
     * 原始文本内容
     */
    private String textContent;

    /**
     * 人才画像向量（维度为768的浮点型向量）
     */
    private List<Double> portraitVector;
}
