package com.wisematch.modules.chat.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class AESCBCUtil {

    private static final String CHARSET = "UTF-8";
    private static final String AES_CIPHER = "AES/CBC/PKCS5Padding";

    public static String encrypt(String content, String key, String iv) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(CHARSET), "AES");
        IvParameterSpec ivParam = new IvParameterSpec(iv.getBytes(CHARSET));
        Cipher cipher = Cipher.getInstance(AES_CIPHER);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivParam);
        byte[] encrypted = cipher.doFinal(content.getBytes(CHARSET));
        return bytesToHex(encrypted); // ⬅️ 返回十六进制字符串
    }

    public static String decrypt(String encryptedHex, String key, String iv) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(CHARSET), "AES");
        IvParameterSpec ivParam = new IvParameterSpec(iv.getBytes(CHARSET));
        Cipher cipher = Cipher.getInstance(AES_CIPHER);
        cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParam);
        byte[] decodedBytes = hexToBytes(encryptedHex); // ⬅️ 从十六进制解析
        byte[] original = cipher.doFinal(decodedBytes);
        return new String(original, CHARSET);
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder hex = new StringBuilder();
        for (byte b : bytes) {
            String part = Integer.toHexString(0xFF & b);
            if (part.length() == 1) hex.append('0');
            hex.append(part);
        }
        return hex.toString();
    }

    private static byte[] hexToBytes(String hex) {
        int len = hex.length();
        byte[] result = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            result[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return result;
    }

}

