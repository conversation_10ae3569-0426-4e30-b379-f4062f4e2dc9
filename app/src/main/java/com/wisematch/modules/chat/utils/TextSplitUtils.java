package com.wisematch.modules.chat.utils;

import com.wisematch.modules.chat.agent.AiGeekFacade;
import com.wisematch.modules.chat.agent.AiPositionFacade;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * 文本拆分工具类，处理长文本拆分逻辑
 */
@Component
public class TextSplitUtils {

    @Resource
    private AiPositionFacade aiPositionFacade;

    @Resource
    private AiGeekFacade aiGeekFacade;


    public List<String> splitPosition(String text) {
        return aiPositionFacade.split(text);
    }

    public List<String> splitGeek(String text) {
        return aiGeekFacade.split(text);
    }

}