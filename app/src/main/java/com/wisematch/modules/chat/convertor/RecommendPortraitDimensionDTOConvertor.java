package com.wisematch.modules.chat.convertor;

import com.wisematch.modules.chat.entity.AiViewPortrait;
import com.wisematch.modules.chat.model.GeekTextWithType;
import com.wisematch.modules.chat.model.RecommendPortraitDimensionDTO;
import com.wisematch.modules.sys.entity.SysUser;
import org.springframework.util.StringUtils;

import java.util.List;

public class RecommendPortraitDimensionDTOConvertor {

    public static RecommendPortraitDimensionDTO convert(String dimensions, Integer dimensionScore, List<Double> embeddingContent, GeekTextWithType textWithType, SysUser sysUser, String examineId, String roomId, AiViewPortrait aiViewPortrait) {
        RecommendPortraitDimensionDTO dto = new RecommendPortraitDimensionDTO();
        dto.setUserId(sysUser.getUserId());
        dto.setRoomId(roomId);
        dto.setDelStatus(sysUser.getIsDel());
        dto.setStatus(sysUser.getStatus());
        dto.setPortraitStatus(aiViewPortrait.getStatus());
        dto.setPortraitVerifyStatus(aiViewPortrait.getVerifyStatus());
        dto.setExamineId(examineId);

        dto.setDimensions(dimensions);
        dto.setDimensionScore(dimensionScore);

        // 设置文本内容和类型
        String text = textWithType.getText();
        if (StringUtils.hasLength(text) && text.length() > 100) {
            // 如果文本超过100字，截取前100字
            text = text.substring(0, 100);
        }
        dto.setTextContent(text);
        dto.setTextType(textWithType.getType().getType());

        // 转换向量类型
        dto.setPortraitVector(embeddingContent);
        return dto;
    }
}
