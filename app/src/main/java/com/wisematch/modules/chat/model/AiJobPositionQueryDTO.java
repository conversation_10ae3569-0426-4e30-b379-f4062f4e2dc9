package com.wisematch.modules.chat.model;

import com.wisematch.common.model.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AiJobPositionQueryDTO  implements PageRequest {

    @Schema(description = "岗位名称")
    private String position;

    @Schema(description = "智能体Id")
    private String agentId;

    @Schema(description = "考核维度ID")
    private String jobExamineId;

    @Schema(description = "面试官Id")
    private String jobViewerId;

    @Schema(description = "岗位状态（0未发布，1已发布，2停用）")
    private Integer positionStatus;

    @Schema(description = "创建用户Id")
    private String userId;

    @Schema(description = "组织机构Id")
    private String orgId;

    @Schema(description = "起始页")
    private Integer pageNum = 1;

    @Schema(description = "页面大小")
    private Integer pageSize = 10;

    private Boolean sortCreatedTime = false;

    private Boolean sortMaxSalary = false;

    @Schema(description = "招聘类型，全职，兼职，必填")
    private String type;
}
