package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiChatWiser;
import com.wisematch.modules.chat.model.*;
import reactor.core.publisher.Flux;

import java.util.List;


/**
 * AI面试服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */
public interface AiChatWiserService extends IService<AiChatWiser> {

   AiChatWiserVO initChat(ChatInitParams chatInitParams);

   HomeWiser index(String userId);

   Flux<ChatMessage> stream(AiChatUserMsg aiChatUserMsg);

   boolean stopStream(String msgId);

   void messagesWrapper(String conversationId, List<ChatMessage> messages);

   List<ChatMessage> messages(String chatId);

   List<AiChatRecord> chatRecords(String userId);

   Page<AiChatRecord> historyPage(HistoryPageDTO historyPageDTO);

   AiChatWiser updateWiser(AiChatWiser wiser);

   AiChatWiser getByConservationId(String conservationId);

   void deleteChatRecords(String chatId);

   void batchLogicDelete(List<String> ids);

}
