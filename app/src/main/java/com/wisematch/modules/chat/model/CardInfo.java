package com.wisematch.modules.chat.model;

import com.alibaba.fastjson.JSONObject;
import com.wisematch.modules.chat.entity.AiJobPosition;
import com.wisematch.modules.chat.entity.AiJobTrain;
import com.wisematch.modules.chat.enums.CardType;
import com.wisematch.modules.common.utils.JsonUtils;
import com.wisematch.modules.exam.entity.AiExamBank;
import com.wisematch.modules.exam.entity.AiExamQuestionRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 卡片信息
 * <AUTHOR>
 * @version CardInfo.java, v0.1 2025-07-01 23:30
 */
@Data
public class CardInfo {

    String id;
    /**
     * 卡片标题
     */
    @Schema(description = "卡片标题")
    String title;

    /**
     * logo
     */
    @Schema(description = "logo")
    String logo;

    /**
     * 卡片类型，CardInfoEnum
     */
    @Schema(description = "卡片类型，position/train/report/portrait")
    String cardType;

    /**
     * 状态
     */
    @Schema(description = "状态")
    String status;

    /**
     * 业务状态
     */
    @Schema(description = "业务状态")
    String bizStatus;


    @Schema(description = "额外信息")
    //不要去掉初始化，去掉之后有些地方会报错
    JSONObject extra = new JSONObject();


    @Schema(description = "状态码")
    String code;

    /**
     * 简介
     */
    @Schema(description = "简介")
    String summary;

    /**
     * 关键标签
     */
    @Schema(description = "关键标签")
    String keyLabel;

    /**
     * 标签
     */
    @Schema(description = "标签")
    String labels;

    /**
     * agentId
     */
    @Schema(description = "agentId")
    String agentId;

    /**
     * 面试官Id
     */
    @Schema(description = "面试官Id")
    String viewId;

    /**
     * 用户Id
     */
    @Schema(description = "用户Id")
    String userId;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称")
    String company = "WiseMatch";

    /**
     * 日期
     */
    @Schema(description = "日期")
    String datetime;

    public static CardInfo questionRecordToCard(AiExamQuestionRecord source){
        CardInfo dest = new CardInfo();
        dest.setTitle(source.getQuestionName());
        dest.setSummary(source.getOptions());
        dest.setKeyLabel(String.valueOf(source.getSequence()));
        dest.setCardType(CardType.MBTI.name());
        dest.setStatus(String.valueOf(source.getStatus()));
        dest.setId(String.valueOf(source.getQuestionId()));
        JSONObject extra = dest.getExtra();
        extra.put("userAnswer", source.getUserAnswer());
        dest.setExtra(extra);
        return dest;
    }

    public static CardInfo jobTrainToCard(AiJobTrain source){
        CardInfo dest = new CardInfo();
        // 手动设置不同名属性
        dest.setTitle(source.getPosition());
        dest.setKeyLabel(source.getKeyLabel());
        dest.setLabels(source.getLabels());
        if (source.getIsNewest() > 0) {
            dest.setLabels(dest.getLabels() + "|最新");
        } else if (source.getIsHot() > 0) {
            dest.setLabels(dest.getLabels() + "|热门");
        }
        if (source.getType() == 0) {
            dest.setLabels(dest.getLabels() + "|模拟");
        } else {
            dest.setLabels(dest.getLabels() + "|专项");
        }
        dest.setCardType(CardType.INTERVIEW.name());
        dest.setLogo(source.getLogo());
        dest.setStatus(String.valueOf(source.getStatus()));
        dest.setSummary(source.getSummary());
        dest.setId(source.getId());
        dest.setAgentId(source.getAgentId());
        dest.setViewId(source.getJobViewerId());
        dest.setExtra(JSONObject.parseObject(JSONObject.toJSONString(source)));
        return dest;
    }


    public static CardInfo positionToCardInfo(AiJobPosition aiJobPosition) {
        CardInfo cardInfo = new CardInfo();
        cardInfo.setTitle(aiJobPosition.getPosition());
        cardInfo.setKeyLabel(aiJobPosition.getPayArea());
        cardInfo.setLabels(aiJobPosition.getLabels());
        if (aiJobPosition.getIsNewest() > 0) {
            cardInfo.setStatus("最新");
        } else if (aiJobPosition.getIsHot() > 0) {
            cardInfo.setStatus("热门");
        }
        cardInfo.setSummary(aiJobPosition.getSummary());
        cardInfo.setCardType(CardType.POSITION.name());
        cardInfo.setId(aiJobPosition.getId());
        cardInfo.setExtra(JSONObject.parseObject(JSONObject.toJSONString(aiJobPosition)));
        return cardInfo;
    }

    public static CardInfo mbtiReportToCard(MbtiInfo mbtiInfo){
        CardInfo cardInfo = new CardInfo();
        cardInfo.setTitle(mbtiInfo.getName());
        cardInfo.setKeyLabel(mbtiInfo.getComments());
        cardInfo.setSummary(JsonUtils.toJson(mbtiInfo.getList()));
        JSONObject extra = cardInfo.getExtra();
        extra.put("mbti", mbtiInfo.getMbti());
        cardInfo.setExtra(extra);
        return cardInfo;
    }


    public static CardInfo examBankToCardInfo(AiExamBank source) {

        CardInfo dest = new CardInfo();
        // 手动设置不同名属性
        dest.setTitle(source.getName());
        dest.setKeyLabel(source.getExamType());
        dest.setLogo(source.getLogo());
        dest.setSummary(source.getSummary());
        dest.setLabels(source.getLabels());
        dest.setCardType(CardType.MBTI.name());
        dest.setId(String.valueOf(source.getId()));
        return dest;
    }

}
