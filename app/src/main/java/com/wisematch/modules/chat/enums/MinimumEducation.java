package com.wisematch.modules.chat.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 最低学历枚举（原学历要求）
 */
@Getter
public enum MinimumEducation {
    // 不限
    UNLIMITED(0, "学历不限"),
    // 初中及以下
    JUNIOR_HIGH_OR_BELOW(1, "初中及以下"),
    // 中专/中技
    TECHNICAL_SECONDARY(2, "中专/中技"),
    // 高中
    SENIOR_HIGH(3, "高中"),
    // 大专
    JUNIOR_COLLEGE(4, "大专"),
    // 本科
    BACHELOR(5, "本科"),
    // 硕士
    MASTER(6, "硕士"),
    // 博士
    DOCTOR(7, "博士");

    private final int code;
    private final String name;

    MinimumEducation(int code, String name) {
        this.code = code;
        this.name = name;
    }

    // 根据code获取枚举实例
    public static MinimumEducation getByCode(int code) {
        for (MinimumEducation education : values()) {
            if (education.code == code) {
                return education;
            }
        }
        throw new IllegalArgumentException("无效的最低学历code：" + code);
    }


    // 根据name获取枚举实例（便于查询转换）
    public static MinimumEducation getByName(String name) {
        for (MinimumEducation type : values()) {
            if (Objects.equals(name, type.getName())) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的最低学历name：" + name);
    }
}