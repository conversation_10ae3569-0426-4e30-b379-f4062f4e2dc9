package com.wisematch.modules.chat.handler.viewer;

import com.alibaba.fastjson.JSONObject;
import com.wisematch.common.utils.SpringContextUtils;
import com.wisematch.common.utils.ThreadPoolUtil;
import com.wisematch.modules.chat.model.ChatReplyMsg;
import com.wisematch.modules.chat.service.AiChatMemoryService;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

/**
 * 记忆存储
 * <AUTHOR>
 * @version IAgentHandler.java, v0.1 2025-07-15 16:42
 */
@Component
@Slf4j
public class ViewMemoryAgentHandler implements IViewAgentHandler {

    IViewAgentHandler iViewAgentHandler;
    @Autowired
    private AiChatMemoryService aiChatMemoryService;

    @Override
    public void setNext(IViewAgentHandler iAgentHandler) {
        this.iViewAgentHandler = iAgentHandler;
    }
    @Override
    public Flux<ChatReplyMsg> handle(ViewHandlerContext context) {
        log.info("interview add memory start, chatId: {}", context.getRoomId());

        this.aiChatMemoryService.addUserAsync(context.getRoomId(), context.getUserMsg(), context.getAiViewRecord().getAskIndex());

        return iViewAgentHandler.handle(context);
    }

}
