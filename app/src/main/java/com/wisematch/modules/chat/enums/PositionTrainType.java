package com.wisematch.modules.chat.enums;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
public enum PositionTrainType {
    SPECIALIZED_TRAINING(1, "专项实训"),
    MOCK_INTERVIEW(0, "模拟面试"),
    ;

    private Integer code ;

    private String description;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
