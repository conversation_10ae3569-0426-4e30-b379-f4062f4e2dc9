package com.wisematch.modules.chat.utils;// This file is auto-generated, don't edit it. Thanks.

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.cloudauth20221125.AsyncClient;
import com.aliyun.sdk.service.cloudauth20221125.models.EntElementVerifyV2Request;
import com.aliyun.sdk.service.cloudauth20221125.models.EntElementVerifyV2Response;
import com.aliyun.sdk.service.cloudauth20221125.models.EntElementVerifyV2ResponseBody;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import darabonba.core.client.ClientOverrideConfiguration;

import java.util.concurrent.CompletableFuture;

public class EntElementVerifyV2 {
    public static void main(String[] args) throws Exception {

        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId("LTAI5tC3Ze5R54rZ22ohvNMY")
                .accessKeySecret("******************************")
                .build());

        // Configure the Client
        AsyncClient client = AsyncClient.builder()
                .region("cn-hangzhou")
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                .setEndpointOverride("cloudauth.aliyuncs.com")
                )
                .build();

        EntElementVerifyV2Request entElementVerifyV2Request = EntElementVerifyV2Request.builder()
                .userAuthorization("1")
                .sceneCode("orgVerify")
                .merchantBizId("orgVerify_osadqwer")
                .merchantUserId("mch_osadqwer")
                .infoVerifyType("ENT_4META")
                .licenseNo("91330109MAEL973B99")
                .legalPersonName("冯迪洋")
                .entName("杭州青麟栖云科技有限公司")
                .legalPersonCertNo("330921198910011547")
                .build();

        CompletableFuture<EntElementVerifyV2Response> response = client.entElementVerifyV2(entElementVerifyV2Request);

        EntElementVerifyV2Response resp = response.get();

        client.close();


        if(200 != resp.getStatusCode()){
            throw new RRException(RRExceptionEnum.SERVICE_UNAVAILABLE);
        }
        EntElementVerifyV2ResponseBody body = resp.getBody();
//        System.out.println(new Gson().toJson(body));
        String bizCode = body.getResult().getBizCode();
        String status = body.getResult().getStatus();
        if("1".equals(bizCode)&& "1".equals(status)){
            // todo 认证成功
        }

    }

}