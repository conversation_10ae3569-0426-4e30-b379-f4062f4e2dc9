package com.wisematch.modules.chat.convertor;

import com.wisematch.common.utils.TimeUtils;
import com.wisematch.modules.chat.entity.AiViewPortrait;
import com.wisematch.modules.chat.model.GeekTextWithType;
import com.wisematch.modules.chat.model.RecommendWorkExperiencesDTO;
import com.wisematch.modules.chat.model.ResumeDTO;
import com.wisematch.modules.sys.entity.SysUser;
import org.springframework.util.StringUtils;

import java.util.List;

public class RecommendWorkExperiencesDTOConvertor {


    public static RecommendWorkExperiencesDTO convert(ResumeDTO.WorkExperience workExperience, List<Double> embeddingContent, GeekTextWithType textWithType, SysUser sysUser, String examineId, String roomId, AiViewPortrait aiViewPortrait) {
        RecommendWorkExperiencesDTO dto = new RecommendWorkExperiencesDTO();
        dto.setUserId(sysUser.getUserId());
        dto.setRoomId(roomId);
        dto.setDelStatus(sysUser.getIsDel());
        dto.setStatus(sysUser.getStatus());
        dto.setPortraitStatus(aiViewPortrait.getStatus());
        dto.setPortraitVerifyStatus(aiViewPortrait.getVerifyStatus());
        dto.setExamineId(examineId);

        // 处理开始时间 - 转换为时间戳
        String startDate = workExperience.getStartDate();
        if (StringUtils.hasLength(startDate)) {
            Long startTimestamp = TimeUtils.convertDateToTimestamp(startDate);
            dto.setStartDate(startTimestamp);
        }

        // 处理结束时间 - 转换为时间戳
        String endDate = workExperience.getEndDate();
        if (StringUtils.hasLength(endDate)) {
            Long endTimestamp = TimeUtils.convertDateToTimestamp(endDate);
            dto.setEndDate(endTimestamp);
        }

        // 设置文本内容和类型
        String text = textWithType.getText();
        if (StringUtils.hasLength(text) && text.length() > 100) {
            // 如果文本超过100字，截取前100字
            text = text.substring(0, 100);
        }
        dto.setTextContent(text);
        dto.setTextType(textWithType.getType().getType());

        // 转换向量类型
        dto.setWorkVector(embeddingContent);
        return dto;
    }
}
