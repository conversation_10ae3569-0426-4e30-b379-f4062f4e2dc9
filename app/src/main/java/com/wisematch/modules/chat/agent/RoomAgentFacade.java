package com.wisematch.modules.chat.agent;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wisematch.common.cache.RedisCache;
import com.wisematch.common.lock.RedissonLock;
import com.wisematch.common.oss.FileAcl;
import com.wisematch.common.oss.OssFacade;
import com.wisematch.common.utils.InterviewThreadPoolUtil;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.config.AiChatConfig;
import com.wisematch.modules.chat.config.AiChatStartConfig;
import com.wisematch.modules.chat.config.AiChatStopConfig;
import com.wisematch.modules.chat.entity.AiJobPosition;
import com.wisematch.modules.chat.entity.AiJobViewer;
import com.wisematch.modules.chat.entity.AiViewRecord;
import com.wisematch.modules.chat.enums.ApplyPositionType;
import com.wisematch.modules.chat.enums.ApplyType;
import com.wisematch.modules.chat.enums.InterviewConstant;
import com.wisematch.modules.chat.enums.InterviewStatus;
import com.wisematch.modules.chat.executor.StartVideoRecordExecutor;
import com.wisematch.modules.chat.model.StartAgentParam;
import com.wisematch.modules.chat.model.StopAgentParam;
import com.wisematch.modules.chat.model.UpdateAgentParam;
import com.wisematch.modules.chat.service.*;
import com.wisematch.modules.chat.utils.VolcSignUtil;
import com.wisematch.modules.common.service.AiSysConfigService;
import com.wisematch.modules.job.entity.UserTaskSchedule;
import com.wisematch.modules.job.enums.TaskStatusEnum;
import com.wisematch.modules.job.enums.TaskTypeEnum;
import com.wisematch.modules.job.mapper.UserTaskScheduleMapper;
import com.wisematch.modules.rtc.RecordResp;
import com.wisematch.modules.rtc.RtcService;
import com.wisematch.modules.rtc.impl.RtcServiceImpl;
import com.wisematch.modules.rtc.model.StopRecordRequest;
import com.wisematch.modules.rtc.model.StopRecordResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ThreadUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

/**
 * 火山音视频智能体
 * <AUTHOR>
 * @version RoomAgentFacade.java, v0.1 2025-07-12 23:11
 */
@Component
@Slf4j
public class RoomAgentFacade {

    @Autowired
    private AiChatConfig aiChatConfig;
    @Autowired
    private AiAgentPoolService aiAgentPoolService;
    @Autowired
    private AiViewRecordService aiViewRecordService;
    @Autowired
    private AiChatMemoryService aiChatMemoryService;
    @Autowired
    private OssFacade ossFacade;
    @Autowired
    private AiViewReportService aiViewReportService;
    @Autowired
    private AiJobViewerService aiJobViewerService;
    @Autowired
    private AiJobPositionService aiJobPositionService;
    @Autowired
    private RedissonLock redisLock;
    @Autowired
    private AiSysConfigService aiSysConfigService;
    @Autowired
    private StartVideoRecordExecutor startVideoRecordExecutor;

    @Resource
    private RedisCache redisCache;

    @Resource
    private UserTaskScheduleMapper userTaskScheduleMapper;

    public boolean startAgent(StartAgentParam agentParam){
        try {
            AiChatStartConfig aiChatStartConfig = new AiChatStartConfig();
            aiChatStartConfig.setAppId(aiChatConfig.getAppId());
            aiChatStartConfig.setRoomId(agentParam.getRoomId());
            aiChatStartConfig.setTaskId(agentParam.getTaskId());

            JSONObject custom = new JSONObject();
            custom.put("roomId", agentParam.getRoomId());
            custom.put("agentId", agentParam.getAgentId());

            aiChatStartConfig.getConfig().getLLMConfig().setCustom(custom.toJSONString());
            aiChatStartConfig.getConfig().getLLMConfig().setURL(aiChatConfig.getLlmUrl());
            aiChatStartConfig.getAgentConfig().getTargetUserId().add(UserInfoUtils.getCurrentUserId());

            AiViewRecord aiViewRecord = aiViewRecordService.getByRoomId(agentParam.getRoomId());
            AiJobViewer aiJobViewer = aiJobViewerService.getById(aiViewRecord.getViewerId());
            //面试官设置
            aiChatStartConfig.getAgentConfig().setWelcomeMessage(agentParam.getWelcome());
            if (!StringUtils.isEmpty(aiJobViewer.getVoice())) {
                aiChatStartConfig.getConfig().getTTSConfig().getProviderParams().getJSONObject("audio").put("voice_type", aiJobViewer.getVoice());
            } else {
                if (StringUtils.isNotEmpty(agentParam.getAgentVoice())) {
                    aiChatStartConfig.getConfig().getTTSConfig().getProviderParams().getJSONObject("audio").put("voice_type", agentParam.getAgentVoice());
                }
            }
            JSONObject params = JSONObject.parseObject(aiSysConfigService.getByKey("view_room_param").getConfigValue());
            if (params.getInteger("SilenceTime") != null) {
                log.info("view SilenceTime set:{}", params.getInteger("SilenceTime"));
                aiChatStartConfig.getConfig().getASRConfig().getVADConfig().put("SilenceTime", params.getInteger("SilenceTime"));
            }
            if (params.getInteger("InterruptSpeechDuration") != null) {
                log.info("view InterruptSpeechDuration set:{}", params.getInteger("InterruptSpeechDuration"));
                aiChatStartConfig.getConfig().getASRConfig().getInterruptConfig().put("InterruptSpeechDuration", params.getInteger("InterruptSpeechDuration"));
            }
            log.info("start agent param:{}", JSONObject.toJSONString(aiChatStartConfig));
            String action = "StartVoiceChat";
            String version = "2024-12-01";
            String region = "cn-north-1";
            String service = "rtc";
            // 请求地址
            String endpoint = "rtc.volcengineapi.com";
            String path = "/"; // 路径，不包含 Query// 请求接口信息
            String schema = "https";
            VolcSignUtil sign = new VolcSignUtil(region, service, schema, endpoint, path, aiChatConfig.getAccessKeyId(), aiChatConfig.getSecretAccessKey());

            String body = JSONObject.toJSONString(aiChatStartConfig);
            Date date = new Date();
            HashMap<String, String> queryMap = new HashMap<>();

            String response = sign.doRequest("POST", queryMap, body.getBytes(), date, action, version);
            aiViewRecord.setBizResponse(response);
            JSONObject result = JSONObject.parseObject(response);
            if("ok".equals(result.getString("Result"))){
                //启动云端录制
                startVideoRecordExecutor.executeAsync(agentParam.getUserId(),agentParam.getRoomId(),agentParam.getPlatform());
                //开场白
                aiChatMemoryService.addAssistantAsync(aiViewRecord.getRoomId(),aiViewRecord.getWelcome(),aiViewRecord.getAskIndex());

                aiViewRecord.setRoomStatus(InterviewConstant.ROOM_OPEN);
                aiViewRecordService.updateById(aiViewRecord);
                return true;
            }else {
                aiViewRecord.setRoomStatus(InterviewConstant.ROOM_FAIL);
                aiViewRecordService.updateById(aiViewRecord);
                return false;
            }
        } catch (Exception e) {
            log.error("start agent step2 error", e);
        }
        return false;
    }

    /**
     * 停止智能体
     * @param agentParam
     * @return
     */
    public boolean stopAgent(StopAgentParam agentParam) {
        String roomId = agentParam.getRoomId();
        String lockKey = "stop_agent_" + roomId;
        String existKey = "stopped_agent_" + roomId;
        // 使用分布式锁防止同一个房间的并发停止操作
        if (redisCache.hasKey(existKey)) {
            log.info("房间已处理，roomId: {}", roomId);
            return true;
        }

        if (!redisLock.lock(lockKey, 5000, TimeUnit.MILLISECONDS)) {
            log.info("获取停止Agent锁失败，可能已有其他进程在处理，roomId: {}", roomId);
            return true;
        }

        try {
            // 获取锁后再次检查existKey，防止并发情况下的重复处理
            if (redisCache.hasKey(existKey)) {
                log.info("获取锁后发现房间已处理，roomId: {}", roomId);
                return true;
            }

            AiChatStopConfig aiChatStartConfig = new AiChatStopConfig();
            aiChatStartConfig.setAppId(aiChatConfig.getAppId());
            aiChatStartConfig.setRoomId(roomId);
            aiChatStartConfig.setTaskId(agentParam.getTaskId());
            String action = "StopVoiceChat";
            String version = "2024-12-01";
            String region = "cn-north-1";
            String service = "rtc";
            // 请求地址
            String endpoint = "rtc.volcengineapi.com";
            String path = "/"; // 路径，不包含 Query// 请求接口信息
            String schema = "https";
            VolcSignUtil sign = new VolcSignUtil(region, service, schema, endpoint, path, aiChatConfig.getAccessKeyId(), aiChatConfig.getSecretAccessKey());

            String body = JSONObject.toJSONString(aiChatStartConfig);
            Date date = new Date();
            HashMap<String, String> queryMap = new HashMap<>();

            String response = sign.doRequest("POST", queryMap, body.getBytes(), date, action, version);
            AiViewRecord aiViewRecord = aiViewRecordService.getByRoomId(roomId);
            aiViewRecord.setBizResponse(response);
            JSONObject result = JSONObject.parseObject(response);
            if ("ok".equals(result.getString("Result"))) {
                aiViewRecord.setRoomStatus(InterviewConstant.ROOM_CLOSED);
            }
            aiViewRecordService.updateById(aiViewRecord);
            //异步生成报告
            JSONObject params = aiSysConfigService.getInterviewConfig();
            long miniDuration = params.getInteger(InterviewConstant.MINI_DURATION);
            if (aiViewRecord.getDuration() >= miniDuration) {
                insertUserSchedule(roomId);
            }
            //设置一个标志位，表示已经停止
            redisCache.put(existKey, "stop", 10L);
            log.info("stop agent response:{}", response);
        } catch (Exception e) {
            log.error("start agent step2 error", e);
        } finally {
            // 释放锁
            redisLock.unlock(lockKey);
        }
        return true;
    }

    private void insertUserSchedule(String roomId) {






        JSONObject sysConfig = aiSysConfigService.getSysConfig();
        Integer portraitLimitTry = sysConfig.getInteger("portrait_limit_try");
        UserTaskSchedule userTaskSchedule = new UserTaskSchedule();
        userTaskSchedule.setRoomId(roomId);
        userTaskSchedule.setTaskType(TaskTypeEnum.REPORT_PORTRAIT.getCode());
        userTaskSchedule.setMaxScheduleCount(portraitLimitTry);
        userTaskSchedule.setCurrentScheduleCount(0);
        userTaskSchedule.setStatus(TaskStatusEnum.ENABLE.getCode());
        userTaskScheduleMapper.insert(userTaskSchedule);
    }

    public boolean updateAgent(UpdateAgentParam updateAgentParam) {
        log.info("智能体消息提醒:{}", updateAgentParam);
        try {
            updateAgentParam.setAppId(aiChatConfig.getAppId());
            String action = "UpdateVoiceChat";
            String version = "2024-12-01";
            String region = "cn-north-1";
            String service = "rtc";
            // 请求地址
            String endpoint = "rtc.volcengineapi.com";
            String path = "/"; // 路径，不包含 Query// 请求接口信息
            String schema = "https";
            VolcSignUtil sign = new VolcSignUtil(region, service, schema, endpoint, path, aiChatConfig.getAccessKeyId(), aiChatConfig.getSecretAccessKey());

            String body = JSONObject.toJSONString(updateAgentParam);
            Date date = new Date();
            HashMap<String, String> queryMap = new HashMap<>();

            String response = sign.doRequest("POST", queryMap, body.getBytes(), date, action, version);

            log.info("update agent response:{}", response);
        } catch (Exception e) {
            log.error("update agent error", e);
        }
        return true;
    }

    /**
     * 解散房间
     * @param agentParam
     * @return
     */
    public boolean banRoom(StopAgentParam agentParam) {
        try {
            AiChatStartConfig aiChatStartConfig = new AiChatStartConfig();
            aiChatStartConfig.setAppId(aiChatConfig.getAppId());
            aiChatStartConfig.setRoomId(agentParam.getRoomId());
            aiChatStartConfig.setTaskId(null);
            aiChatStartConfig.setConfig(null);
            aiChatStartConfig.setAgentConfig(null);
            String action = "BanRoomUser";
            String version = "2023-11-01";
            String region = "cn-north-1";
            String service = "rtc";
            // 请求地址
            String endpoint = "rtc.volcengineapi.com";
            String path = "/"; // 路径，不包含 Query// 请求接口信息
            String schema = "https";
            VolcSignUtil sign = new VolcSignUtil(region, service, schema, endpoint, path, aiChatConfig.getAccessKeyId(), aiChatConfig.getSecretAccessKey());

            String body = JSONObject.toJSONString(aiChatStartConfig);
            Date date = new Date();
            HashMap<String, String> queryMap = new HashMap<>();

            String response = sign.doRequest("POST", queryMap, body.getBytes(), date, action, version);

            log.info("ban room response:{}", response);

        } catch (Exception e) {
            log.error("ban room error", e);
        }

        return this.stopAgent(agentParam);
    }

    /**
     * 获取通知信息
     * @param roomId
     * @return
     */
    public String getRoomNotice(String roomId) {
        String key = roomId + "_notice_count";
        Integer noticeCount = (Integer) redisCache.get(key);
        if (noticeCount == null) {
            noticeCount = 0;
        }
        noticeCount++;
        redisCache.put(key, noticeCount);
        if (noticeCount < 3) {
            return "听得到我说话吗？";
        } else if (noticeCount == 3) {
            return "你还在吗？不在的话这边结束面试了";
        } else {
            InterviewThreadPoolUtil.supplyAsync(() -> this.stopRoomAgent(roomId));
            redisCache.remove(key);
            return "面试结束，请关闭页面";
        }
    }

    /**
     * 清空通知次数
     *
     * @param roomId
     */
    public void clearRoomNotice(String roomId) {
        String key = roomId + "_notice_count";
        redisCache.put(key, 0);
    }

    public boolean startRoomAgent(StartAgentParam agentParam) {
        //查询数据
        try {
//            if (!redisCache.getLocked("Start_"+agentParam.getRoomId())) {
//                return true;
//            }
            log.info("start agent. roomId: {}, locked: {}", agentParam.getRoomId(), redisCache.getLocked("Start_" + agentParam.getRoomId()));
            AiViewRecord aiViewRecord = aiViewRecordService.getByRoomId(agentParam.getRoomId());
            if (aiViewRecord == null) {
                return false;
            }
            if (!InterviewStatus.INIT.name().equals(aiViewRecord.getStatus())) {
                return false;
            }
            aiViewRecord.setStatus(InterviewStatus.CHATTING.name());
            aiViewRecord.setStartTime(new Date());
            aiViewRecord.setUpdateTime(new Date());
            aiViewRecord.setUserAgent(agentParam.getUserAgent());
            this.aiViewRecordService.updateById(aiViewRecord);
            //获取平台来源
            agentParam.setPlatform(aiViewRecord.getPlatform());
            agentParam.setWelcome(aiViewRecord.getWelcome());
        } catch (Exception e) {
            log.error("start agent error", e);
        }
        return this.startAgent(agentParam);
    }

    /**
     * 停止录像
     *
     * @param aiViewRecord
     */
    public void stopRecord(AiViewRecord aiViewRecord){
        this.stopRecord(aiViewRecord.getRoomId());
    }

    /**
     * 解散房间
     * @param roomId
     * @return
     */
    public boolean stopRoomAgent(String roomId){
        StopAgentParam stopAgentParam = new StopAgentParam();
        stopAgentParam.setRoomId(roomId);
        return this.stopRoomAgent(stopAgentParam);
    }

    /**
     * 解散房间
     * @param agentParam
     * @return
     */
    public boolean stopRoomAgent(StopAgentParam agentParam) {

        //延迟5秒后解散房间
        try {
            ThreadUtils.sleep(Duration.ofSeconds(5));
        } catch (InterruptedException e) {
        }
        log.info("stop agent. roomId: {}, locked: {}", agentParam.getRoomId(), redisCache.getLocked("Stop_" + agentParam.getRoomId()));
        //更新对话和状态数据
        AiViewRecord aiViewRecord = aiViewRecordService.getByRoomId(agentParam.getRoomId());
        if (aiViewRecord == null
                || InterviewStatus.STOP.name().equals(aiViewRecord.getStatus())
                || InterviewStatus.SUBMITED.name().equals(aiViewRecord.getStatus())) return true;
        try {
            //面试录像停止
            this.stopRecord(aiViewRecord);
        } catch (Exception e) {
            log.error("停止录像异常", e);
        }
        try {
            aiViewRecord.setEndTime(new Date());
            aiViewRecord.setUpdateTime(new Date());
            if (aiViewRecord.getStartTime() == null) {
                aiViewRecord.setStartTime(aiViewRecord.getCreateTime());
            }
            aiViewRecord.setDuration((aiViewRecord.getEndTime().getTime() - aiViewRecord.getStartTime().getTime()) / 1000);
            //判定面试时间是否足够
            JSONObject params = aiSysConfigService.getInterviewConfig();
            long miniDuration = params.getInteger(InterviewConstant.MINI_DURATION);
            if (aiViewRecord.getDuration() < miniDuration) {
                aiViewRecord.setStatus(InterviewStatus.STOP.name());
            } else {
                //岗位投递满足面试时长，跳转待提交表单
                if (ApplyType.POSITION.name().equals(aiViewRecord.getChatType())) {
                    aiViewRecord.setStatus(InterviewStatus.SUBMITTING.name());
                    AiJobPosition aiJobPosition = aiJobPositionService.getById(aiViewRecord.getPositionId());
                    aiViewRecord.setApplyType(ApplyPositionType.FULL.name());
                    if (aiJobPosition.getType().contains("兼职")) {
                        aiViewRecord.setApplyType(ApplyPositionType.PART.name());
                    }
                } else {
                    aiViewRecord.setStatus(InterviewStatus.SUBMITED.name());
                }
            }
            aiViewRecordService.updateById(aiViewRecord);
            log.info("stop agent, roomId = {}", agentParam.getRoomId());
        } catch (Exception e) {
            log.error("stop agent error", e);
        }
        return this.banRoom(agentParam);
    }

    /**
     * 停止云端录制
     * @param roomId
     * @return
     */
    public RecordResp stopRecord(String roomId) {
        RecordResp recordResp = new RecordResp();

        RtcService rtcService = RtcServiceImpl.getInstance();
        rtcService.setAccessKey(aiChatConfig.getAccessKeyId());
        rtcService.setSecretKey(aiChatConfig.getSecretAccessKey());

        try {
            StopRecordRequest stopRecordRequest = new StopRecordRequest();
            stopRecordRequest.setAppId(aiChatConfig.getAppId());
            stopRecordRequest.setBusinessId(roomId);
            stopRecordRequest.setRoomId(roomId);
            stopRecordRequest.setTaskId("video_record");
            StopRecordResponse stopRecordResponse = rtcService.stopRecord(stopRecordRequest);
            log.info("停止录制:{}", JSON.toJSONString(stopRecordResponse));
            if ("ok".equals(stopRecordResponse.getResult())) {
                recordResp.setStatus(2);
                recordResp.setFileKey(ossFacade.getHttpUrl("interview/user/" + roomId + ".mp4", FileAcl.PUBLIC));
                return recordResp;
            }
        } catch (Exception e) {
            log.error("stop record error", e);
        }
        recordResp.setFileKey(ossFacade.getHttpUrl("interview/user/" + roomId + ".mp4", FileAcl.PUBLIC));
        recordResp.setStatus(-1);
        return recordResp;
    }
}
