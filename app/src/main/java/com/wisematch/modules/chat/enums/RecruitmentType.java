package com.wisematch.modules.chat.enums;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 招聘类型枚举
 */
@Getter
@Slf4j
public enum RecruitmentType {
    // 社招全职（默认选项）
    FULL_TIME_SOCIAL(0, "全职"),
    // 兼职招聘
    PART_TIME(3, "兼职");

    // 获取code
    private final int code;
    // 获取描述
    private final String name;

    RecruitmentType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    // 根据code获取枚举实例（便于查询转换）
    public static RecruitmentType getByCode(int code) {
        for (RecruitmentType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的招聘类型code：" + code);
    }


    // 根据name获取枚举实例（便于查询转换）
    public static RecruitmentType getByName(String name) {
        for (RecruitmentType type : values()) {
            if (Objects.equals(name, type.getName())) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的招聘类型name：" + name);
    }
}