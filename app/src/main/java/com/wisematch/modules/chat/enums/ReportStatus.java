package com.wisematch.modules.chat.enums;

import java.util.Objects;

/**
 * 卡片类型
 * <AUTHOR>
 * @version CardType.java, v0.1 2025-07-01 23:45
 */
public enum ReportStatus {

    ING(0,"报告生成中"),

    FAIL(-1,"生成失败"),

    NONE(-2,"暂无报告"),

    SUCCESS(1,"查看报告");

    private final Integer code;
    private final String desc;

    ReportStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public static ReportStatus getStatus(Integer code){
        for (ReportStatus status: ReportStatus.values()) {
            if(Objects.equals(status.code, code)){
                return status;
            }
        }
        return NONE;
    }
}
