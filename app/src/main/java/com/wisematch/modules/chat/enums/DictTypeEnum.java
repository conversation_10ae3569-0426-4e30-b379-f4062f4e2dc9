package com.wisematch.modules.chat.enums;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @version DictTypeEnum.java, v0.1 2025-07-21 15:05
 */
@Schema(title = "字典枚举/下拉菜单")
public enum DictTypeEnum {

    @Schema(title = "岗位信息-工作方式下拉菜单", description = "工作类型：到岗/云办公")
    work_type,
    @Schema(title = "岗位信息-招聘类型下拉菜单", description = "岗位信息-招聘类型下拉菜单")
    job_way,
    @Schema(title = "岗位信息-结算方式下拉菜单", description = "岗位信息-结算方式下拉菜单")
    pay_type,
    @Schema(title = "表单管理-求职期望", description = "表单管理-求职期望")
    job_intention,
    @Schema(title = "表单管理-允许企业查看视频", description = "表单管理-允许企业查看视频")
    shou_video,
    @Schema(title = "表单管理-求职状态", description = "表单管理-求职状态")
    job_status,
    @Schema(title = "表单管理-每周工作天数", description = "表单管理-每周工作天数")
    week_work_time,
    @Schema(title = "表单管理-工作时间", description = "表单管理-工作时间")
    work_time,
    @Schema(title = "表单管理-工作时段", description = "表单管理-工作时段")
    period_time,
    @Schema(title = "表单管理-反馈类型", description = "表单管理-反馈类型")
    feedback_type,
    @Schema(title = "表单管理-薪资要求", description = "表单管理-薪资要求")
    salary_requirements,
    @Schema(title = "表单管理-工作城市", description = "表单管理-工作城市")
    work_city,
    @Schema(title = "工作意向表单-城市", description = "工作意向表单-城市")
    app_letter_city,
    @Schema(title = "表单管理-工作经验", description = "表单管理-工作经验")
    work_experience,
    @Schema(title = "表单管理-期望行业", description = "表单管理-期望行业")
    expected_industry,
    @Schema(title = "表单管理-聊天问题反馈(点赞)", description = "表单管理-聊天问题反馈(点赞)")
    chat_feed_back_good ,
    @Schema(title = "表单管理-聊天问题反馈(点踩)", description = "表单管理-聊天问题反馈(点踩)")
    chat_feed_back_bad ,
    @Schema(title = "表单管理-学历要求", description = "表单管理-学历要求")
    educational ,
    @Schema(title = "《WiseMatch服务协议》", description = "《WiseMatch服务协议》")
    svagmt ,
    @Schema(title = "《WiseMatch隐私协议》", description = "《WiseMatch隐私协议》")
    pvcagmt ,
    @Schema(title = "《个人信息共享清单》", description = "《个人信息共享清单》")
    pisl ,
    @Schema(title = "《个人信息收集清单》", description = "《个人信息收集清单》")
    picl ,
    @Schema(title = "《应用权限说明》", description = "《应用权限说明》")
    apd,
    @Schema(title = "app下载链接", description = "app下载链接")
    app_download,

}
