package com.wisematch.modules.chat.convertor;

import com.wisematch.modules.chat.entity.AiViewPortrait;
import com.wisematch.modules.chat.enums.GeekEducationEnum;
import com.wisematch.modules.chat.enums.RecruitmentType;
import com.wisematch.modules.chat.enums.SexEnum;
import com.wisematch.modules.chat.model.GeekTextWithType;
import com.wisematch.modules.chat.model.RecommendResumeProfilesDTO;
import com.wisematch.modules.chat.model.ResumeDTO;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.model.JobApplication;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.List;


@Slf4j
public class RecommendResumeProfilesDTOConvertor {


    public static RecommendResumeProfilesDTO convert(ResumeDTO resumeDTO, List<Double> embeddingContent, GeekTextWithType textWithType, SysUser sysUser, String examineId, String roomId, JobApplication jobApplication, AiViewPortrait aiViewPortrait) {
        RecommendResumeProfilesDTO dto = new RecommendResumeProfilesDTO();
        dto.setUserId(sysUser.getUserId());
        dto.setRoomId(roomId);
        dto.setDelStatus(sysUser.getIsDel());
        dto.setStatus(sysUser.getStatus());
        dto.setPortraitStatus(aiViewPortrait.getStatus());
        dto.setPortraitVerifyStatus(aiViewPortrait.getVerifyStatus());
        dto.setExamineId(examineId);

        // 处理性别
        Integer sex = resumeDTO.getSex();
        SexEnum sexEnum = SexEnum.getByResumeValue(sex);
        dto.setSex(sexEnum.getDtoValue());

        ResumeDTO.Brief brief = resumeDTO.getBrief();
        boolean isJobAppSalaryValid = false; // 标记jobApplication的薪资是否有效

        // 1. 优先处理jobApplication的信息（工作方式+薪资）
        if (jobApplication != null) {
            // 1.1 处理工作方式（全职/兼职）- 优先级：jobApplication > 简历
            String jobExpectations = jobApplication.getJobExpectations();
            if (StringUtils.hasLength(jobExpectations)) {
                try {
                    RecruitmentType recruitmentType = RecruitmentType.getByName(jobExpectations);
                    dto.setWorkWay(recruitmentType.getCode());
                } catch (IllegalArgumentException e) {
                    log.warn("未找到匹配的工作方式(jobExpectations): {}", jobExpectations);
                    dto.setWorkWay(RecruitmentType.FULL_TIME_SOCIAL.getCode()); // 默认全职
                }
            }

            // 1.2 处理薪资：仅当salaryMinStr和salaryMaxStr都不为空时，才使用jobApplication的薪资
            String salaryMinStr = jobApplication.getSalaryRequirementsStart();
            String salaryMaxStr = jobApplication.getSalaryRequirementsEnd();
            if (StringUtils.hasLength(salaryMinStr) && StringUtils.hasLength(salaryMaxStr)) {
                try {
                    // 解析jobApplication的薪资（单值，如"15K" → [15000,15000]）
                    Integer[] jobMinSalaryArr = extractSalaryRange(salaryMinStr);
                    Integer[] jobMaxSalaryArr = extractSalaryRange(salaryMaxStr);

                    // 确保解析结果有效
                    if (jobMinSalaryArr != null && jobMinSalaryArr.length > 0 && jobMaxSalaryArr != null && jobMaxSalaryArr.length > 0) {
                        dto.setSalaryMin(jobMinSalaryArr[0]);
                        dto.setSalaryMax(jobMaxSalaryArr[0]);
                        isJobAppSalaryValid = true; // 标记jobApplication薪资有效，无需降级
                    }
                } catch (Exception e) {
                    log.warn("解析jobApplication中的薪资信息失败", e);
                }
            }
        }

        // 2. 降级处理：若jobApplication薪资无效，或jobApplication为空，则从简历取信息（工作经验+薪资+工作方式）
        if (brief != null) {
            // 2.1 工作经验：仅从简历获取
            dto.setWorkExperience(brief.getWorkYears());

            // 2.2 薪资：仅当jobApplication薪资无效时，从简历取
            if (!isJobAppSalaryValid) {
                if (StringUtils.hasLength(brief.getSalary())) {
                    Integer[] resumeSalaryRange = extractSalaryRange(brief.getSalary());
                    if (resumeSalaryRange != null && resumeSalaryRange.length == 2) {
                        dto.setSalaryMin(resumeSalaryRange[0]);
                        dto.setSalaryMax(resumeSalaryRange[1]);
                    }
                }
            }

            // 2.3 工作方式：仅当jobApplication未设置工作方式时，从简历取
            if (dto.getWorkWay() == null) {
                if (StringUtils.hasLength(brief.getWorkWay())) {
                    try {
                        RecruitmentType recruitmentType = RecruitmentType.getByName(brief.getWorkWay());
                        dto.setWorkWay(recruitmentType.getCode());
                    } catch (IllegalArgumentException e) {
                        log.warn("未找到匹配的工作方式(brief.getWorkWay): {}", brief.getWorkWay());
                        dto.setWorkWay(RecruitmentType.FULL_TIME_SOCIAL.getCode()); // 默认全职
                    }
                }
            }
        }

        // 3. 学历信息：仅从简历获取
        if (brief != null && StringUtils.hasLength(brief.getEducationalTitle())) {
            try {
                GeekEducationEnum education = GeekEducationEnum.getByName(brief.getEducationalTitle());
                dto.setHighestEducation(education.getCode());
            } catch (IllegalArgumentException e) {
                log.warn("未找到匹配的学历: {}", brief.getEducationalTitle());
                dto.setHighestEducation(GeekEducationEnum.UNKNOWN.getCode()); // 默认为不限
            }
        }

        // 4. 设置文本内容和类型（截取100字限制）
        String text = textWithType.getText();
        if (StringUtils.hasLength(text) && text.length() > 100) {
            text = text.substring(0, 100);
        }
        dto.setTextContent(text);
        dto.setTextType(textWithType.getType().getType());

        // 5. 转换向量类型
        dto.setProfilesVector(embeddingContent);

        return dto;
    }

    /**
     * 提取薪资范围，将K转换为具体数字
     * 支持两种格式：
     * - 范围格式："15K-20K" → [15000, 20000]
     * - 单值格式："15K" / "8000" → [15000, 15000] / [8000, 8000]
     * @param salary 原始薪资字符串
     * @return 薪资范围数组 [最低薪资, 最高薪资]（无效时返回null）
     */
    private static Integer[] extractSalaryRange(String salary) {
        if (!StringUtils.hasLength(salary)) {
            return null;
        }

        try {
            // 处理范围格式（如"15K-20K"）
            if (salary.contains("-")) {
                String[] parts = salary.split("-");
                if (parts.length == 2) {
                    Integer minSalary = convertSalaryPart(parts[0].trim());
                    Integer maxSalary = convertSalaryPart(parts[1].trim());
                    if (minSalary != null && maxSalary != null) {
                        return new Integer[]{minSalary, maxSalary};
                    }
                }
            } else {
                // 处理单值格式（如"15K"、"8000"）
                Integer singleSalary = convertSalaryPart(salary.trim());
                if (singleSalary != null) {
                    return new Integer[]{singleSalary, singleSalary};
                }
            }
        } catch (Exception e) {
            log.warn("薪资解析失败: {}", salary, e);
        }

        return null;
    }

    /**
     * 转换单个薪资片段（如"15K" → 15000，"8000" → 8000）
     * @param salaryPart 单个薪资字符串（无"-"分隔）
     * @return 转换后的薪资数值（无效时返回null）
     */
    private static Integer convertSalaryPart(String salaryPart) {
        if (!StringUtils.hasLength(salaryPart)) {
            return null;
        }

        try {
            // 移除所有空格（避免"15 K"这类异常格式）
            salaryPart = salaryPart.replaceAll("\\s+", "");

            // 处理带K的格式（大小写不敏感，如"15K"、"20k"）
            if (salaryPart.toUpperCase().endsWith("K")) {
                String numberStr = salaryPart.substring(0, salaryPart.length() - 1);
                int number = Integer.parseInt(numberStr);
                return number * 1000;
            } else {
                // 处理纯数字格式（如"8000"）
                return Integer.parseInt(salaryPart);
            }
        } catch (NumberFormatException e) {
            log.warn("薪资数值解析失败: {}", salaryPart);
            return null;
        }
    }

}