package com.wisematch.modules.rtc;


import com.wisematch.modules.rtc.model.*;
import com.volcengine.service.IBaseService;


/**
 * The interface rtc service.
 */
public interface RtcService extends IBaseService {
    StartRecordResponse startRecord(StartRecordRequest startRecordRequest) throws Exception;

    StopRecordResponse stopRecord(StopRecordRequest stopRecordRequest) throws Exception;

    GetRecordTaskResponse getRecordTask(GetRecordTaskRequest getRecordTaskRequest) throws Exception;
}
