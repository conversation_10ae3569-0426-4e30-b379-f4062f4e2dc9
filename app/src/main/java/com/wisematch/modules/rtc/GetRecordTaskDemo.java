package com.wisematch.modules.rtc;

import com.volcengine.service.vod.IVodService;
import com.volcengine.service.vod.impl.VodServiceImpl;

public class GetRecordTaskDemo {
    public static void main(String[] args) {
//        //注意：1.0.33版本之前的getInstance方法不是单例模式，内部存在监控http client的守护线程，多次重复调用可能会产生内存泄露
//        RtcService rtcService = RtcServiceImpl.getInstance();
//
//        rtcService.setAccessKey("AKLTNGUwMzYyODI0ZGQ3NDYzYmEzYmRjMzI5NGYwZmI1OTM");
//        rtcService.setSecretKey("WkRnNE4yVXlaalUzT1dFMU5ESXhabUk1TXpNME1XUmxaVEk1WkROaE9USQ==");
//
////        rtcService.getSessionToken();
//
//
//        try {
//            GetRecordTaskRequest getRecordTaskRequest = new GetRecordTaskRequest();
//            getRecordTaskRequest.setAppId("683927008ac8a9018c2f0a34");
//            getRecordTaskRequest.setRoomId("Your_RoomId");
//            getRecordTaskRequest.setTaskId("Your_TaskId");
//            GetRecordTaskResponse getRecordTaskResponse = rtcService.getRecordTask(getRecordTaskRequest);
//            System.out.println(JSON.toJSONString(getRecordTaskResponse));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }

        IVodService vodService = VodServiceImpl.getInstance();

        // call below method if you dont set ak and sk in ～/.vcloud/config
        vodService.setAccessKey("AKLTNGUwMzYyODI0ZGQ3NDYzYmEzYmRjMzI5NGYwZmI1OTM");
        vodService.setSecretKey("WkRnNE4yVXlaalUzT1dFMU5ESXhabUk1TXpNME1XUmxaVEk1WkROaE9USQ==");

        try {
            String resp = vodService.createSha1HlsDrmAuthToken(360000L);
//            System.out.println(resp);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
