package com.wisematch.modules.rtc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wisematch.common.oss.FileAcl;
import com.wisematch.common.oss.OssFacade;
import com.wisematch.modules.chat.config.AiChatConfig;
import com.wisematch.modules.chat.entity.AiViewRecord;
import com.wisematch.modules.chat.enums.PlatformType;
import com.wisematch.modules.chat.service.AiViewRecordService;
import com.wisematch.modules.common.service.AiSysConfigService;
import com.wisematch.modules.rtc.impl.RtcServiceImpl;
import com.wisematch.modules.rtc.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 房间记录
 * <AUTHOR>
 * @version InterviewRecordServiceImpl.java, v0.1 2025-07-13 22:49
 */
@Slf4j
@Component
public class RoomRecordFacade {

    @Autowired
    private AiChatConfig aiChatConfig;

    @Autowired
    private OssFacade ossFacade;

    @Autowired
    private AiViewRecordService aiViewRecordService;

    @Autowired
    private AiSysConfigService aiSysConfigService;

    /**
     * 启动云端录制
     * @param roomId
     * @return
     */
    public RecordResp startRecord(String userId, String roomId, String platform){
        RecordResp recordResp = new RecordResp();
        try {
            log.info("start video record.");
            JSONObject settings = aiSysConfigService.getInterviewConfig();
            Integer width = settings.getInteger("width");
            Integer height = settings.getInteger("height");
            //移动端切换竖屏
            if (!PlatformType.PC.name().equals(platform)) {
                width = settings.getInteger("height");
                height = settings.getInteger("width");
            }
            //注意：1.0.33版本之前的getInstance方法不是单例模式，内部存在监控http client的守护线程，多次重复调用可能会产生内存泄露
            long start = System.currentTimeMillis();
            RtcService rtcService = RtcServiceImpl.getInstance();
            rtcService.setAccessKey(aiChatConfig.getAccessKeyId());
            rtcService.setSecretKey(aiChatConfig.getSecretAccessKey());

            StartRecordRequest startRecordRequest = new StartRecordRequest();
            startRecordRequest.setAppId(aiChatConfig.getAppId());
            startRecordRequest.setBusinessId(roomId);
            startRecordRequest.setRoomId(roomId);
            startRecordRequest.setTaskId("video_record");
            startRecordRequest.setRecordMode(0);
            startRecordRequest.setEncode(startRecordRequest.new Encode(
                    width,
                    height,
                    15,
                    4000,
                    0,
                    4,
                    0,
                    0,
                    64,
                    48000,
                    2
            ));

            //自定义录制流和画布
            startRecordRequest.setTargetStreams(startRecordRequest.new TargetStreams(List.of(new JSONObject(Map.of("Index", 0, "UserId", userId)), new JSONObject(Map.of("index", 1, "userId", "HR")))));
            startRecordRequest.setLayout(startRecordRequest.new Layout(2, startRecordRequest.new CustomLayout(width,height)));

            log.info("targetStreams:{}", JSONObject.toJSONString(startRecordRequest.getTargetStreams()));
            log.info("layout:{}",JSONObject.toJSONString(startRecordRequest.getLayout()));

            startRecordRequest.setControl(startRecordRequest.new Control(
                    0,
                    0,
                    180,
                    0
            ));
            startRecordRequest.setFileFormatConfig(startRecordRequest.new FileFormatConfig(new String[]{"MP4"}));
            startRecordRequest.setStorageConfig(startRecordRequest.new StorageConfig(2, startRecordRequest.new CustomConfig()));

            StartRecordResponse startRecordResponse = rtcService.startRecord(startRecordRequest);

//            AiViewRecord aiViewRecord = aiViewRecordService.getByRoomId(roomId);
//            aiViewRecord.setBizResponse(JSON.toJSONString(startRecordResponse));
//            aiViewRecordService.updateById(aiViewRecord);
            log.info("start record roomId:{}, response:{}", roomId, JSON.toJSONString(startRecordResponse));
            if ("ok".equals(startRecordResponse.getResult())) {
                recordResp.setStatus(1);
                return recordResp;
            }

        } catch (Exception e) {
            log.info("start record error", e);
        }
        recordResp.setStatus(-1);
        return recordResp;
    }

    /**
     * 获取任务录制状态
     * @param roomId
     * @return
     */
    public boolean getRecordTask(String roomId) {
        RtcService rtcService = RtcServiceImpl.getInstance();

        rtcService.setAccessKey(aiChatConfig.getAccessKeyId());
        rtcService.setSecretKey(aiChatConfig.getSecretAccessKey());

        try {
            GetRecordTaskRequest getRecordTaskRequest = new GetRecordTaskRequest();
            getRecordTaskRequest.setAppId(aiChatConfig.getAppId());
            getRecordTaskRequest.setRoomId(roomId);
            getRecordTaskRequest.setTaskId("video_record");
            GetRecordTaskResponse getRecordTaskResponse = rtcService.getRecordTask(getRecordTaskRequest);
            log.info(JSON.toJSONString(getRecordTaskResponse));
        } catch (Exception e) {
            log.error("get record task error", e);
        }
        return true;
    }


}
