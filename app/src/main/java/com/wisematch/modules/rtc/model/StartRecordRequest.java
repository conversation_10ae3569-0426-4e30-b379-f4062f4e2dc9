package com.wisematch.modules.rtc.model;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class StartRecordRequest {
    @JSONField(name = "AppId")
    String appId;
    @JSONField(name = "BusinessId")
    String businessId;
    @JSONField(name = "RoomId")
    String RoomId;
    @JSONField(name = "TaskId")
    String TaskId;
    @JSONField(name = "RecordMode")
    int RecordMode;
    @J<PERSON>NField(name = "Encode")
    Encode Encode;
    @JSONField(name = "Layout")
    Layout Layout;
    @J<PERSON>NField(name = "Control")
    Control Control;
    @JSONField(name = "FileFormatConfig")
    FileFormatConfig FileFormatConfig;
    @JSONField(name = "FileNameConfig")
    FileNameConfig FileNameConfig = new FileNameConfig();
    @JSONField(name = "StorageConfig")
    StorageConfig StorageConfig;
    @J<PERSON>NField(name = "TargetStreams")
    TargetStreams TargetStreams;

    @Data
    @AllArgsConstructor
    public class TargetStreams{
        @JSONField(name = "StreamList")
        List<JSONObject> StreamList;
    }

    @Data
    @AllArgsConstructor
    public class Encode {
        @JSONField(name = "VideoWidth")
        int VideoWidth;
        @JSONField(name = "VideoHeight")
        int VideoHeight;
        @JSONField(name = "VideoFps")
        int VideoFps;
        @JSONField(name = "VideoBitrate")
        int VideoBitrate;
        @JSONField(name = "VideoCodec")
        int VideoCodec;
        @JSONField(name = "VideoGop")
        int VideoGop;
        @JSONField(name = "AudioCodec")
        int AudioCodec;
        @JSONField(name = "AudioProfile")
        int AudioProfile;
        @JSONField(name = "AudioBitrate")
        int AudioBitrate;
        @JSONField(name = "AudioSampleRate")
        int AudioSampleRate;
        @JSONField(name = "AudioChannels")
        int AudioChannels;
    }


    @Data
    @AllArgsConstructor
    public class Layout {
        @JSONField(name = "LayoutMode")
        int LayoutMode;

        @JSONField(name = "CustomLayout")
        CustomLayout CustomLayout;
    }

    @Data
    public class CustomLayout{

        public CustomLayout(Integer width, Integer height) {
            this.Canvas = new JSONObject(Map.of("Width", width, "Height", height));

            this.Regions = List.of(
                    new JSONObject(Map.of(
                            "StreamIndex", 0,
                            "LocationX", 0,
                            "LocationY", 0,
                            "WidthProportion",1.0,
                            "HeightProportion",1.0,
                            "Width", width,
                            "Height", height,
                            "ZOrder",100)
//                        "SourceCrop",new JSONObject(Map.of("LocationX", 0, "LocationY", 0,"WidthProportion",1,"HeightProportion",1)))
                    ),
                    new JSONObject(Map.of(
                            "StreamIndex", 1,
                            "LocationX", 0,
                            "LocationY", 0,
                            "WidthProportion",1.0,
                            "HeightProportion",1.0,
                            "Width", 1,
                            "Height", 1,
                            "ZOrder",0)
//                        "SourceCrop",new JSONObject(Map.of("LocationX", 0, "LocationY", 0,"WidthProportion",1,"HeightProportion",1)))
                    )
            );
        }

        @JSONField(name = "Canvas")
        JSONObject Canvas;

        @JSONField(name = "Regions")
        List<JSONObject> Regions;
    }

    @Data
    @AllArgsConstructor
    public class Control {
        @JSONField(name = "MediaType")
        int MediaType;
        @JSONField(name = "FrameInterpolationMode")
        int FrameInterpolationMode;
        @JSONField(name = "MaxIdleTime")
        int MaxIdleTime;
        @JSONField(name = "MaxRecordTime")
        int MaxRecordTime;
    }

    @Data
    @AllArgsConstructor
    public class FileFormatConfig {
        @JSONField(name = "FileFormat")
        String[] FileFormat;
    }

    @Data
    public class FileNameConfig {
        @JSONField(name = "Prefix")
        String[] Prefix = {"interview","user"};
        @JSONField(name = "Pattern")
        String Pattern = "{RoomId}";
    }

    @Data
    @AllArgsConstructor
    public class StorageConfig {
        @JSONField(name = "Type")
        int Type;

        @JSONField(name = "CustomConfig")
        CustomConfig customConfig;

    }

    @Data
    public class CustomConfig {
        @JSONField(name = "Vendor")
        int Vendor = 1;
        @JSONField(name = "Region")
        int Region = 0;
        @JSONField(name = "Bucket")
        String Bucket = "wise-match";
        @JSONField(name = "AccessKey")
        String AccessKey = "LTAI5tPcN5kZ8RFWswxRt5oF";
        @JSONField(name = "SecretKey")
        String SecretKey = "******************************";
    }

    @Data
    @AllArgsConstructor
    public class TosConfig {
        @JSONField(name = "AccountId")
        String AccountId;
        @JSONField(name = "Region")
        int Region;
        @JSONField(name = "Bucket")
        String Bucket;
    }
}

