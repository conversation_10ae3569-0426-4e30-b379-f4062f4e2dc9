package com.wisematch.modules.rtc;

import com.alibaba.fastjson.JSON;
import com.wisematch.modules.rtc.impl.RtcServiceImpl;
import com.wisematch.modules.rtc.model.StopRecordRequest;
import com.wisematch.modules.rtc.model.StopRecordResponse;

public class StopRecordDemo {
    public static void main(String[] args) {
        //注意：1.0.33版本之前的getInstance方法不是单例模式，内部存在监控http client的守护线程，多次重复调用可能会产生内存泄露
        RtcService rtcService = RtcServiceImpl.getInstance();

        rtcService.setAccessKey("ak");
        rtcService.setSecretKey("sk");

        try {
            StopRecordRequest stopRecordRequest = new StopRecordRequest();
            stopRecordRequest.setAppId("Your_AppId");
            stopRecordRequest.setBusinessId("Your_BusinessId");
            stopRecordRequest.setRoomId("Your_RoomId");
            stopRecordRequest.setTaskId("Your_TaskId");
            StopRecordResponse stopRecordResponse = rtcService.stopRecord(stopRecordRequest);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
