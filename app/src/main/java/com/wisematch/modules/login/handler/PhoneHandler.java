package com.wisematch.modules.login.handler;

import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.modules.chat.config.wsFlash.WsFlashUrlConfig;
import com.wisematch.modules.chat.model.WsFlashVO;
import com.wisematch.modules.chat.utils.JacksonOrderedUtil;
import com.wisematch.modules.common.utils.JsonUtils;
import com.wisematch.modules.chat.utils.PostUtil;
import com.wisematch.modules.sys.utils.WsFlashUtil;
import com.wisematch.modules.login.model.FlashAuthorizationDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.http.HttpResponse;
import java.util.Map;

@Service
public class PhoneHandler {
    private static final Logger log = LoggerFactory.getLogger(PhoneHandler.class);
    @Autowired
    WsFlashUrlConfig flashUrlConfig;
    public  String phoneParse(String appKey, String appId, String token) throws Exception {

        FlashAuthorizationDTO flashAuthorizationDTO = new FlashAuthorizationDTO();
        flashAuthorizationDTO.setAppId(appId);
        flashAuthorizationDTO.setToken(token);
        flashAuthorizationDTO.setSign(WsFlashUtil.sign(appKey, appId, token));

        Map<String, Object> orderedMap = JacksonOrderedUtil.beanToOrderedMap(flashAuthorizationDTO);
        HttpResponse<String> httpResponse = PostUtil.postUrlencoded(flashUrlConfig.getUrl(), orderedMap);

        return responseParse(httpResponse,appKey);
    }

    private  String responseParse(HttpResponse<String> httpResponse, String appKey) throws Exception {

        if (StringUtils.isBlank(httpResponse.body())){
            log.error("返回参数错误");
            throw new RRException(RRExceptionEnum.HTTP_ERROR);
        }
        if (httpResponse.statusCode() != 200){
            log.error("请求失败");
            throw new RRException(RRExceptionEnum.HTTP_ERROR);
        }
        if (httpResponse.statusCode() == 200){
            log.info("服务请求成功");
            WsFlashVO wsFlashVO = JsonUtils.fromJson(httpResponse.body(), WsFlashVO.class);
            if (null != wsFlashVO && "200000".equals(wsFlashVO.getCode())){
                log.error("token解析成功");
                String mobileName = wsFlashVO.getData().getMobileName();
                return WsFlashUtil.decrypt(mobileName, appKey);
            }else {
                throw new RRException(RRExceptionEnum.HTTP_ERROR);
            }
        }
        return httpResponse.body();
    }

}
