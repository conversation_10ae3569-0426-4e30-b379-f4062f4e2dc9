package com.wisematch.modules.login.handler;

import com.wisematch.common.event.LoginLogEvent;
import com.wisematch.common.utils.IPUtils;
import com.wisematch.common.utils.SpringContextUtils;
import com.wisematch.modules.security.auth.CustomUserDetailsUser;
import com.wisematch.modules.sys.entity.SysLoginLog;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Date;

/**
 * <AUTHOR>
 * @version CustomAuthenticationSuccessHandler.java, v0.1 2025-09-11 11:25
 */
@Slf4j
@Component
public class LoginAuthenticationSuccessHandler implements AuthenticationSuccessHandler {

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
        String userId = "";
        String userName = "";
        if (authentication.getPrincipal() instanceof CustomUserDetailsUser) {
            CustomUserDetailsUser userDetailsUser = (CustomUserDetailsUser) authentication.getPrincipal();
            userId = userDetailsUser.getUserId();
            userName = userDetailsUser.getUsername();
        }
        SysLoginLog loginLog = new SysLoginLog();
        loginLog.setUserId(userId);
        loginLog.setOptionIp(IPUtils.getIpAddr(request));
        loginLog.setOptionName("用户登录成功");
        loginLog.setOptionTerminal(request.getHeader("User-Agent"));
        loginLog.setUsername(userName);
        loginLog.setOptionTime(new Date());
        log.info("发送登录日志事件");
        SpringContextUtils.publishEvent(new LoginLogEvent(loginLog));
    }
}
