package com.wisematch.modules.login.service;

import com.wisematch.modules.chat.model.AppLoginBody;
import com.wisematch.modules.chat.model.LoginBody;
import com.wisematch.modules.chat.model.UserInfo;

/**
 * <AUTHOR>
 * @version LoginService.java, v0.1 2025-07-01 14:30
 */
public interface LoginService {

    Boolean sendCode(String phone, String ip);

    UserInfo getUserInfo(String phone);

    String userLogin(LoginBody loginBody);

    String onceClickLogin(AppLoginBody loginBody) throws Exception;
}
