package com.wisematch.modules.login.strategy;

import com.wisematch.modules.chat.config.wsFlash.IosWsFlashConfig;
import com.wisematch.modules.login.handler.PhoneHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("iosPhoneParseStrategy")
@RequiredArgsConstructor
public class IosPhoneParseStrategy implements PhoneParseStrategy {
    @Autowired
    IosWsFlashConfig config;

    @Autowired
    PhoneHandler phoneHandler;
    @Override
    public String parsePhone(String token) throws Exception {
        return phoneHandler.phoneParse(
                config.getIosAppKey(),
                config.getIosAppId(),
                token
        );
    }
}
