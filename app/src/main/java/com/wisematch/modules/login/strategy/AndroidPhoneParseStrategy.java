package com.wisematch.modules.login.strategy;

import com.wisematch.modules.chat.config.wsFlash.AndroidWsFlashConfig;
import com.wisematch.modules.login.handler.PhoneHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("androidPhoneParseStrategy")
@RequiredArgsConstructor // Lombok自动注入构造函数
public class AndroidPhoneParseStrategy implements PhoneParseStrategy {
    @Autowired
    AndroidWsFlashConfig config;
    @Autowired
    PhoneHandler phoneHandler;

    @Override
    public String parsePhone(String token) throws Exception {
        return phoneHandler.phoneParse(
                config.getAndroidAppKey(),
                config.getAndroidAppId(),
                token
        );
    }
}
