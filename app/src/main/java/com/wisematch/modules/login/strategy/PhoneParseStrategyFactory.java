package com.wisematch.modules.login.strategy;

import com.wisematch.modules.chat.enums.AppLoginType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@RequiredArgsConstructor
public class PhoneParseStrategyFactory {
    // Spring会自动将所有PhoneParseStrategy实现注入Map
    // Key为Bean名称（如iosPhoneParseStrategy）
    private final Map<String, PhoneParseStrategy> strategyMap;

    public PhoneParseStrategy getStrategy(AppLoginType channel) {
        String beanName = channel.name().toLowerCase() + "PhoneParseStrategy";
        PhoneParseStrategy strategy = strategyMap.get(beanName);
        if (strategy == null) {
            throw new IllegalArgumentException("Unsupported channel: " + channel);
        }
        return strategy;
    }
}
