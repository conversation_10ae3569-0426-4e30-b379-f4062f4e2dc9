package com.wisematch.modules.login.controller;

import com.wisematch.common.annotation.SysLog;
import com.wisematch.common.utils.IPUtils;
import com.wisematch.common.utils.R;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.model.AppLoginBody;
import com.wisematch.modules.chat.model.LoginBody;
import com.wisematch.modules.login.service.LoginService;
import com.wisematch.modules.sms.SmsVerifyCodeManager;
import com.wisematch.modules.sys.annotation.Anonymous;
import com.wisematch.modules.sys.constant.Constants;
import com.wisematch.modules.sys.service.SysUserService;
import com.wisematch.modules.sys.service.TokenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version AiChatController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/login")
@Tag(name = "Wiser端用户登录", description = "用户登录")
public class UserLoginController {
    @Autowired
    private LoginService loginService;

    @Autowired
    private SysUserService userService;
    @Autowired
    private SmsVerifyCodeManager smsVerifyCodeManager;
    @Autowired
    private TokenService tokenService;


    @Operation(method = "发送验证码", summary = "发送验证码")
    @GetMapping("/send/code")
    @Anonymous
    @SysLog("发送验证码")
    public R sendCode(@RequestParam("phone") String phone, HttpServletRequest request) {
        return R.ok().setData(loginService.sendCode(phone,IPUtils.getIpAddr(request)));
    }

    @Operation(method = "校验验证码", summary = "发送验证码")
    @GetMapping("/verify/code")
    @Anonymous
    @SysLog("校验验证码")
    @Deprecated
    public R verifyCode(@RequestParam("phone") String phone, @RequestParam("code") String code) {
        boolean flag = smsVerifyCodeManager.validateUserVerifyCode(phone, code);
        //临时
        this.userService.createUser(phone);
        return R.ok().setData(loginService.getUserInfo(phone));
    }

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    @SysLog("账号密码登录/手机验证码登录")
    @Operation(method = "账号密码登录/手机验证码登录", summary = "账号密码登录/手机验证码登录")
    @Anonymous
    public R login(@RequestBody LoginBody loginBody){
        return R.ok().put(Constants.TOKEN, loginService.userLogin(loginBody));
    }

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/onceClickLogin")
    @SysLog("app端一键登录")
    @Operation(method = "app端一键登录", summary = "app端一键登录")
    @Anonymous
    public R onceClickLogin(@RequestBody AppLoginBody loginBody) throws Exception {
        return R.ok().put(Constants.TOKEN, loginService.onceClickLogin(loginBody));
    }

    /**
     * 退出登录
     *
     * @return 结果
     */
    @GetMapping("/logout")
    @SysLog("退出登录")
    @Operation(method = "退出登录", summary = "退出登录")
    public R logout(){
        tokenService.delLoginUser(UserInfoUtils.getLoginUser().getToken());
        return R.ok();
    }

}
