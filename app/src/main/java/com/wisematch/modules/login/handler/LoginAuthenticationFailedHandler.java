package com.wisematch.modules.login.handler;

import com.wisematch.common.event.LoginLogEvent;
import com.wisematch.common.utils.IPUtils;
import com.wisematch.common.utils.SpringContextUtils;
import com.wisematch.modules.sys.entity.SysLoginLog;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version CustomAuthenticationSuccessHandler.java, v0.1 2025-09-11 11:25
 */
@Slf4j
@Component
public class LoginAuthenticationFailedHandler implements AuthenticationFailureHandler {

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) throws IOException, ServletException {
        final String username = request.getParameter("username");
        SysLoginLog loginLog = new SysLoginLog();
        loginLog.setOptionIp(IPUtils.getIpAddr(request));
        loginLog.setOptionName("用户登录失败");
        loginLog.setOptionTerminal(request.getHeader("User-Agent"));
        loginLog.setUsername(username);
        SpringContextUtils.publishEvent(new LoginLogEvent(loginLog));
    }
}
