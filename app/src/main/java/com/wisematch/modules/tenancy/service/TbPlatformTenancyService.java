package com.wisematch.modules.tenancy.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.tenancy.entity.TbPlatformTenancy;

/**
 * 渠道管理
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-29 14:48:31
 */
public interface TbPlatformTenancyService extends IService<TbPlatformTenancy> {

    IPage<TbPlatformTenancy> pageList(IPage<TbPlatformTenancy> tbPlatformTenancyIPage, QueryWrapper<TbPlatformTenancy> queryWrapper);

    void setDefault(TbPlatformTenancy tenancy);

    TbPlatformTenancy getDefaultTenancy();
}
