package com.wisematch.automatedTesting.cotroller;

import com.wisematch.automatedTesting.model.TestInterviewDTO;
import com.wisematch.automatedTesting.service.InterviewService;
import com.wisematch.common.utils.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version AiChatController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/test/interview")
@Tag(name = "面试测试", description = "面试测试")
public class TestInterviewController {

    @Autowired
    private InterviewService interviewService;

    @Operation(summary = "发送消息")
    @PostMapping("/send")
    public R send(@RequestBody TestInterviewDTO param) {
        interviewService.interview(param);
        return R.ok();
    }
}
