package com.wisematch.automatedTesting.service;

import com.wisematch.automatedTesting.model.TestInterviewDTO;
import com.wisematch.automatedTesting.utils.BotClient;
import com.wisematch.modules.chat.model.AiChatSetting;
import com.wisematch.modules.chat.model.AiChatUserMsg;
import com.wisematch.modules.chat.model.ApplyViewParams;
import com.wisematch.modules.chat.model.ShortMessage;
import com.wisematch.modules.chat.service.impl.AiChatViewFacade;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class InterviewService {

    private static final Logger log = LoggerFactory.getLogger(InterviewService.class);
    @Autowired
    AiChatViewFacade aiChatViewFacade;

    public void interview(TestInterviewDTO param){
        //加传参简历，如果有简历参数就删除并用最新简历。如果没有就用已有最新简历。
        ApplyViewParams applyViewParams = new ApplyViewParams();
        applyViewParams.setApplyId(param.getApplyId());
        applyViewParams.setApplyType(param.applyType);
        AiChatSetting aiChatSetting = aiChatViewFacade.applyStart(applyViewParams);
        String roomId = aiChatSetting.getRoomId();

        List<Map<String, String>> history = new ArrayList<>();

        // 构建火山消息
        history.add(Map.of(
                "role", "system",
                "content", "你好！欢迎参加WiseMatch的面试。我是AI面试官青麟。很高兴认识你！我们首先从自我介绍开始吧，请简单介绍一下自己的背景和经历。"
        ));

        for(int i = 0 ; i < 100 ; i ++){

            String userMsg = BotClient.callBot(history, param.getModel());

            history.add(Map.of(
                    "role", "user",
                    "content", userMsg
            ));

            //获取系统回复
            AiChatUserMsg aiChatUserMsg = new AiChatUserMsg();
            aiChatUserMsg.setChatId(roomId);
            aiChatUserMsg.setMsg(userMsg);
            ShortMessage shortMessage = aiChatViewFacade.sendMsg(aiChatUserMsg);
            String content = shortMessage.getContent();

            history.add(Map.of(
                    "role", "system",
                    "content", content
            ));
            log.info("对话记录："+history);
            if("面试结束，请点击关闭按钮退出".equals(content.trim())){
                log.info("=== 对话结束，收到结束信号 ===");
                break;
            }
        }

    }

}
