package com.wisematch.automatedTesting.utils;

import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

public class BotClient {

    private static final String API_URL = "https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions";
    private static final String API_KEY = "896ef53f-0b8f-46ab-9762-78b0cf4ce01b";

    public static String callBot(List<Map<String, String>> messages, String model) {
        RestTemplate restTemplate = new RestTemplate();

        Map<String, Object> body = Map.of(
                "model", model,
                "stream", false,
                "stream_options", Map.of("include_usage", true),
                "messages", messages
        );

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(API_KEY);

        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        ResponseEntity<Map> response = restTemplate.postForEntity(API_URL, requestEntity, Map.class);

        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
            Map<String, Object> respBody = response.getBody();
            List<Map<String, Object>> choices = (List<Map<String, Object>>) respBody.get("choices");
            if (choices != null && !choices.isEmpty()) {
                Map<String, Object> message = (Map<String, Object>) choices.get(0).get("message");
                if (message != null) {
                    return (String) message.get("content");
                }
            }
        }

        return null;
    }


}

