package com.wisematch.automatedTesting.model;

import com.wisematch.modules.chat.enums.ApplyType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class TestInterviewDTO {

    /**
     * 岗位ID
     */
    @Schema(description = "岗位ID")
    public String applyId = "1";

    /**
     * 申请类型，岗位面试-POSITION，模拟面试-TRAIN
     */
    @Schema(description = "申请类型，岗位面试-POSITION，模拟面试-TRAIN")
    public String applyType = ApplyType.TRAIN.name();

    @Schema(description = "所用模型")
    public String model = "bot-20250728132352-55hfh";

}
