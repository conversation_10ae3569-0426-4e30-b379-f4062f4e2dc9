package com.wisematch.config;

import com.google.gson.FieldNamingStrategy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Field;

@Configuration
public class GsonConfig {

    @Bean
    public Gson gson() {
        return new GsonBuilder()
                .setFieldNamingStrategy(new UnderlineNamingStrategy())
                .serializeNulls() // 可选：序列化null值
                .create();
    }

    private static class UnderlineNamingStrategy implements FieldNamingStrategy {
        @Override
        public String translateName(Field field) {
            String camelCaseName = field.getName();
            StringBuilder sb = new StringBuilder();

            for (int i = 0; i < camelCaseName.length(); i++) {
                char c = camelCaseName.charAt(i);
                // 遇到大写字母，在前面加下划线（首字母除外）
                if (Character.isUpperCase(c) && i > 0) {
                    sb.append('_');
                }
                // 转为小写字母
                sb.append(Character.toLowerCase(c));
            }

            return sb.toString();
        }
    }
}