package com.wisematch.config;

import brave.Tracer;
import brave.propagation.TraceContext;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

// 创建支持Micrometer Tracing的装饰器
public class TraceAwareThreadPoolExecutor extends ThreadPoolExecutor {
    private final Tracer tracer;

    public TraceAwareThreadPoolExecutor(ThreadPoolExecutor executor, Tracer tracer) {
        super(executor.getCorePoolSize(), executor.getMaximumPoolSize(),
                executor.getKeepAliveTime(TimeUnit.MILLISECONDS), TimeUnit.MILLISECONDS, executor.getQueue());
        this.tracer = tracer;
    }

    @Override
    public void execute(Runnable task) {
        super.execute(wrapWithTrace(task));
    }

    public Future<?> submit(Runnable task) {
        return super.submit(wrapWithTrace(task));
    }

    public <T> Future<T> submit(Callable<T> task) {
        return super.submit(wrapWithTrace(task));
    }

    public <T> Future<T> submit(Runnable task, T result) {
        return super.submit(wrapWithTrace(task), result);
    }

    private Runnable wrapWithTrace(Runnable task) {
        // 获取当前的trace context
        TraceContext.Builder builder = tracer.nextSpan().context().toBuilder();
        TraceContext traceContext = builder.build();

        return () -> {
            try (Tracer.SpanInScope ws = tracer.withSpanInScope(tracer.toSpan(traceContext))) {
                task.run();
            }
        };
    }

    private <T> Callable<T> wrapWithTrace(Callable<T> task) {
        TraceContext.Builder builder = tracer.nextSpan().context().toBuilder();
        TraceContext traceContext = builder.build();

        return () -> {
            try (Tracer.SpanInScope ws = tracer.withSpanInScope(tracer.toSpan(traceContext))) {
                return task.call();
            }
        };
    }

}