package com.wisematch.config.props;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ExecutorConfig {

    /**
     * 文本处理线程池，主线程池中调用，用同一个线程池可能会死锁
     */
    @Bean("textExecutor")
    public ThreadPoolExecutor textExecutor() {
        return new ThreadPoolExecutor(3, 5, 60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(10000),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
