package com.wisematch.config.client;

import com.alibaba.dashscope.embeddings.TextEmbedding;
import com.wisematch.config.props.AliyunEmbeddingProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
@EnableConfigurationProperties({AliyunEmbeddingProperties.class})
public class AliyunEmbeddingClientConfiguration {


    // 初始化客户端
    @Bean
    TextEmbedding textEmbedding() {
        return new TextEmbedding();
    }

}
