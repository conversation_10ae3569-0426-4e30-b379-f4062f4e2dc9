package com.wisematch.config.client;

import brave.Tracing;
import brave.spring.web.TracingClientHttpRequestInterceptor;
import com.wisematch.config.client.interceptor.TraceHttpInterceptor;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class TraceTransferConfig {

    @Resource
    private Tracing tracing;

    @Bean
    TracingClientHttpRequestInterceptor tracingInterceptor() {
        return (TracingClientHttpRequestInterceptor) TraceHttpInterceptor.tracingInterceptor(tracing);
    }
}
