package com.wisematch.config.client;

import com.wisematch.config.props.AliyunMilvusProperties;
import io.milvus.v2.client.ConnectConfig;
import io.milvus.v2.client.MilvusClientV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties({AliyunMilvusProperties.class})
@Slf4j
public class AliyunMilvusClientConfiguration {

    @Bean
    MilvusClientV2 milvusClient(AliyunMilvusProperties properties) {
        try {
            return new MilvusClientV2(ConnectConfig.builder()
                    .uri(properties.getEndpoint())
                    .username(properties.getUsername())
                    .password(properties.getPassword())
                    .build());
        }catch (Exception e){
            log.error("milvus连接异常", e);
        }
        return new MilvusClientV2(null);
    }
}
