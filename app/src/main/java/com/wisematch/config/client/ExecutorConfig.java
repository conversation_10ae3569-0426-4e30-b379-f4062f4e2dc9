package com.wisematch.config.client;

import brave.Tracer;
import jakarta.annotation.Resource;
import lombok.Data;
import org.springframework.context.annotation.Bean;

import java.util.concurrent.*;

@Data
public class ExecutorConfig {

    @Resource
    private Tracer tracer;

    @Bean
    ThreadPoolExecutor agentsHttpExecutor() {
        return new ThreadPoolExecutor(10, 10, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(5000), Executors.defaultThreadFactory());
    }
}
