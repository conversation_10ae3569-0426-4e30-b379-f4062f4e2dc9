package com.wisematch.config.client;

import brave.spring.web.TracingClientHttpRequestInterceptor;
import com.wisematch.modules.chat.service.AgentsReportService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.util.DefaultUriBuilderFactory;

import java.net.http.HttpClient;
import java.time.Duration;
import java.util.Collections;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class AgentsHttpClientServiceConfig {

    @Value("${agents-server.report.url}")
    private String reportUrl;

    @Resource
    private TracingClientHttpRequestInterceptor tracingInterceptor;

    @Resource
    private ThreadPoolExecutor agentsHttpExecutor;


    @Bean
    HttpClient httpClient() {
        HttpClient.Builder clientBuilder = HttpClient.newBuilder()
                //Python服务用的Uvicorn不支持http2
                .version(HttpClient.Version.HTTP_1_1)
                .executor(agentsHttpExecutor)
                .connectTimeout(Duration.ofSeconds(5));
        return clientBuilder.build();
    }

    @Bean
    AgentsReportService smsHttpService() {
        return AgentsHttpClientServiceFactory.createHttpService(AgentsReportService.class, reportUrl, httpClient(), DefaultUriBuilderFactory.EncodingMode.NONE, null, Duration.ofSeconds(300), Collections.singletonList(tracingInterceptor));
    }
}
