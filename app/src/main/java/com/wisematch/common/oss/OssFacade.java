package com.wisematch.common.oss;

import com.aliyun.oss.ClientConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.Credentials;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.oss.internal.OSSUtils;
import com.aliyun.oss.internal.SignUtils;
import com.aliyun.oss.internal.SignV2Utils;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.wisematch.modules.chat.config.OSSConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * <AUTHOR>
 * @version OssUtils.java, v0.1 2025-07-14 09:43
 */
@Component
public class OssFacade {

    @Autowired
    private OSSClient ossClient;

    @Autowired
    private OSSConfig ossConfig;

    /**
     * 转换为文件的OSS协议地址
     *
     * @param key 文件对象key
     * @return 文件OSS协议地址
     */
    public String toOssUrl(String key) {
        return "oss://" + ossConfig.getBucketName() + "/" + key;
    }

    private FileAccessOption buildAccessOption(FileAcl acl) {
        final FileAccessOption accessOption;
        if (acl == FileAcl.PUBLIC) {
            accessOption = FileAccessOption.of(FileAcl.PUBLIC);
        } else {
            // TODO: 2023/7/23 要针对每一种业务场景可配置，不能写死
            // 默认3小时有效期。当用户主动刷新网页或查询详情时，前端会发起请求获取到最新的访问链接。
            Instant expiredTime = Instant.now().plus(24, ChronoUnit.HOURS);
            accessOption = FileAccessOption.of(FileAcl.PRIVATE).setExpiredTime(expiredTime);
        }
        return accessOption;
    }

    /**
     * 获取url临时链接
     *
     * @param key
     * @return
     */
    public String getHttpUrl(String key, FileAcl acl) {
        OSSUtils.ensureBucketNameValid(ossConfig.getBucketName());
        OSSUtils.ensureObjectKeyValid(key);

        ClientConfiguration clientConfiguration = ossClient.getClientConfiguration();
        URI endpoint = OSSUtils.toEndpointURI(this.ossConfig.getEndpoint(), clientConfiguration.getProtocol().toString());

        final String url;
        FileAccessOption accessOption = this.buildAccessOption(acl);
        if (accessOption.getAcl() == null || accessOption.getAcl() == FileAcl.PRIVATE) {
            // 私有读文件
            // 默认3小时有效期。当用户主动刷新网页或查询详情时，前端会发起请求获取到最新的访问链接。
            Instant expiredTime = Instant.now().plus(1, ChronoUnit.HOURS);
            accessOption.setExpiredTime(expiredTime);
            // 获取访问链接（无远程访问，签名是纯本地计算的）
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(ossConfig.getBucketName(), key);
            request.setExpiration(Date.from(accessOption.getExpiredTime()));

            Credentials credentials = ossClient.getCredentialsProvider().getCredentials();
            if (clientConfiguration.getSignatureVersion() != null && clientConfiguration.getSignatureVersion() == SignVersion.V2) {
                url = SignV2Utils.buildSignedURL(request, credentials, clientConfiguration, endpoint);
            } else {
                url = SignUtils.buildSignedURL(request, credentials, clientConfiguration, endpoint);
            }
        } else {
            // 公有读文件
            // 获取访问链接（无远程访问，纯本地拼接）
            String bucketEndpoint = OSSUtils.determineFinalEndpoint(endpoint, ossConfig.getBucketName(), clientConfiguration).toString();
            String resourcePath = OSSUtils.determineResourcePath(ossConfig.getBucketName(), key, clientConfiguration.isSLDEnabled());
            String baseUrl;
            if (!bucketEndpoint.endsWith("/")) {
                baseUrl = bucketEndpoint + "/" + resourcePath;
            } else {
                baseUrl = bucketEndpoint + resourcePath;
            }
            url = baseUrl;

        }
        return url;
    }
}
