package com.wisematch.common.event;

import com.wisematch.common.utils.ThreadPoolUtil;
import com.wisematch.modules.sys.entity.SysLoginLog;
import com.wisematch.modules.sys.service.SysLoginLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

/**
 * @Description 登录日志监听器
 * @Date 11:28
 * <AUTHOR>
 **/
@Configuration
@RequiredArgsConstructor
@Slf4j
public class LoginLogListener {

    private final SysLoginLogService sysLoginLogService;

    @EventListener(LoginLogEvent.class)
    public void saveSysLog(LoginLogEvent event) {
        log.info("接收登录日志事件");
        ThreadPoolUtil.execute(() -> {
            SysLoginLog sysLoginLog = (SysLoginLog) event.getSource();
            sysLoginLogService.save(sysLoginLog);
        });
    }

}