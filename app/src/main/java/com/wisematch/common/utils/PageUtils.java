package com.wisematch.common.utils;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.common.model.PageRequest;

public class PageUtils {

    public static <T> Page<T> doPage(IService<T> service,
                                      PageRequest request,
                                      Wrapper<T> queryWrapper) {
        Page<T> page = new Page<>(request.getPageNum(), request.getPageSize());
        return service.page(page, queryWrapper);
    }

}