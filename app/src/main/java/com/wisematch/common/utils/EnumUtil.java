package com.wisematch.common.utils;

import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;

public class EnumUtil {
    /**
     * 根据枚举类和名字获取对应的枚举对象
     *
     * @param enumClass 枚举的 class
     * @param name      枚举的名字
     * @param <T>       枚举类型
     * @return          对应的枚举对象，如果没有匹配的返回 null
     */
    public static <T extends Enum<T>> T getEnumByName(Class<T> enumClass, String name) {
        if (enumClass == null || name == null) {
            return null;
        }
        try {
            return Enum.valueOf(enumClass, name);
        } catch (IllegalArgumentException e) {
            // 如果名字不存在，返回 null
            throw new RRException(RRExceptionEnum.ILLEGAL_ARGUMENT);
        }
    }
}
