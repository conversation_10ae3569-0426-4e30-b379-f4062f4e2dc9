package com.wisematch.common.utils;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 内存缓存
 */
@Component
@Slf4j
public class GuavaCacheUtil {


    private Cache<String, String> CACHE_MAP;

    private Cache<String, Object> CACHE_OBJ_MAP;

    private static final Integer TIME_TO_LIVE = 60 * 60 * 2;

    /**
     * 查询缓存
     * @param key
     * @return
     */
    public JSONObject getJSON(String key){
        if (TIME_TO_LIVE == 0) {
            return null;
        }
        if (CACHE_MAP != null && CACHE_MAP.getIfPresent(key) != null) {
            String jsonStr = CACHE_MAP.getIfPresent(key);
            return JSONUtil.parseObj(jsonStr);
        }
        return null;
    }

    /**
     * 放入缓存
     * @param key
     * @param object
     */
    public void putJSON(String key, JSONObject object) {
        if (CACHE_MAP == null) {
            CACHE_MAP = CacheBuilder.newBuilder()
                    // 设置缓存容量数
                    .expireAfterWrite(TIME_TO_LIVE, TimeUnit.SECONDS)
                    .build();
        }
        CACHE_MAP.put(key, object.toString());
    }

    /**
     * 放入缓存
     *
     * @param key
     * @param object
     */
    public void putObj(String key, Object object) {
        if (CACHE_OBJ_MAP == null) {
            CACHE_OBJ_MAP = CacheBuilder.newBuilder()
                    // 设置缓存容量数
                    .expireAfterWrite(TIME_TO_LIVE, TimeUnit.SECONDS)
                    .build();
        }
        CACHE_OBJ_MAP.put(key, object);
    }

    /**
     * 查询缓存
     * @param key
     * @return
     */
    public Object getObj(String key) {
        if (TIME_TO_LIVE == 0) {
            return null;
        }
        if (CACHE_OBJ_MAP != null && CACHE_OBJ_MAP.getIfPresent(key) != null) {
            return CACHE_OBJ_MAP.getIfPresent(key);
        }
        return null;
    }

    /**
     * 清理缓存
     */
    public void clear(){
        this.CACHE_OBJ_MAP.cleanUp();
        this.CACHE_MAP.cleanUp();
        log.info("缓存清理完成！");
    }

}
