package com.wisematch.common.utils;

import java.time.LocalDate;

public class UserUtils {

    /**
     * 计算年龄
     * @param birthStr 19891001
     * @return 年龄
     */
    public static Integer calculateAge(String birthStr) {
        if (birthStr == null || birthStr.length() != 8) {
            return null;
        }
        try {
            int birthYear = Integer.parseInt(birthStr.substring(0, 4));
            int birthMonth = Integer.parseInt(birthStr.substring(4, 6));
            int birthDay = Integer.parseInt(birthStr.substring(6, 8));

            LocalDate today = LocalDate.now();
            LocalDate birthday = LocalDate.of(birthYear, birthMonth, birthDay);

            int age = today.getYear() - birthday.getYear();
            if (today.getDayOfYear() < birthday.getDayOfYear()) {
                age--;
            }
            return age;
        } catch (Exception e) {
            return null;
        }
    }
}
