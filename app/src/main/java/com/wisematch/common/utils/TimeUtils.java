package com.wisematch.common.utils;

import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class TimeUtils {

    /**
     * 将日期字符串转换为时间戳
     * 支持格式: "2024.06", "2020.09" 等
     * @param dateStr 日期字符串
     * @return 时间戳(毫秒)
     */
    public static Long convertDateToTimestamp(String dateStr) {
        if (!StringUtils.hasLength(dateStr)) {
            return null;
        }

        try {
            // 处理 "YYYY.MM" 格式
            if (dateStr.matches("\\d{4}\\.\\d{2}")) {
                // 将格式从 "2024.06" 转换为 "2024-06"
                String formattedDate = dateStr.replace(".", "-");

                // 解析为年月，日期默认为该月的第一天
                LocalDate date = LocalDate.parse(formattedDate + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));

                // 转换为时间戳(毫秒)
                return date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            }

            // 如果是其他格式，可以在这里添加更多处理逻辑
            // 例如: "YYYY-MM", "YYYY/MM" 等

        } catch (DateTimeParseException e) {
            // 日期解析失败，记录日志或处理异常
            System.err.println("日期格式解析失败: " + dateStr);
        }

        return null;
    }
}
