package com.wisematch.common.utils;

import cn.hutool.core.util.StrUtil;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.modules.chat.enums.WiserConstant;
import com.wisematch.modules.sys.constant.Constants;
import com.wisematch.modules.sys.model.LoginUser;
import com.wisematch.modules.sys.model.PrivacyProtectionChangeDTO;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.util.PatternMatchUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;

/**
 * 安全服务工具类
 *
 * <AUTHOR>
 */
public class UserInfoUtils {


    private static final Logger log = LoggerFactory.getLogger(UserInfoUtils.class);

    public static void examCertificated(){
        Integer hasCertification = getLoginUser().getHasCertification();
        if(!WiserConstant.CERTIFICATED_SUCCESS.equals(hasCertification)){
            throw new RRException(RRExceptionEnum.NOT_CERTIFICATED);
        }
    }


    @Deprecated
    public static void examOrgId(){

        String orgId = UserInfoUtils.getLoginUser().getOrgId();

        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(orgId)) {
            throw new RRException(RRExceptionEnum.ORG_NOT_VERIFY);
        }
    }

    public static PrivacyProtectionChangeDTO geProtection(){
        return PrivacyProtectionChangeDTO.getByJson(
                getLoginUser().getPrivacyProtection());
    }


    /**
     * 用户ID
     **/
    public static String getCurrentUserId() {
        try {
            return getLoginUser().getUserId();
        } catch (Exception e) {
            throw new RRException(RRExceptionEnum.USER_LOGIN_AUTH);
        }
    }
//
//    /**
//     * 获取部门ID
//     **/
//    public static Long getDeptId()
//    {
//        try
//        {
//            return getLoginUser().getDeptId();
//        }
//        catch (Exception e)
//        {
//            throw new ServiceException("获取部门ID异常", HttpStatus.ADMIN_LOGIN_AUTH);
//        }
//    }

    /**
     * 获取用户账户
     **/
    public static String getUsername() {
        try {
            return getLoginUser().getUsername();
        } catch (Exception e) {
            throw new RRException(RRExceptionEnum.USER_LOGIN_AUTH);
        }
    }

    /**
     * 获取用户
     **/
    public static LoginUser getLoginUser() {
        try {
            return (LoginUser) getAuthentication().getPrincipal();
        } catch (Exception e) {
            throw new RRException(RRExceptionEnum.USER_LOGIN_AUTH);
        }
    }

    /**
     * 获取Authentication
     */
    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword     真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 是否为管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId) {
        return userId != null && 1L == userId;
    }


    @SneakyThrows
    public static Integer getUserTenancyId() {
        return getLoginUser() == null ? null : getLoginUser().getTenancyId();
    }

    public static boolean isAdmin(String userId) {
        return StrUtil.isNotEmpty(userId) && userId.equals(Constant.SUPER_ADMIN);
    }

    public static boolean isAdmin() {
        return getLoginUser() != null && getLoginUser().getUserId().equals(Constant.SUPER_ADMIN);
    }

    public static boolean isMiniAdmin() {
        return getLoginUser() != null && getLoginUser().getUserId().equals(Constant.SUPER_MINI_ADMIN);
    }

    public static boolean isMiniAdmin(String userId) {
        return StrUtil.isNotEmpty(userId) && userId.equals(Constant.SUPER_MINI_ADMIN);
    }

    /**
     * 判断是否包含角色
     *
     * @param roles 角色列表
     * @param role  角色
     * @return 用户是否具备某角色权限
     */
    public static boolean hasRole(Collection<String> roles, String role) {
        return roles.stream().filter(StringUtils::hasText)
                .anyMatch(x -> Constants.SUPER_ADMIN.equals(x) || PatternMatchUtils.simpleMatch(x, role));
    }

    /**
     * 判断是否包含权限
     *
     * @param authorities 权限列表
     * @param permission  权限字符串
     * @return 用户是否具备某权限
     */
    public static boolean hasPermi(Collection<String> authorities, String permission) {
        return authorities.stream().filter(StringUtils::hasText)
                .anyMatch(x -> Constants.ALL_PERMISSION.equals(x) || PatternMatchUtils.simpleMatch(x, permission));
    }

    /**
     * 验证用户是否具备某权限
     *
     * @param permission 权限字符串
     * @return 用户是否具备某权限
     */
    public static boolean hasPermi(String permission) {
        return hasPermi(getLoginUser().getPermissions(), permission);
    }

}
