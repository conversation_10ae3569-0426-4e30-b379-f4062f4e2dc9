package com.wisematch.common.utils;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.util.*;
import java.util.function.Function;

public class BeanCopyUtils {

    /**
     * 将 List<S> 转换为 List<T>，并复制属性值
     *
     * @param sourceList 源列表
     * @param targetClass 目标类的 Class 对象
     * @param <S> 源类型
     * @param <T> 目标类型
     * @return 新的目标类型列表
     */
    public static <S, T> List<T> copyList(List<S> sourceList, Class<T> targetClass) {
        if (sourceList == null || sourceList.isEmpty()) {
            return Collections.emptyList();
        }
        List<T> targetList = new ArrayList<>(sourceList.size());
        for (S source : sourceList) {
            try {
                T target = targetClass.getDeclaredConstructor().newInstance();
                BeanUtils.copyProperties(source, target);
                targetList.add(target);
            } catch (Exception e) {
                throw new RuntimeException("拷贝列表属性失败", e);
            }
        }
        return targetList;
    }


    // 新增方法，支持映射函数
    public static <S, T> List<T> copyList(List<S> sourceList, Function<S, T> mapper) {
        if (sourceList == null || sourceList.isEmpty()) {
            return Collections.emptyList();
        }
        List<T> targetList = new ArrayList<>(sourceList.size());
        for (S source : sourceList) {
            targetList.add(mapper.apply(source));
        }
        return targetList;
    }

    public static void copyProperties(Object source, Object target){
        BeanUtils.copyProperties(source, target);
    }

    /**
     * 复制属性，但忽略 null 和 "id"
     */
    public static void copyPropertiesIgnoreId(Object source, Object target) {
        BeanUtils.copyProperties(source, target, getIgnorePropertyNames(source));
    }

    /**
     * 获取需要忽略的属性名：null 值 + "id"
     */
    private static String[] getIgnorePropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> ignoreNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            String propName = pd.getName();
            Object srcValue = src.getPropertyValue(propName);

            // 忽略 null
            if (srcValue == null) {
                ignoreNames.add(propName);
            }

            // 忽略 id（无论是否有值）
            if ("id".equalsIgnoreCase(propName)) {
                ignoreNames.add(propName);
            }
        }
        return ignoreNames.toArray(new String[0]);
    }

    /**
     * 复制非空属性
     *
     * @param source 源对象
     * @param target 目标对象
     */
    public static void copyNonNullProperties(Object source, Object target) {
        BeanUtils.copyProperties(source, target, getNullPropertyNames(source));
    }

    /**
     * 获取对象中为 null 的属性名
     */
    private static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        return emptyNames.toArray(new String[0]);
    }


}

