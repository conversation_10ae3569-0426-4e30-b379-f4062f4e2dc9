package com.wisematch.common.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;

import java.util.Collections;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分页转换工具（支持传入目标类 + 自定义赋值方法）
 */
public final class PageConvertUtils {

    private PageConvertUtils() {}

    /**
     * 使用目标类 + 自定义赋值器 (BiConsumer<source, dest>)
     * targetClass 必须有无参构造器
     */
    public static <T, R> Page<R> convert(Page<T> sourcePage,
                                         Class<R> targetClass,
                                         BiConsumer<T, R> recordCustomizer) {
        if (sourcePage == null) {
            return new Page<>();
        }

        Page<R> targetPage = new Page<>();
        // 复制分页元数据，排除 records 字段（我们会单独设置）
        BeanUtils.copyProperties(sourcePage, targetPage, "records");

        List<T> srcRecords = sourcePage.getRecords() == null ? Collections.emptyList() : sourcePage.getRecords();

        List<R> destRecords = srcRecords.stream().map(src -> {
            R dest = newInstance(targetClass);
            // 默认字段复制
            BeanUtils.copyProperties(src, dest);
            // 自定义赋值（可为 null）
            if (recordCustomizer != null) {
                recordCustomizer.accept(src, dest);
            }
            return dest;
        }).collect(Collectors.toList());

        targetPage.setRecords(destRecords);

        // 重新计算 pages（当 size>0 时）
        long size = targetPage.getSize();
        if (size > 0) {
            long total = targetPage.getTotal();
            long pages = (total + size - 1) / size;
            targetPage.setPages(pages);
        }

        return targetPage;
    }

    /**
     * 使用 mapper (Function<T,R>) 的重载（推荐：当你自己构建 R，避免反射）
     */
    public static <T, R> Page<R> convert(Page<T> sourcePage,
                                         Function<T, R> mapper) {
        if (sourcePage == null) {
            return new Page<>();
        }

        Page<R> targetPage = new Page<>();
        BeanUtils.copyProperties(sourcePage, targetPage, "records");

        List<T> srcRecords = sourcePage.getRecords() == null ? Collections.emptyList() : sourcePage.getRecords();

        List<R> destRecords = srcRecords.stream()
                .map(mapper)
                .collect(Collectors.toList());

        targetPage.setRecords(destRecords);

        long size = targetPage.getSize();
        if (size > 0) {
            long total = targetPage.getTotal();
            long pages = (total + size - 1) / size;
            targetPage.setPages(pages);
        }

        return targetPage;
    }


    /**
     * 对分页数据 records 统一加工的工具方法
     *
     * @param page     原分页对象
     * @param consumer 对每条记录的加工逻辑
     * @param <T>      分页记录的类型
     * @return 处理后的分页对象
     */
    public static <T> Page<T> mapRecords(Page<T> page, Consumer<T> consumer) {
        List<T> records = page.getRecords();
        records.forEach(consumer);
        page.setRecords(records);
        return page;
    }



    // 简单的反射实例化（抛出运行时异常以便调用方能快速发现问题）
    private static <R> R newInstance(Class<R> clazz) {
        try {
            return clazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException("无法创建目标类实例: " + clazz.getName(), e);
        }
    }
}

