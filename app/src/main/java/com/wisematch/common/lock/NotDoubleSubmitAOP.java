package com.wisematch.common.lock;

import cn.hutool.core.text.CharSequenceUtil;
import com.wisematch.common.utils.Constant;
import com.wisematch.common.utils.HttpContextUtils;
import com.wisematch.common.utils.R;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 防重复提交注解的实现，使用AOP。
 */
@Slf4j
@Aspect
@Component
public class NotDoubleSubmitAOP {

    @Resource
    private RedissonLock redissonLock;

    @Pointcut("@annotation(com.wisematch.common.lock.NotDoubleSubmit)")
    public void doubleSubmit() {}

    @Around("doubleSubmit()")
    public Object interceptor(ProceedingJoinPoint pjp) throws Throwable {
        Method method = ((MethodSignature) pjp.getSignature()).getMethod();
        NotDoubleSubmit notDoubleSubmit = method.getAnnotation(NotDoubleSubmit.class);

        String token = HttpContextUtils.getHttpServletRequest().getHeader("Authorization");
        if (CharSequenceUtil.isEmpty(token)) {
            token = UUID.randomUUID().toString();
        }

        String lockKey = Constant.SYSTEM_NAME + generateKey(pjp, token);

        boolean locked = false;
        try {
            locked = redissonLock.lock(lockKey, notDoubleSubmit.delaySeconds(), TimeUnit.SECONDS);
            if (!locked) {
                return R.error("操作太频繁");
            }
            return pjp.proceed();
        } catch (Exception e) {
            log.error("重复提交拦截异常", e);
            throw e;
        } finally {
            // 确保锁释放
            if (locked) {
                try {
                    redissonLock.unlock(lockKey);
                } catch (Exception e) {
                    log.warn("释放锁失败: {}", lockKey, e);
                }
            }
        }
    }

    private String generateKey(ProceedingJoinPoint pjp, String token) {
        StringBuilder sb = new StringBuilder(token);
        sb.append(pjp.getTarget().getClass().getName())
                .append(((MethodSignature) pjp.getSignature()).getMethod().getName());

        for (Object arg : pjp.getArgs()) {
            sb.append(arg == null ? "null" : arg.toString());
        }

        return DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8));
    }
}
