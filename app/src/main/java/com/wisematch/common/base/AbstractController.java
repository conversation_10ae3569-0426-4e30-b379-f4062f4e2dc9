package com.wisematch.common.base;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.wisematch.common.utils.HttpContextUtils;
import com.wisematch.common.utils.MPPageConvert;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.sys.model.LoginUser;
import com.wisematch.modules.tenancy.entity.TbPlatformTenancy;
import com.wisematch.modules.tenancy.service.TbPlatformTenancyService;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;

import java.util.Map;

/**
 * Controller公共组件
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年11月9日 下午9:42:26
 */

public abstract class AbstractController {

    @Resource
    protected MPPageConvert mpPageConvert;

    @Resource
    public TbPlatformTenancyService tbPlatformTenancyService;

    protected LoginUser getUser() {
        return UserInfoUtils.getLoginUser();
    }

    @SneakyThrows
    protected String getUserId() {
        return UserInfoUtils.getCurrentUserId();
    }


    @SneakyThrows
    protected Integer getUserTenancyId() {
        return UserInfoUtils.getUserTenancyId();
    }

    public boolean isAdmin() {
        return UserInfoUtils.isAdmin() || UserInfoUtils.isMiniAdmin();
    }

    public boolean isAdmin(String userId) {
        return UserInfoUtils.isAdmin(userId) || UserInfoUtils.isMiniAdmin(userId);
    }

    /**
     * 优先从参数获取租户
     *
     * @param params
     * @return
     */
    public String getTenancyId(Map<String, Object> params) {
        String tenancyId = MapUtil.getStr(params, "tenancyId");
        if (StrUtil.isEmpty(tenancyId)) {
            tenancyId = HttpContextUtils.getHttpServletRequest().getHeader("tenancyId");
        }
        if (StrUtil.isEmpty(tenancyId)) {
            TbPlatformTenancy defaultTenancy = getDefaultTenancy();
            if (defaultTenancy != null) {
                tenancyId = defaultTenancy.getTenancyId().toString();
            }
        }
        return tenancyId;
    }

    /**
     * 优先从请求头获取租户
     *
     * @return
     */
    public String getTenancyId() {
        String tenancyId = HttpContextUtils.getHttpServletRequest().getHeader("tenancyId");
        if (StrUtil.isEmpty(tenancyId)) {
            TbPlatformTenancy defaultTenancy = getDefaultTenancy();
            if (defaultTenancy != null) {
                tenancyId = defaultTenancy.getTenancyId().toString();
            }
        }
        return tenancyId;
    }

    /**
     * 获取默认租户
     *
     * @return
     */
    public TbPlatformTenancy getDefaultTenancy() {
        return tbPlatformTenancyService.getDefaultTenancy();
    }
}
