package com.wisematch.common.aspect;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.wisematch.common.utils.HttpContextUtils;
import com.wisematch.common.utils.IPUtils;
import com.wisematch.modules.common.service.AiSysConfigService;
import com.wisematch.modules.sys.config.SyslogConfig;
import com.wisematch.modules.sys.entity.SysLog;
import com.wisematch.modules.sys.service.SysLogService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.SneakyThrows;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Date;

/**
 * 系统日志切面，兼容 Spring Security 匿名访问
 */
@Aspect
@Component
public class SysLogAspect {

    @Resource
    private SysLogService sysLogService;
    @Resource
    private AiSysConfigService aiSysConfigService;

    @Resource
    SyslogConfig syslogConfig;
    @Pointcut("@annotation(com.wisematch.common.annotation.SysLog)")
    public void logPointCut() {}

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
//        if (auth == null || !auth.isAuthenticated() || "anonymousUser".equals(auth.getPrincipal())) {
//            // 匿名用户直接执行方法，不记录日志
//            return point.proceed();
//        }

        long beginTime = System.currentTimeMillis();

        // 执行目标方法
        Object result = point.proceed();

        if(aiSysConfigService.isLogOpen()){
            // 执行时长
            long time = System.currentTimeMillis() - beginTime;
            // 保存日志
            saveSysLog(point, time);
        }

        return result;
    }

    @SneakyThrows
    private void saveSysLog(ProceedingJoinPoint joinPoint, long time) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        SysLog sysLog = new SysLog();

        // 注解描述
        com.wisematch.common.annotation.SysLog syslog = method.getAnnotation(com.wisematch.common.annotation.SysLog.class);
        if (syslog != null) {
            sysLog.setOperation(syslog.value());
        }

        // 请求方法
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = signature.getName();
        sysLog.setMethod(className + "." + methodName + "()");

        // 请求参数
        try {
            String params = JSONUtil.toJsonStr(joinPoint.getArgs());
            sysLog.setParams(params);
        } catch (Exception ignored) {}

        // 获取请求对象
        HttpServletRequest request = HttpContextUtils.getHttpServletRequest();
        if (request != null) {
            sysLog.setIp(IPUtils.getIpAddr(request));
            sysLog.setRequestUrl(request.getRequestURI());
        }

        // 获取用户名
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        String username = "anonymousUser";
        if (auth != null && auth.isAuthenticated() && auth.getPrincipal() != null && !"anonymousUser".equals(auth.getPrincipal())) {
            Object principal = auth.getPrincipal();
            if (principal instanceof org.springframework.security.core.userdetails.UserDetails userDetails) {
                username = userDetails.getUsername();
            } else if (principal instanceof String) {
                username = (String) principal;
            } else {
                try {
                    JSONObject userJson = JSONUtil.parseObj(principal);
                    String name = userJson.getStr("username");
                    if (StrUtil.isNotEmpty(name)) {
                        username = name;
                    }
                } catch (Exception ignored) {}
            }
        }
        sysLog.setUsername(username);

        // 其他日志信息
        sysLog.setTime(time);
        sysLog.setCreateDate(new Date());

        // 保存
        sysLogService.asyncSave(sysLog);
    }
}
