<?xml version="1.0" encoding="UTF-8"?>
<configuration>
<!--    <include resource="org/springframework/boot/logging/logback/base.xml" />-->
    <logger name="org.springframework.web" level="INFO"/>
<!--    <logger name="org.springboot.sample" level="TRACE" />-->
    <logger name="com.wisematch" level="INFO" />

<!--    &lt;!&ndash; 开发、测试环境 &ndash;&gt;-->
<!--    <springProfile name="dev,test">-->
<!--        <logger name="org.springframework.web" level="INFO"/>-->
<!--        <logger name="org.springboot.sample" level="INFO" />-->
<!--        <logger name="com.wisematch" level="DEBUG" />-->
<!--    </springProfile>-->

<!--    &lt;!&ndash; 生产环境 &ndash;&gt;-->
<!--    <springProfile name="pro">-->
<!--        <logger name="org.springframework.web" level="ERROR"/>-->
<!--        <logger name="org.springboot.sample" level="ERROR" />-->
<!--        <logger name="com.wisematch" level="ERROR" />-->
<!--    </springProfile>-->

    <!-- 定义日志文件的存储位置和名称 -->
    <property name="LOGS_PATH" value="./logs" />
    <!-- 控制台输出 -->
    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} - %5level [%thread] [%X{traceId}] [%X{spanId}] %logger{36} - %msg%n</pattern>        </encoder>
    </appender>
    <!-- 文件输出 -->
    <appender name="File" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOGS_PATH}/app-default.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} - %5level [%thread] [%X{traceId}] [%X{spanId}] %logger{36} - %msg%n</pattern>        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 每天产生一个新的日志文件 -->
            <fileNamePattern>${LOGS_PATH}/app-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>30</maxHistory> <!-- 保留30天的日志 -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize> <!-- 单个文件最大10MB -->
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>
    <!-- 日志级别控制 -->
    <root level="info" >
        <appender-ref ref="Console" />
        <appender-ref ref="File" />
    </root>
<!--    &lt;!&ndash; 针对特定包或类的日志级别设置 &ndash;&gt;-->
<!--    <logger name="org.springframework" level="info" additivity="false">-->
<!--        <appender-ref ref="Console" />-->
<!--        <appender-ref ref="File" />-->
<!--    </logger>-->
</configuration>