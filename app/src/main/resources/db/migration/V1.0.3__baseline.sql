CREATE TABLE `user_task_schedule` (
  `id` VARCHAR(128) NOT NULL COMMENT '主键ID' ,
  `room_id` VARCHAR(128) NOT NULL COMMENT '任务标识ID' ,
  `task_type` VARCHAR(64) NOT NULL COMMENT '调度类型' ,
  `max_schedule_count` INT NOT NULL DEFAULT 0  COMMENT '最大调度次数限制' ,
  `current_schedule_count` INT NOT NULL DEFAULT 0  COMMENT '当前已调度次数' ,
  `status` VARCHAR(32) NOT NULL COMMENT '任务状态: ENABLE-可调度, DISABLE-不可调度, PENDING-调度中' ,
  `last_schedule_time` TIMESTAMP NULL COMMENT '最后一次调度时间' ,
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间' ,
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间' ,
   PRIMARY KEY (`id`),
  CONSTRAINT `unq_room_id` UNIQUE (`room_id`)
)
ENGINE = InnoDB
COMMENT = '用户任务调度表';
CREATE INDEX `idx_type_status_create_count`
ON `user_task_schedule` (
  `task_type` ASC,
  `status` ASC,
  `create_time` ASC,
  `current_schedule_count` ASC,
  `max_schedule_count` ASC
);
