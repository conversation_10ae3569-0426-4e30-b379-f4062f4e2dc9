CREATE TABLE `ai_invitation` (
                                 `id` varchar(50) NOT NULL COMMENT '主键',
                                 `source_id` bigint DEFAULT NULL COMMENT '所属活动ID',
                                 `inviter_user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发布邀请的用户ID',
                                 `biz_sence` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '场景',
                                 `invite_type` varchar(20) DEFAULT NULL COMMENT '邀请类型(邀请码/短链/二维码标识)',
                                 `invite_code` varchar(200) DEFAULT NULL COMMENT '邀请唯一标识',
                                 `max_uses` int DEFAULT NULL COMMENT '最大使用次数，NULL表示无限制',
                                 `status` enum('ACTIVE','EXPIRED') DEFAULT NULL COMMENT '活动状态',
                                 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `expired_time` datetime DEFAULT NULL COMMENT '过期时间，可为空',
                                 `extra_info` longtext COMMENT '额外信息',
                                 `title` varchar(255) DEFAULT NULL,
                                 `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
                                 PRIMARY KEY (`id`),
                                 UNIQUE KEY `invite_code` (`invite_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='邀请信息表';

CREATE TABLE `ai_invitation_records` (
                                         `id` varchar(50) NOT NULL COMMENT '主键',
                                         `invitation_id` varchar(50) NOT NULL COMMENT '对应 invitations.id',
                                         `invitee_user_id` varchar(50) NOT NULL COMMENT '被邀请人用户ID',
                                         `biz_status` varchar(20) NOT NULL COMMENT '业务状态',
                                         `used_time` datetime DEFAULT NULL COMMENT '使用时间',
                                         `extra_info` longtext COMMENT '渠道、设备、活动阶段等额外信息',
                                         `invite_type` varchar(20) DEFAULT NULL COMMENT '邀请类型(邀请码/短链/二维码标识)',
                                         `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                                         `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='邀请记录表';


