# Tomcat
server:
  port: 8808
  connection-timeout: 30000
  tomcat:
    connection-timeout: 30000

spring:
  mvc:
    servlet:
      path: /ai
    async:
      request-timeout: 60000
  # 环境 dev|test|pro
  profiles:
    active: dev
  # jackson时间格式化
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      maxFileSize: 104857600 #100MB
      maxRequestSize: 104857600 #100MB
  data:
    redis:
      host: localhost
      port: 6379

swagger:
  title: WiseMatch API
  description: 青鳞栖云团队
  version: 1.0.0
  author: Jmatrix
  email: <EMAIL>

springdoc:
  api-docs:
    enabled: true # 开启OpenApi接口
    path: /v3/api-docs  # 自定义路径，默认为 "/v3/api-docs"
  swagger-ui:
    enabled: true # 开启swagger界面，依赖OpenApi，需要OpenApi同时开启
    path: /index.html # 自定义路径，默认为"/swagger-ui/index.html"


#七牛 AK  和  SK可以去密钥管理中查询
qiniu:
  accessKey: 2222
  secretKey: 33333
  bucketName: qh-file
  domain: https://www.qiniu.com
  prefix: task

# mybaits-plus配置
mybatis-plus:
  # MyBatis Mapper所对应的XML文件位置
  mapper-locations: classpath:/mapper/**/*Mapper.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    enable-sql-runner: true
    db-config:
      # 主键类型 0:数据库ID自增 1.未定义 2.用户输入 3 id_worker 4.uuid 5.id_worker字符串表示
      id-type: 0
      #字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断"
      field-strategy: 1
      # 默认数据库表下划线命名
      table-underline: true
