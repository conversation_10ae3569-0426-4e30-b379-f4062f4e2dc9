<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wisematch.modules.sys.mapper.SysRoleMapper">
	
	<!-- 查询用户创建的角色ID列表 -->
	<select id="queryRoleIdList" resultType="long">
		select role_id from sys_role where create_user_id = #{createUserId} 
	</select>

	<!-- 查询用户的所有菜单ID -->
	<select id="queryAllMenuId" resultType="long">
		SELECT DISTINCT
			rm.menu_id
		FROM
			sys_role_menu rm
				LEFT JOIN sys_user_role ur ON ur.role_id = rm.role_id
		where ur.user_id = #{userId}
	</select>
</mapper>