<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wisematch.modules.sys.mapper.SysUserMapper">

	<!-- 可根据自己的需求，是否要使用 -->
	<resultMap type="com.wisematch.modules.sys.entity.SysUser" id="userMap">
		<result property="userId" column="user_id"/>
		<result property="username" column="username"/>
		<result property="mobile" column="mobile"/>
		<result property="password" column="password"/>
		<result property="createTime" column="create_time"/>
	</resultMap>

	<!-- 查询用户的所有权限 -->
	<select id="queryAllPerms" resultType="string">
		select m.perms from sys_user_role ur 
			LEFT JOIN sys_role_menu rm on ur.role_id = rm.role_id 
			LEFT JOIN sys_menu m on rm.menu_id = m.menu_id 
		where ur.user_id = #{userId}
	</select>
</mapper>