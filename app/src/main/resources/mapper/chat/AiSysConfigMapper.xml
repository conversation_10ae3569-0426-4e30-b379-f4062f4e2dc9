<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wisematch.modules.common.mapper.AiSysConfigMapper">

    <resultMap type="com.wisematch.modules.common.entity.AiSysConfig" id="SysConfigResult">
        <id     property="configId"      column="config_id"      />
        <result property="configName"    column="config_name"    />
        <result property="configKey"     column="config_key"     />
        <result property="configValue"   column="config_value"   />
        <result property="configType"    column="config_type"    />
        <result property="createBy"      column="create_by"      />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"      column="update_by"      />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectConfigVo">
        select config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark
        from ai_sys_config
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlwhereSearch">
        <where>
            <if test="configId !=null">
                and config_id = #{configId}
            </if>
            <if test="configKey !=null and configKey != ''">
                and config_key = #{configKey}
            </if>
        </where>
    </sql>

    <select id="selectConfig" parameterType="com.wisematch.modules.common.entity.AiSysConfig" resultMap="SysConfigResult">
        <include refid="selectConfigVo"/>
        <include refid="sqlwhereSearch"/>
    </select>

</mapper>