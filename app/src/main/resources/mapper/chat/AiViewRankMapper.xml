<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wisematch.modules.chat.mapper.AiViewRankMapper">
    <resultMap id="AiViewRankWithRankResultMap" type="com.wisematch.modules.chat.model.AiViewRankWithRankVO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="position" property="position"/>
        <result column="score" property="score"/>
        <result column="update_time" property="updateTime"/>
        <result column="name" property="name"/>
        <result column="city" property="city"/>
        <result column="room_id" property="roomId"/>
        <result column="photo" property="photo"/>
<!--        <result column="user_rank" property="userRank"/>-->
    </resultMap>

    <select id="selectRankedByCityPosition" resultMap="AiViewRankWithRankResultMap" parameterType="map">
        <include refid="selectRankWithFilter"/> LIMIT 1000
    </select>

    <select id="selectUserRankByCityPositionUserId" resultMap="AiViewRankWithRankResultMap" parameterType="map">
        <include refid="selectRankWithFilter"/> AND t.userRank IS NOT NULL  order by t.userRank ASC
        LIMIT 1
    </select>

    <select id="selectRanksByUserId" resultMap="AiViewRankWithRankResultMap" parameterType="map">
        <include refid="selectRankWithFilter"/>
    </select>


    <sql id="selectRankWithFilter">
        WITH user_top_score AS (
        SELECT id, user_id, position, city, score, update_time, name, photo,
        ROW_NUMBER() OVER (
        PARTITION BY user_id, position
        ORDER BY score DESC, update_time ASC
        ) AS rn
        FROM ai_view_rank
        <where>
            <if test="dto.userId != null and dto.userId != ''">
                AND user_id = #{dto.userId}
            </if>
            <if test="dto.city != null and dto.city != ''">
                AND city = #{dto.city}
            </if>
            <if test="dto.position != null and dto.position != ''">
                AND position = #{dto.position}
            </if>
        </where>
        ),
        ranked_user_top AS (
        SELECT id, user_id, position, city, score, update_time, name, photo,
        RANK() OVER (
        PARTITION BY position, city
        ORDER BY score DESC, update_time ASC
        ) AS userRank
        FROM user_top_score
        WHERE rn = 1
        )
        SELECT
        r.id, r.user_id, r.position, r.city, r.score,
        r.update_time, r.name, r.photo, t.userRank
        FROM ai_view_rank r
        LEFT JOIN ranked_user_top t
        ON r.id = t.id
        <where>
            <if test="dto.userId != null and dto.userId != ''">
                AND r.user_id = #{dto.userId}
            </if>
            <if test="dto.city != null and dto.city != ''">
                AND r.city = #{dto.city}
            </if>
            <if test="dto.position != null and dto.position != ''">
                AND r.position = #{dto.position}
            </if>
        </where>
    </sql>


</mapper>