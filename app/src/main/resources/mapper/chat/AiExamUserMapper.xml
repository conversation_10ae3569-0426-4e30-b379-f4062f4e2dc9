<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wisematch.modules.exam.mapper.AiExamUserMapper">

    <resultMap type="com.wisematch.modules.exam.entity.AiExamUser" id="AiExamUserMapper">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="time" column="time"/>
        <result property="answerStatus" column="answer_status"/>
        <result property="userId" column="user_id"/>
        <result property="reportContent" column="report_content"/>
        <result property="createTime" column="create_time"/>
        <result property="bankId" column="bank_id"/>
        <result property="questionNum" column="question_num"/>
        <result property="summary" column="summary"/>
        <result property="labels" column="labels"/>
        <result property="examType" column="exam_type"/>
    </resultMap>
    <select id="selectLatestPerUserBank" resultMap="AiExamUserMapper">
        SELECT id,
        name,
        `time`,
        answer_status,
        user_id,
        report_content,
        create_time,
        bank_id,
        question_num,
        logo,
        summary,
        labels,
        exam_type
        FROM (
        SELECT t.id,
        t.name,
        t.time,
        t.logo,
        t.answer_status,
        t.user_id,
        t.report_content,
        t.create_time,
        t.bank_id,
        t.question_num,
        t.summary,
        t.labels,
        t.exam_type,
        ROW_NUMBER() OVER(PARTITION BY user_id, bank_id ORDER BY create_time DESC) AS rn
        FROM ai_exam_user t
        <where>
            <if test="userId != null and userId != ''">
                t.user_id = #{userId}
            </if>
        </where>
        ) x
        WHERE x.rn = 1
        ORDER BY x.create_time DESC
    </select>

</mapper>