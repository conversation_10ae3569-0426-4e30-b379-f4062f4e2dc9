<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wisematch.modules.chat.mapper.AiViewRecordMapper">
    <!-- 复合结果映射 -->
    <resultMap id="InterviewWithMessagesResultMap" type="com.wisematch.modules.chat.model.InterviewReport">
        <!-- 面试信息映射 -->
        <id property="interviewId" column="interview_id"/>
        <result property="roomId" column="room_id"/>
        <result property="userId" column="user_id"/>

        <result property="chatType" column="chat_type"/>
        <result property="chatName" column="chat_name"/>
        <result property="positionId" column="position_id"/>
        <result property="trainId" column="train_id"/>


        <result property="resume" column="resume"/>
        <result property="videoUrl" column="chat_url"/>
        <result property="startTime" column="start_time"/>
        <result property="duration" column="duration"/>
        <result property="chatReport" column="chat_report"/>

        <!-- 聊天记录集合 -->
        <collection property="messages" ofType="com.wisematch.modules.chat.model.ChatInterviewViewMessage">
<!--            <id property="id" column="id"/>-->
            <result property="chatId" column="conversation_id"/>
            <result property="content" column="content"/>
            <result property="role" column="type"/>
            <result property="dateTime" column="timestamp"/>
        </collection>



    </resultMap>


    <select id="report" resultMap="InterviewWithMessagesResultMap">
        <!-- 分页查询面试及关联的聊天记录 -->
        SELECT
        interview.id AS interview_id,
        interview.room_id,
        interview.chat_id,
        interview.user_id,
        interview.agent_id,
        interview.agent_name,
        interview.chat_type,
        interview.chat_name,
        interview.position_id,
        interview.train_id,
        interview.demand,
        interview.questions,
        interview.status,
        interview.resume,
        interview.chat_url,
        interview.chat_report,
        interview.chat_score,
        interview.start_time,
        interview.end_time,
        interview.duration,
        interview.pool_status,
        interview.create_time,
        interview.update_time,
        m.conversation_id,
        m.content,
        m.type,
        m.timestamp
        FROM
        ai_chat_interview interview
        left outer join
        ai_chat_memory m ON interview.chat_id = m.conversation_id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

</mapper>