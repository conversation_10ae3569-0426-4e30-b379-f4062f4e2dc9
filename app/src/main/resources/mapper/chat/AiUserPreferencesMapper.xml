<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wisematch.modules.chat.mapper.AiUserPreferencesMapper">

    <update id="batchLogicDelete">
        <foreach collection="list" item="item" separator=";">
            UPDATE ai_user_preferences
            SET is_del=0
            WHERE id=#{item.id}
        </foreach>
    </update>

    <select id="selectPreferenceCandidate" resultType="com.wisematch.modules.chat.entity.AiUserPreferences">
        SELECT aup.*
        FROM ai_user_preferences aup
        RIGHT JOIN ai_view_portrait avp ON aup.target_id = avp.id
        <where>
            <!-- 固定筛选条件 -->
            AND aup.user_id = #{userId}
            AND aup.biz_sence = 'TALENT'
            AND aup.collect = 1
            AND avp.verify_status = 1

            <!-- 职位ID条件 -->
            <if test="positionId != null and positionId != ''">
                AND avp.position_id = #{positionId}
            </if>

            <!-- 搜索条件处理 -->
            <if test="splitSearch != null and splitSearch.size() > 0">
                AND (
                <foreach collection="splitSearch" item="keyword" separator=" OR ">
                    avp.portrait LIKE CONCAT('%', #{keyword}, '%')
                </foreach>
                )
            </if>
        </where>
    </select>
</mapper>