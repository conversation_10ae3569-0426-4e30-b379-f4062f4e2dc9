version: "3.4"
services:
  wise-match-ai:
    image: crpi-o6u11ymyucz0k15d.cn-hangzhou.personal.cr.aliyuncs.com/wisematch/wise-match-ai:prod
    container_name: wise-match-ai
    environment:
      - spring.profiles.active=pro
    networks:
      - backend
    ports:
      - "8808:8808"
      - "9999:9999"
    volumes:
      # 文件所属者为：1001:1001
      - /home/<USER>/logs:/app/logs/
    healthcheck:
      test: [ "CMD-SHELL", "curl -f http://localhost:8808/ai/actuator/health || exit 1" ]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

networks:
  backend:
    external: true
