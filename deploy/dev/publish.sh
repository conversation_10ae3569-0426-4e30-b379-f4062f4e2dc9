#!/bin/bash
set -eu

rm -rf ./app.jar

scp ../../app/target/app-1.0.0.jar ./app.jar

# 构建最新镜像
docker build -t wise-match-ai:latest --no-cache .

# 登录远程镜像仓库（只需要执行1次就行）
# 镜像仓库地址：https://cr.console.aliyun.com/cn-hangzhou/instance/repositories
# $ docker login --username=puteedu2024 crpi-o6u11ymyucz0k15d.cn-hangzhou.personal.cr.aliyuncs.com   Wiaematch@888


# 发布最新镜像
docker tag wise-match-ai:latest crpi-o6u11ymyucz0k15d.cn-hangzhou.personal.cr.aliyuncs.com/wisematch/wise-match-ai:latest
docker push crpi-o6u11ymyucz0k15d.cn-hangzhou.personal.cr.aliyuncs.com/wisematch/wise-match-ai:latest

# 清理无 tag 的镜像
docker image prune -f

#拉取镜像，重启docker
#docker-compose down
#
#docker-compose pull
#
#docker-compose up
