# 运行阶段
FROM registry.cn-hangzhou.aliyuncs.com/jast-docker/openjdk:17-slim

# 设置工作目录
WORKDIR /app

# 从构建阶段复制构建好的jar
COPY ./app.jar ./app.jar

# 创建日志目录
RUN mkdir -p /app/logs

# 更新软件源并安装必要工具
# 更新软件源（同时替换普通源和安全源）并安装curl
RUN sed -i \
    -e 's|http://deb.debian.org|http://mirrors.aliyun.com|g' \
    -e 's|http://security.debian.org/debian-security|http://mirrors.aliyun.com/debian-security|g' \
    /etc/apt/sources.list && \
    apt-get update --allow-unauthenticated && \
    apt-get install -y --allow-unauthenticated curl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 暴露应用端口
EXPOSE 8808

# 设置时区
ENV TZ=Asia/Shanghai

# 设置 JAVA_OPTS 环境变量
ENV JAVA_OPTS="\
    -Xms1024m \
    -Xmx2048m \
    -Xss512k \
    -XX:+UseContainerSupport \
    -XX:MaxRAMPercentage=75.0 \
    -XX:+DisableExplicitGC \
    -Djava.security.egd=file:/dev/./urandom \
    -Xlog:async \
    -Xlog:gc*=info:file=/app/logs/gc.log:time,uptime,pid,level,tags:filecount=5,filesize=10M \
    -Xlog:jit+compilation=info:file=/app/logs/jit_compile.log:time,level,tags:filecount=5,filesize=10M \
    -Xlog:safepoint=info:file=/app/logs/safepoint.log:time,level,tags:filecount=5,filesize=10M \
    -XX:+HeapDumpOnOutOfMemoryError \
    -XX:HeapDumpPath=/app/logs/heapdump.hprof"

# 启动命令
CMD ["sh", "-c", "java $JAVA_OPTS -jar /app/app.jar --spring.profiles.active=test"]